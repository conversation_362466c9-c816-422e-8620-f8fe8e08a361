"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[4605],{

/***/ 54605:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": () => (/* binding */ enhanced_TestingTools)
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(64467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(10467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(5544);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js
var taggedTemplateLiteral = __webpack_require__(57528);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/regenerator/index.js
var regenerator = __webpack_require__(54756);
var regenerator_default = /*#__PURE__*/__webpack_require__.n(regenerator);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
// EXTERNAL MODULE: ./node_modules/antd/es/index.js + 112 modules
var es = __webpack_require__(33966);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/index.js + 1565 modules
var icons_es = __webpack_require__(36031);
// EXTERNAL MODULE: ./node_modules/styled-components/dist/styled-components.browser.esm.js + 12 modules
var styled_components_browser_esm = __webpack_require__(71606);
;// ./src/utils/testUtils.js


function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }

/**
 * Test Utilities
 * 
 * This module provides utilities for testing the application.
 */



/**
 * Test the WebSocket connection
 * @param {string} url - WebSocket URL to test
 * @returns {Promise<Object>} Test results
 */
var testWebSocketConnection = /*#__PURE__*/function () {
  var _ref = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee(url) {
    return regenerator_default().wrap(function (_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          return _context.abrupt("return", new Promise(function (resolve) {
            try {
              var startTime = performance.now();
              var ws = new WebSocket(url);
              var timeout = setTimeout(function () {
                if (ws.readyState !== WebSocket.OPEN) {
                  ws.close();
                  resolve({
                    success: false,
                    error: 'Connection timeout',
                    time: performance.now() - startTime
                  });
                }
              }, 5000);
              ws.onopen = function () {
                clearTimeout(timeout);
                var connectionTime = performance.now() - startTime;

                // Send a ping message
                ws.send(JSON.stringify({
                  type: 'ping'
                }));

                // Wait for a response or timeout
                var responseTimeout = setTimeout(function () {
                  ws.close();
                  resolve({
                    success: true,
                    error: null,
                    connectionTime: connectionTime,
                    responseTime: null,
                    message: 'Connected but no response to ping'
                  });
                }, 2000);
                ws.onmessage = function (event) {
                  clearTimeout(responseTimeout);
                  var responseTime = performance.now() - startTime;
                  ws.close();
                  resolve({
                    success: true,
                    error: null,
                    connectionTime: connectionTime,
                    responseTime: responseTime,
                    message: 'Connection successful'
                  });
                };
                ws.onerror = function (error) {
                  clearTimeout(responseTimeout);
                  ws.close();
                  resolve({
                    success: false,
                    error: 'Error after connection: ' + error.message,
                    connectionTime: connectionTime,
                    responseTime: null
                  });
                };
              };
              ws.onerror = function (error) {
                clearTimeout(timeout);
                ws.close();
                resolve({
                  success: false,
                  error: 'Connection error: ' + error.message,
                  time: performance.now() - startTime
                });
              };
              ws.onclose = function (event) {
                clearTimeout(timeout);
                if (event.code !== 1000 && event.code !== 1001) {
                  resolve({
                    success: false,
                    error: "Connection closed with code ".concat(event.code, ": ").concat(event.reason),
                    time: performance.now() - startTime
                  });
                }
              };
            } catch (error) {
              resolve({
                success: false,
                error: 'Exception: ' + error.message,
                time: 0
              });
            }
          }));
        case 1:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return function testWebSocketConnection(_x) {
    return _ref.apply(this, arguments);
  };
}();

/**
 * Test the API connection
 * @param {string} url - API URL to test
 * @returns {Promise<Object>} Test results
 */
var testApiConnection = /*#__PURE__*/function () {
  var _ref2 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee2(url) {
    var startTime, response, time, data, _t, _t2;
    return regenerator_default().wrap(function (_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.prev = 0;
          startTime = performance.now();
          _context2.next = 1;
          return fetch(url, {
            method: 'GET',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json'
            },
            timeout: 5000
          });
        case 1:
          response = _context2.sent;
          time = performance.now() - startTime;
          if (!response.ok) {
            _context2.next = 7;
            break;
          }
          _context2.prev = 2;
          _context2.next = 3;
          return response.json();
        case 3:
          data = _context2.sent;
          _context2.next = 6;
          break;
        case 4:
          _context2.prev = 4;
          _t = _context2["catch"](2);
          _context2.next = 5;
          return response.text();
        case 5:
          data = _context2.sent;
        case 6:
          return _context2.abrupt("return", {
            success: true,
            status: response.status,
            statusText: response.statusText,
            time: time,
            data: data
          });
        case 7:
          return _context2.abrupt("return", {
            success: false,
            status: response.status,
            statusText: response.statusText,
            time: time,
            error: "HTTP error: ".concat(response.status, " ").concat(response.statusText)
          });
        case 8:
          _context2.next = 10;
          break;
        case 9:
          _context2.prev = 9;
          _t2 = _context2["catch"](0);
          return _context2.abrupt("return", {
            success: false,
            error: 'Exception: ' + _t2.message,
            time: 0
          });
        case 10:
        case "end":
          return _context2.stop();
      }
    }, _callee2, null, [[0, 9], [2, 4]]);
  }));
  return function testApiConnection(_x2) {
    return _ref2.apply(this, arguments);
  };
}();

/**
 * Test browser performance
 * @returns {Object} Performance metrics
 */
var testBrowserPerformance = function testBrowserPerformance() {
  try {
    // Get performance metrics
    var metrics = {};

    // Navigation timing
    if (window.performance && window.performance.timing) {
      var timing = window.performance.timing;
      metrics.pageLoad = timing.loadEventEnd - timing.navigationStart;
      metrics.domReady = timing.domComplete - timing.domLoading;
      metrics.networkLatency = timing.responseEnd - timing.fetchStart;
      metrics.processingTime = timing.domComplete - timing.responseEnd;
      metrics.backendTime = timing.responseStart - timing.navigationStart;
      metrics.frontendTime = timing.loadEventEnd - timing.responseStart;
    }

    // Memory info (Chrome only)
    if (window.performance && window.performance.memory) {
      metrics.memory = {
        jsHeapSizeLimit: window.performance.memory.jsHeapSizeLimit,
        totalJSHeapSize: window.performance.memory.totalJSHeapSize,
        usedJSHeapSize: window.performance.memory.usedJSHeapSize
      };
    }

    // Frame rate (if available)
    if (window.requestAnimationFrame) {
      metrics.frameRate = {
        current: 0,
        average: 0
      };
      var frameCount = 0;
      var lastTime = performance.now();
      var _countFrames = function countFrames(time) {
        frameCount++;
        var elapsed = time - lastTime;
        if (elapsed >= 1000) {
          metrics.frameRate.current = Math.round(frameCount * 1000 / elapsed);
          if (!metrics.frameRate.average) {
            metrics.frameRate.average = metrics.frameRate.current;
          } else {
            metrics.frameRate.average = Math.round((metrics.frameRate.average + metrics.frameRate.current) / 2);
          }
          frameCount = 0;
          lastTime = time;
        }
        window.requestAnimationFrame(_countFrames);
      };
      window.requestAnimationFrame(_countFrames);
    }
    return {
      success: true,
      metrics: metrics
    };
  } catch (error) {
    return {
      success: false,
      error: 'Exception: ' + error.message
    };
  }
};

/**
 * Run all tests
 * @param {Object} options - Test options
 * @returns {Promise<Object>} Test results
 */
var runAllTests = /*#__PURE__*/function () {
  var _ref3 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee3() {
    var options,
      results,
      _args3 = arguments,
      _t3;
    return regenerator_default().wrap(function (_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          options = _args3.length > 0 && _args3[0] !== undefined ? _args3[0] : {};
          results = {
            websocket: null,
            api: null,
            performance: null
          };
          _context3.prev = 1;
          if (!options.websocketUrl) {
            _context3.next = 3;
            break;
          }
          es/* message */.iU.info('Testing WebSocket connection...');
          _context3.next = 2;
          return testWebSocketConnection(options.websocketUrl);
        case 2:
          results.websocket = _context3.sent;
          if (results.websocket.success) {
            es/* message */.iU.success("WebSocket connection successful (".concat(Math.round(results.websocket.connectionTime), "ms)"));
          } else {
            es/* message */.iU.error("WebSocket connection failed: ".concat(results.websocket.error));
          }
        case 3:
          if (!options.apiUrl) {
            _context3.next = 5;
            break;
          }
          es/* message */.iU.info('Testing API connection...');
          _context3.next = 4;
          return testApiConnection(options.apiUrl);
        case 4:
          results.api = _context3.sent;
          if (results.api.success) {
            es/* message */.iU.success("API connection successful (".concat(Math.round(results.api.time), "ms)"));
          } else {
            es/* message */.iU.error("API connection failed: ".concat(results.api.error));
          }
        case 5:
          // Test browser performance
          if (options.testPerformance) {
            es/* message */.iU.info('Testing browser performance...');
            results.performance = testBrowserPerformance();
            if (results.performance.success) {
              es/* message */.iU.success('Performance test completed');
            } else {
              es/* message */.iU.error("Performance test failed: ".concat(results.performance.error));
            }
          }
          return _context3.abrupt("return", results);
        case 6:
          _context3.prev = 6;
          _t3 = _context3["catch"](1);
          es/* message */.iU.error("Test failed: ".concat(_t3.message));
          return _context3.abrupt("return", _objectSpread(_objectSpread({}, results), {}, {
            error: _t3.message
          }));
        case 7:
        case "end":
          return _context3.stop();
      }
    }, _callee3, null, [[1, 6]]);
  }));
  return function runAllTests() {
    return _ref3.apply(this, arguments);
  };
}();
/* harmony default export */ const testUtils = ({
  testWebSocketConnection: testWebSocketConnection,
  testApiConnection: testApiConnection,
  testBrowserPerformance: testBrowserPerformance,
  runAllTests: runAllTests
});
// EXTERNAL MODULE: ./src/config/env.js
var env = __webpack_require__(26390);
;// ./src/components/enhanced/TestingTools.js




var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6, _templateObject7;
function TestingTools_ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function TestingTools_objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? TestingTools_ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : TestingTools_ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }

function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t["return"] || t["return"](); } finally { if (u) throw o; } } }; }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }






var Title = es/* Typography */.o5.Title,
  Text = es/* Typography */.o5.Text,
  Paragraph = es/* Typography */.o5.Paragraph;
var Panel = es/* Collapse */.SD.Panel;
var TestingContainer = styled_components_browser_esm/* default */.Ay.div(_templateObject || (_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  padding: 20px;\n"])));
var TestCard = (0,styled_components_browser_esm/* default */.Ay)(es/* Card */.Zp)(_templateObject2 || (_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  margin-bottom: 20px;\n"])));
var FormGroup = styled_components_browser_esm/* default */.Ay.div(_templateObject3 || (_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  margin-bottom: 16px;\n\n  .label {\n    display: block;\n    margin-bottom: 8px;\n    font-weight: 500;\n  }\n"])));
var ButtonGroup = styled_components_browser_esm/* default */.Ay.div(_templateObject4 || (_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  gap: 8px;\n  margin-top: 16px;\n"])));
var InfoBox = (0,styled_components_browser_esm/* default */.Ay)(es/* Alert */.Fc)(_templateObject5 || (_templateObject5 = (0,taggedTemplateLiteral/* default */.A)(["\n  margin-bottom: 16px;\n"])));
var ResultCard = (0,styled_components_browser_esm/* default */.Ay)(es/* Card */.Zp)(_templateObject6 || (_templateObject6 = (0,taggedTemplateLiteral/* default */.A)(["\n  margin-top: 16px;\n\n  .ant-card-head {\n    background-color: ", ";\n  }\n"])), function (props) {
  if (props.status === 'success') return 'rgba(82, 196, 26, 0.1)';
  if (props.status === 'error') return 'rgba(245, 34, 45, 0.1)';
  if (props.status === 'warning') return 'rgba(250, 173, 20, 0.1)';
  return 'transparent';
});
var MetricCard = (0,styled_components_browser_esm/* default */.Ay)(es/* Card */.Zp)(_templateObject7 || (_templateObject7 = (0,taggedTemplateLiteral/* default */.A)(["\n  height: 100%;\n\n  .ant-statistic-title {\n    font-size: 14px;\n  }\n\n  .ant-statistic-content {\n    font-size: 24px;\n  }\n"])));
var StatusIcon = function StatusIcon(_ref) {
  var status = _ref.status;
  if (status === 'success') return /*#__PURE__*/react.createElement(icons_es/* CheckCircleOutlined */.hWy, {
    style: {
      color: '#52c41a'
    }
  });
  if (status === 'error') return /*#__PURE__*/react.createElement(icons_es/* CloseCircleOutlined */.bBN, {
    style: {
      color: '#f5222d'
    }
  });
  if (status === 'warning') return /*#__PURE__*/react.createElement(icons_es/* WarningOutlined */.v7y, {
    style: {
      color: '#faad14'
    }
  });
  if (status === 'loading') return /*#__PURE__*/react.createElement(icons_es/* LoadingOutlined */.NKq, {
    style: {
      color: '#1890ff'
    }
  });
  return /*#__PURE__*/react.createElement(icons_es/* InfoCircleOutlined */.rUN, {
    style: {
      color: '#1890ff'
    }
  });
};

/**
 * TestingTools component
 * Provides tools for testing the application
 */
var TestingTools = function TestingTools(_ref2) {
  var _ref2$components = _ref2.components,
    components = _ref2$components === void 0 ? [] : _ref2$components,
    onTestComplete = _ref2.onTestComplete,
    onTestStart = _ref2.onTestStart,
    _ref2$enabledTests = _ref2.enabledTests,
    enabledTests = _ref2$enabledTests === void 0 ? ['component', 'layout', 'accessibility', 'performance'] : _ref2$enabledTests,
    _ref2$autoRun = _ref2.autoRun,
    autoRun = _ref2$autoRun === void 0 ? false : _ref2$autoRun,
    _ref2$showMetrics = _ref2.showMetrics,
    showMetrics = _ref2$showMetrics === void 0 ? true : _ref2$showMetrics;
  // Test configuration
  var _useState = (0,react.useState)((0,env/* getWebSocketUrl */.$0)('app_builder')),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    websocketUrl = _useState2[0],
    setWebsocketUrl = _useState2[1];
  var _useState3 = (0,react.useState)((0,env/* getApiUrl */.e9)('health')),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    apiUrl = _useState4[0],
    setApiUrl = _useState4[1];
  var _useState5 = (0,react.useState)(true),
    _useState6 = (0,slicedToArray/* default */.A)(_useState5, 2),
    testPerformance = _useState6[0],
    setTestPerformance = _useState6[1];

  // Test state
  var _useState7 = (0,react.useState)(false),
    _useState8 = (0,slicedToArray/* default */.A)(_useState7, 2),
    loading = _useState8[0],
    setLoading = _useState8[1];
  var _useState9 = (0,react.useState)(null),
    _useState0 = (0,slicedToArray/* default */.A)(_useState9, 2),
    results = _useState0[0],
    setResults = _useState0[1];
  var _useState1 = (0,react.useState)(null),
    _useState10 = (0,slicedToArray/* default */.A)(_useState1, 2),
    componentTestResults = _useState10[0],
    setComponentTestResults = _useState10[1];

  // Component testing function
  var runComponentTests = /*#__PURE__*/function () {
    var _ref3 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee() {
      var testResults, _iterator, _step, component, componentTest;
      return regenerator_default().wrap(function (_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            if (onTestStart) onTestStart('component');
            testResults = []; // Test each component
            _iterator = _createForOfIteratorHelper(components);
            try {
              for (_iterator.s(); !(_step = _iterator.n()).done;) {
                component = _step.value;
                try {
                  // Basic component validation
                  componentTest = {
                    id: component.id,
                    type: component.type,
                    status: 'passed',
                    message: 'Component structure is valid',
                    timestamp: new Date().toISOString()
                  }; // Check required properties
                  if (!component.type) {
                    componentTest.status = 'failed';
                    componentTest.message = 'Component missing type property';
                  } else if (!component.id) {
                    componentTest.status = 'failed';
                    componentTest.message = 'Component missing id property';
                  }
                  testResults.push(componentTest);
                } catch (error) {
                  testResults.push({
                    id: component.id || 'unknown',
                    type: component.type || 'unknown',
                    status: 'failed',
                    message: "Component test failed: ".concat(error.message),
                    timestamp: new Date().toISOString()
                  });
                }
              }
            } catch (err) {
              _iterator.e(err);
            } finally {
              _iterator.f();
            }
            setComponentTestResults(testResults);
            if (onTestComplete) onTestComplete('component', testResults);
            return _context.abrupt("return", testResults);
          case 1:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function runComponentTests() {
      return _ref3.apply(this, arguments);
    };
  }();

  // Handle running all tests
  var handleRunAllTests = /*#__PURE__*/function () {
    var _ref4 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee2() {
      var componentResults, testResults, allResults, _t;
      return regenerator_default().wrap(function (_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            setLoading(true);
            setResults(null);
            setComponentTestResults(null);
            _context2.prev = 1;
            // Run component tests if enabled
            componentResults = null;
            if (!(enabledTests.includes('component') && components.length > 0)) {
              _context2.next = 3;
              break;
            }
            _context2.next = 2;
            return runComponentTests();
          case 2:
            componentResults = _context2.sent;
          case 3:
            _context2.next = 4;
            return runAllTests({
              websocketUrl: websocketUrl,
              apiUrl: apiUrl,
              testPerformance: testPerformance
            });
          case 4:
            testResults = _context2.sent;
            // Combine results
            allResults = TestingTools_objectSpread(TestingTools_objectSpread({}, testResults), {}, {
              components: componentResults
            });
            setResults(allResults);
            if (onTestComplete) onTestComplete('all', allResults);
            _context2.next = 6;
            break;
          case 5:
            _context2.prev = 5;
            _t = _context2["catch"](1);
            console.error('Test error:', _t);
          case 6:
            _context2.prev = 6;
            setLoading(false);
            return _context2.finish(6);
          case 7:
          case "end":
            return _context2.stop();
        }
      }, _callee2, null, [[1, 5, 6, 7]]);
    }));
    return function handleRunAllTests() {
      return _ref4.apply(this, arguments);
    };
  }();

  // Handle running WebSocket test
  var handleTestWebSocket = /*#__PURE__*/function () {
    var _ref5 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee3() {
      var wsResults, _t2;
      return regenerator_default().wrap(function (_context3) {
        while (1) switch (_context3.prev = _context3.next) {
          case 0:
            setLoading(true);
            _context3.prev = 1;
            _context3.next = 2;
            return testWebSocketConnection(websocketUrl);
          case 2:
            wsResults = _context3.sent;
            setResults(function (prev) {
              return TestingTools_objectSpread(TestingTools_objectSpread({}, prev), {}, {
                websocket: wsResults
              });
            });
            _context3.next = 4;
            break;
          case 3:
            _context3.prev = 3;
            _t2 = _context3["catch"](1);
            console.error('WebSocket test error:', _t2);
          case 4:
            _context3.prev = 4;
            setLoading(false);
            return _context3.finish(4);
          case 5:
          case "end":
            return _context3.stop();
        }
      }, _callee3, null, [[1, 3, 4, 5]]);
    }));
    return function handleTestWebSocket() {
      return _ref5.apply(this, arguments);
    };
  }();

  // Handle running API test
  var handleTestApi = /*#__PURE__*/function () {
    var _ref6 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee4() {
      var apiResults, _t3;
      return regenerator_default().wrap(function (_context4) {
        while (1) switch (_context4.prev = _context4.next) {
          case 0:
            setLoading(true);
            _context4.prev = 1;
            _context4.next = 2;
            return testApiConnection(apiUrl);
          case 2:
            apiResults = _context4.sent;
            setResults(function (prev) {
              return TestingTools_objectSpread(TestingTools_objectSpread({}, prev), {}, {
                api: apiResults
              });
            });
            _context4.next = 4;
            break;
          case 3:
            _context4.prev = 3;
            _t3 = _context4["catch"](1);
            console.error('API test error:', _t3);
          case 4:
            _context4.prev = 4;
            setLoading(false);
            return _context4.finish(4);
          case 5:
          case "end":
            return _context4.stop();
        }
      }, _callee4, null, [[1, 3, 4, 5]]);
    }));
    return function handleTestApi() {
      return _ref6.apply(this, arguments);
    };
  }();

  // Handle running performance test
  var handleTestPerformance = function handleTestPerformance() {
    setLoading(true);
    try {
      var perfResults = testBrowserPerformance();
      setResults(function (prev) {
        return TestingTools_objectSpread(TestingTools_objectSpread({}, prev), {}, {
          performance: perfResults
        });
      });
    } catch (error) {
      console.error('Performance test error:', error);
    } finally {
      setLoading(false);
    }
  };

  // Render WebSocket test results
  var renderWebSocketResults = function renderWebSocketResults() {
    if (!results || !results.websocket) return null;
    var websocket = results.websocket;
    var status = websocket.success ? 'success' : 'error';
    return /*#__PURE__*/react.createElement(ResultCard, {
      title: /*#__PURE__*/react.createElement(es/* Space */.$x, null, /*#__PURE__*/react.createElement(StatusIcon, {
        status: status
      }), /*#__PURE__*/react.createElement("span", null, "WebSocket Test Results")),
      status: status
    }, websocket.success ? /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement(es/* Row */.fI, {
      gutter: [16, 16]
    }, /*#__PURE__*/react.createElement(es/* Col */.fv, {
      xs: 24,
      sm: 12
    }, /*#__PURE__*/react.createElement(es/* Statistic */.jL, {
      title: "Connection Time",
      value: websocket.connectionTime ? Math.round(websocket.connectionTime) : 'N/A',
      suffix: "ms"
    })), /*#__PURE__*/react.createElement(es/* Col */.fv, {
      xs: 24,
      sm: 12
    }, /*#__PURE__*/react.createElement(es/* Statistic */.jL, {
      title: "Response Time",
      value: websocket.responseTime ? Math.round(websocket.responseTime - websocket.connectionTime) : 'N/A',
      suffix: "ms"
    }))), /*#__PURE__*/react.createElement(es/* Divider */.cG, null), /*#__PURE__*/react.createElement(Paragraph, null, /*#__PURE__*/react.createElement(Text, {
      strong: true
    }, "Status:"), " ", websocket.message || 'Connection successful')) : /*#__PURE__*/react.createElement(es/* Alert */.Fc, {
      message: "Connection Failed",
      description: websocket.error || 'Unknown error',
      type: "error",
      showIcon: true
    }));
  };

  // Render API test results
  var renderApiResults = function renderApiResults() {
    if (!results || !results.api) return null;
    var api = results.api;
    var status = api.success ? 'success' : 'error';
    return /*#__PURE__*/react.createElement(ResultCard, {
      title: /*#__PURE__*/react.createElement(es/* Space */.$x, null, /*#__PURE__*/react.createElement(StatusIcon, {
        status: status
      }), /*#__PURE__*/react.createElement("span", null, "API Test Results")),
      status: status
    }, api.success ? /*#__PURE__*/react.createElement(react.Fragment, null, /*#__PURE__*/react.createElement(es/* Row */.fI, {
      gutter: [16, 16]
    }, /*#__PURE__*/react.createElement(es/* Col */.fv, {
      xs: 24,
      sm: 8
    }, /*#__PURE__*/react.createElement(es/* Statistic */.jL, {
      title: "Response Time",
      value: Math.round(api.time),
      suffix: "ms"
    })), /*#__PURE__*/react.createElement(es/* Col */.fv, {
      xs: 24,
      sm: 8
    }, /*#__PURE__*/react.createElement(es/* Statistic */.jL, {
      title: "Status Code",
      value: api.status,
      suffix: api.statusText
    })), /*#__PURE__*/react.createElement(es/* Col */.fv, {
      xs: 24,
      sm: 8
    }, /*#__PURE__*/react.createElement(es/* Statistic */.jL, {
      title: "Response Size",
      value: api.data ? JSON.stringify(api.data).length : 0,
      suffix: "bytes"
    }))), /*#__PURE__*/react.createElement(es/* Divider */.cG, null), /*#__PURE__*/react.createElement(es/* Collapse */.SD, null, /*#__PURE__*/react.createElement(Panel, {
      header: "Response Data",
      key: "1"
    }, /*#__PURE__*/react.createElement("pre", {
      style: {
        maxHeight: '200px',
        overflow: 'auto'
      }
    }, JSON.stringify(api.data, null, 2))))) : /*#__PURE__*/react.createElement(es/* Alert */.Fc, {
      message: "HTTP ".concat(api.status || 'Error'),
      description: api.error || 'Unknown error',
      type: "error",
      showIcon: true
    }));
  };

  // Render performance test results
  var renderPerformanceResults = function renderPerformanceResults() {
    if (!results || !results.performance) return null;
    var performance = results.performance;
    var status = performance.success ? 'success' : 'error';
    if (!performance.success) {
      return /*#__PURE__*/react.createElement(ResultCard, {
        title: /*#__PURE__*/react.createElement(es/* Space */.$x, null, /*#__PURE__*/react.createElement(StatusIcon, {
          status: "error"
        }), /*#__PURE__*/react.createElement("span", null, "Performance Test Results")),
        status: "error"
      }, /*#__PURE__*/react.createElement(es/* Alert */.Fc, {
        message: "Test Failed",
        description: performance.error || 'Unknown error',
        type: "error",
        showIcon: true
      }));
    }
    var metrics = performance.metrics;

    // Calculate performance score (0-100)
    var performanceScore = 0;
    var scoreComponents = 0;
    if (metrics.pageLoad) {
      // Page load: <1s is great, >3s is poor
      var pageLoadScore = Math.max(0, 100 - metrics.pageLoad / 30);
      performanceScore += pageLoadScore;
      scoreComponents++;
    }
    if (metrics.frameRate && metrics.frameRate.average) {
      // Frame rate: 60fps is perfect, <30fps is poor
      var frameRateScore = Math.min(100, metrics.frameRate.average / 60 * 100);
      performanceScore += frameRateScore;
      scoreComponents++;
    }
    if (metrics.memory && metrics.memory.usedJSHeapSize && metrics.memory.jsHeapSizeLimit) {
      // Memory usage: <50% is great, >80% is poor
      var memoryUsagePercent = metrics.memory.usedJSHeapSize / metrics.memory.jsHeapSizeLimit * 100;
      var memoryScore = Math.max(0, 100 - memoryUsagePercent);
      performanceScore += memoryScore;
      scoreComponents++;
    }

    // Calculate final score
    var finalScore = scoreComponents > 0 ? Math.round(performanceScore / scoreComponents) : 0;

    // Determine status based on score
    var performanceStatus = 'success';
    if (finalScore < 50) performanceStatus = 'error';else if (finalScore < 70) performanceStatus = 'warning';
    return /*#__PURE__*/react.createElement(ResultCard, {
      title: /*#__PURE__*/react.createElement(es/* Space */.$x, null, /*#__PURE__*/react.createElement(StatusIcon, {
        status: performanceStatus
      }), /*#__PURE__*/react.createElement("span", null, "Performance Test Results")),
      status: performanceStatus
    }, /*#__PURE__*/react.createElement(es/* Row */.fI, {
      gutter: [16, 16],
      style: {
        marginBottom: '16px'
      }
    }, /*#__PURE__*/react.createElement(es/* Col */.fv, {
      xs: 24,
      md: 8
    }, /*#__PURE__*/react.createElement(MetricCard, null, /*#__PURE__*/react.createElement(es/* Statistic */.jL, {
      title: "Performance Score",
      value: finalScore,
      suffix: "/100",
      valueStyle: {
        color: finalScore >= 70 ? '#52c41a' : finalScore >= 50 ? '#faad14' : '#f5222d'
      }
    }), /*#__PURE__*/react.createElement(es/* Progress */.ke, {
      percent: finalScore,
      status: finalScore >= 70 ? 'success' : finalScore >= 50 ? 'normal' : 'exception',
      showInfo: false,
      style: {
        marginTop: '8px'
      }
    }))), metrics.pageLoad && /*#__PURE__*/react.createElement(es/* Col */.fv, {
      xs: 24,
      md: 8
    }, /*#__PURE__*/react.createElement(MetricCard, null, /*#__PURE__*/react.createElement(es/* Statistic */.jL, {
      title: /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
        title: "Time to fully load the page"
      }, /*#__PURE__*/react.createElement("span", null, "Page Load Time ", /*#__PURE__*/react.createElement(icons_es/* InfoCircleOutlined */.rUN, null))),
      value: Math.round(metrics.pageLoad),
      suffix: "ms",
      valueStyle: {
        color: metrics.pageLoad < 1000 ? '#52c41a' : metrics.pageLoad < 3000 ? '#faad14' : '#f5222d'
      }
    }))), metrics.frameRate && metrics.frameRate.average && /*#__PURE__*/react.createElement(es/* Col */.fv, {
      xs: 24,
      md: 8
    }, /*#__PURE__*/react.createElement(MetricCard, null, /*#__PURE__*/react.createElement(es/* Statistic */.jL, {
      title: /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
        title: "Average frames per second"
      }, /*#__PURE__*/react.createElement("span", null, "Frame Rate ", /*#__PURE__*/react.createElement(icons_es/* InfoCircleOutlined */.rUN, null))),
      value: metrics.frameRate.average,
      suffix: "fps",
      valueStyle: {
        color: metrics.frameRate.average >= 50 ? '#52c41a' : metrics.frameRate.average >= 30 ? '#faad14' : '#f5222d'
      }
    })))), /*#__PURE__*/react.createElement(es/* Collapse */.SD, null, /*#__PURE__*/react.createElement(Panel, {
      header: "Detailed Metrics",
      key: "1"
    }, /*#__PURE__*/react.createElement(es/* Row */.fI, {
      gutter: [16, 16]
    }, metrics.domReady && /*#__PURE__*/react.createElement(es/* Col */.fv, {
      xs: 24,
      md: 8
    }, /*#__PURE__*/react.createElement(es/* Statistic */.jL, {
      title: "DOM Ready Time",
      value: Math.round(metrics.domReady),
      suffix: "ms"
    })), metrics.networkLatency && /*#__PURE__*/react.createElement(es/* Col */.fv, {
      xs: 24,
      md: 8
    }, /*#__PURE__*/react.createElement(es/* Statistic */.jL, {
      title: "Network Latency",
      value: Math.round(metrics.networkLatency),
      suffix: "ms"
    })), metrics.processingTime && /*#__PURE__*/react.createElement(es/* Col */.fv, {
      xs: 24,
      md: 8
    }, /*#__PURE__*/react.createElement(es/* Statistic */.jL, {
      title: "Processing Time",
      value: Math.round(metrics.processingTime),
      suffix: "ms"
    })), metrics.backendTime && /*#__PURE__*/react.createElement(es/* Col */.fv, {
      xs: 24,
      md: 8
    }, /*#__PURE__*/react.createElement(es/* Statistic */.jL, {
      title: "Backend Time",
      value: Math.round(metrics.backendTime),
      suffix: "ms"
    })), metrics.frontendTime && /*#__PURE__*/react.createElement(es/* Col */.fv, {
      xs: 24,
      md: 8
    }, /*#__PURE__*/react.createElement(es/* Statistic */.jL, {
      title: "Frontend Time",
      value: Math.round(metrics.frontendTime),
      suffix: "ms"
    })), metrics.memory && metrics.memory.usedJSHeapSize && /*#__PURE__*/react.createElement(es/* Col */.fv, {
      xs: 24,
      md: 8
    }, /*#__PURE__*/react.createElement(es/* Statistic */.jL, {
      title: "Memory Usage",
      value: (metrics.memory.usedJSHeapSize / (1024 * 1024)).toFixed(1),
      suffix: "MB"
    })), metrics.memory && metrics.memory.totalJSHeapSize && /*#__PURE__*/react.createElement(es/* Col */.fv, {
      xs: 24,
      md: 8
    }, /*#__PURE__*/react.createElement(es/* Statistic */.jL, {
      title: "Total Heap Size",
      value: (metrics.memory.totalJSHeapSize / (1024 * 1024)).toFixed(1),
      suffix: "MB"
    })), metrics.memory && metrics.memory.jsHeapSizeLimit && /*#__PURE__*/react.createElement(es/* Col */.fv, {
      xs: 24,
      md: 8
    }, /*#__PURE__*/react.createElement(es/* Statistic */.jL, {
      title: "Heap Size Limit",
      value: (metrics.memory.jsHeapSizeLimit / (1024 * 1024)).toFixed(1),
      suffix: "MB"
    }))))));
  };
  return /*#__PURE__*/react.createElement(TestingContainer, null, /*#__PURE__*/react.createElement(Title, {
    level: 3
  }, "Testing Tools"), /*#__PURE__*/react.createElement(Paragraph, null, "Use these tools to test various aspects of the application."), /*#__PURE__*/react.createElement(TestCard, {
    title: "Test Configuration"
  }, /*#__PURE__*/react.createElement(InfoBox, {
    type: "info",
    message: "Configure Test Parameters",
    description: "Set up the parameters for the tests you want to run.",
    icon: /*#__PURE__*/react.createElement(icons_es/* InfoCircleOutlined */.rUN, null)
  }), /*#__PURE__*/react.createElement(FormGroup, null, /*#__PURE__*/react.createElement("div", {
    className: "label"
  }, "WebSocket URL:"), /*#__PURE__*/react.createElement(es/* Input */.pd, {
    value: websocketUrl,
    onChange: function onChange(e) {
      return setWebsocketUrl(e.target.value);
    },
    placeholder: "Enter WebSocket URL",
    prefix: /*#__PURE__*/react.createElement(icons_es/* LinkOutlined */.t7c, null),
    disabled: loading
  })), /*#__PURE__*/react.createElement(FormGroup, null, /*#__PURE__*/react.createElement("div", {
    className: "label"
  }, "API URL:"), /*#__PURE__*/react.createElement(es/* Input */.pd, {
    value: apiUrl,
    onChange: function onChange(e) {
      return setApiUrl(e.target.value);
    },
    placeholder: "Enter API URL",
    prefix: /*#__PURE__*/react.createElement(icons_es/* ApiOutlined */.bfv, null),
    disabled: loading
  })), /*#__PURE__*/react.createElement(FormGroup, null, /*#__PURE__*/react.createElement("div", {
    className: "label"
  }, "Include Performance Test:"), /*#__PURE__*/react.createElement(es/* Switch */.dO, {
    checked: testPerformance,
    onChange: setTestPerformance,
    disabled: loading
  })), /*#__PURE__*/react.createElement(ButtonGroup, null, /*#__PURE__*/react.createElement(es/* Button */.$n, {
    type: "primary",
    icon: /*#__PURE__*/react.createElement(icons_es/* PlayCircleOutlined */.VgC, null),
    onClick: handleRunAllTests,
    loading: loading,
    disabled: loading
  }, "Run All Tests"), enabledTests.includes('component') && /*#__PURE__*/react.createElement(es/* Button */.$n, {
    icon: /*#__PURE__*/react.createElement(icons_es/* CheckCircleOutlined */.hWy, null),
    onClick: runComponentTests,
    disabled: loading || components.length === 0
  }, "Test Components (", components.length, ")"), /*#__PURE__*/react.createElement(es/* Button */.$n, {
    icon: /*#__PURE__*/react.createElement(icons_es/* ApiOutlined */.bfv, null),
    onClick: handleTestWebSocket,
    disabled: loading || !websocketUrl
  }, "Test WebSocket"), /*#__PURE__*/react.createElement(es/* Button */.$n, {
    icon: /*#__PURE__*/react.createElement(icons_es/* LinkOutlined */.t7c, null),
    onClick: handleTestApi,
    disabled: loading || !apiUrl
  }, "Test API"), /*#__PURE__*/react.createElement(es/* Button */.$n, {
    icon: /*#__PURE__*/react.createElement(icons_es/* DashboardOutlined */.zpd, null),
    onClick: handleTestPerformance,
    disabled: loading || !testPerformance
  }, "Test Performance"))), loading && /*#__PURE__*/react.createElement("div", {
    style: {
      textAlign: 'center',
      padding: '40px 0'
    }
  }, /*#__PURE__*/react.createElement(es/* Spin */.tK, {
    size: "large"
  }), /*#__PURE__*/react.createElement("div", {
    style: {
      marginTop: '16px'
    }
  }, "Running tests...")), componentTestResults && /*#__PURE__*/react.createElement(ResultCard, {
    title: "Component Test Results",
    size: "small"
  }, /*#__PURE__*/react.createElement(es/* Row */.fI, {
    gutter: [16, 16]
  }, /*#__PURE__*/react.createElement(es/* Col */.fv, {
    span: 8
  }, /*#__PURE__*/react.createElement(es/* Statistic */.jL, {
    title: "Total Components",
    value: componentTestResults.length,
    prefix: /*#__PURE__*/react.createElement(icons_es/* CheckCircleOutlined */.hWy, null)
  })), /*#__PURE__*/react.createElement(es/* Col */.fv, {
    span: 8
  }, /*#__PURE__*/react.createElement(es/* Statistic */.jL, {
    title: "Passed",
    value: componentTestResults.filter(function (r) {
      return r.status === 'passed';
    }).length,
    valueStyle: {
      color: '#3f8600'
    },
    prefix: /*#__PURE__*/react.createElement(icons_es/* CheckCircleOutlined */.hWy, null)
  })), /*#__PURE__*/react.createElement(es/* Col */.fv, {
    span: 8
  }, /*#__PURE__*/react.createElement(es/* Statistic */.jL, {
    title: "Failed",
    value: componentTestResults.filter(function (r) {
      return r.status === 'failed';
    }).length,
    valueStyle: {
      color: '#cf1322'
    },
    prefix: /*#__PURE__*/react.createElement(icons_es/* CloseCircleOutlined */.bBN, null)
  }))), /*#__PURE__*/react.createElement(es/* Divider */.cG, null), /*#__PURE__*/react.createElement(es/* Collapse */.SD, {
    size: "small"
  }, componentTestResults.map(function (result, index) {
    return /*#__PURE__*/react.createElement(Panel, {
      key: index,
      header: /*#__PURE__*/react.createElement(es/* Space */.$x, null, result.status === 'passed' ? /*#__PURE__*/react.createElement(icons_es/* CheckCircleOutlined */.hWy, {
        style: {
          color: '#52c41a'
        }
      }) : /*#__PURE__*/react.createElement(icons_es/* CloseCircleOutlined */.bBN, {
        style: {
          color: '#ff4d4f'
        }
      }), /*#__PURE__*/react.createElement(Text, {
        strong: true
      }, result.type), /*#__PURE__*/react.createElement(Text, {
        type: "secondary"
      }, "(", result.id, ")"))
    }, /*#__PURE__*/react.createElement(Text, null, result.message), /*#__PURE__*/react.createElement("br", null), /*#__PURE__*/react.createElement(Text, {
      type: "secondary",
      style: {
        fontSize: '12px'
      }
    }, new Date(result.timestamp).toLocaleString()));
  }))), results && /*#__PURE__*/react.createElement(react.Fragment, null, renderWebSocketResults(), renderApiResults(), renderPerformanceResults()));
};
/* harmony default export */ const enhanced_TestingTools = (TestingTools);

/***/ })

}]);