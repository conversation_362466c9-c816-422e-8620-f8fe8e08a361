"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[5921],{

/***/ 75921:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(57528);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(33966);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(71468);
/* harmony import */ var _redux_reducers_uiReducer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(85331);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(71606);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(36031);
/* harmony import */ var _contexts_EnhancedThemeContext__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(82569);
/* harmony import */ var _components_layout_EnhancedLayout__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(99160);
/* harmony import */ var _hooks_useRealTimePreview__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(79459);
/* harmony import */ var _hooks_useResponsivePreview__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(17050);
/* harmony import */ var _hooks_useCollaborativePreview__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(25577);
/* harmony import */ var _components_performance__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(4617);
/* harmony import */ var _components_enhanced_SafeComponentWrapper__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(88167);




var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6, _templateObject7;

function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }









// Import new preview functionality





// Import safe wrapper


// Import the actual components using lazy loading for better performance with error handling
var ComponentBuilder = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_5__.lazy)(function () {
  return Promise.all(/* import() */[__webpack_require__.e(2112), __webpack_require__.e(5291), __webpack_require__.e(2621), __webpack_require__.e(9158), __webpack_require__.e(9787), __webpack_require__.e(1893), __webpack_require__.e(9774), __webpack_require__.e(8292), __webpack_require__.e(3366), __webpack_require__.e(6744), __webpack_require__.e(4922), __webpack_require__.e(2825), __webpack_require__.e(9530), __webpack_require__.e(6731), __webpack_require__.e(1018), __webpack_require__.e(9805), __webpack_require__.e(4788), __webpack_require__.e(7637)]).then(__webpack_require__.bind(__webpack_require__, 16030))["catch"](function (err) {
    console.warn('Failed to load ComponentBuilder:', err);
    return {
      "default": function _default() {
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", null, "Component Builder not available");
      }
    };
  });
});
var LayoutDesigner = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_5__.lazy)(function () {
  return Promise.all(/* import() */[__webpack_require__.e(5505), __webpack_require__.e(4816)]).then(__webpack_require__.bind(__webpack_require__, 95505))["catch"](function (err) {
    console.warn('Failed to load LayoutDesigner:', err);
    return {
      "default": function _default() {
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", null, "Layout Designer not available");
      }
    };
  });
});
var ThemeManager = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_5__.lazy)(function () {
  return __webpack_require__.e(/* import() */ 1667).then(__webpack_require__.bind(__webpack_require__, 71667))["catch"](function (err) {
    console.warn('Failed to load ThemeManager:', err);
    return {
      "default": function _default() {
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", null, "Theme Manager not available");
      }
    };
  });
});
var FixedWebSocketManager = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_5__.lazy)(function () {
  return Promise.all(/* import() */[__webpack_require__.e(1470), __webpack_require__.e(2472), __webpack_require__.e(7136)]).then(__webpack_require__.bind(__webpack_require__, 97136))["catch"](function (err) {
    console.warn('Failed to load FixedWebSocketManager:', err);
    return {
      "default": function _default() {
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", null, "WebSocket Manager not available");
      }
    };
  });
});
var ProjectManager = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_5__.lazy)(function () {
  return __webpack_require__.e(/* import() */ 1680).then(__webpack_require__.bind(__webpack_require__, 51680))["catch"](function (err) {
    console.warn('Failed to load ProjectManager:', err);
    return {
      "default": function _default() {
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", null, "Project Manager not available");
      }
    };
  });
});
var CodeExporter = /*#__PURE__*/(/* unused pure expression or super */ null && (lazy(function () {
  return __webpack_require__.e(/* import() */ 5008).then(__webpack_require__.bind(__webpack_require__, 75008))["catch"](function (err) {
    console.warn('Failed to load CodeExporter:', err);
    return {
      "default": function _default() {
        return /*#__PURE__*/React.createElement("div", null, "Code Exporter not available");
      }
    };
  });
})));
var EnhancedCodeExporter = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_5__.lazy)(function () {
  return __webpack_require__.e(/* import() */ 9205).then(__webpack_require__.bind(__webpack_require__, 19205))["catch"](function (err) {
    console.warn('Failed to load EnhancedCodeExporter:', err);
    return {
      "default": function _default() {
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", null, "Enhanced Code Exporter not available");
      }
    };
  });
});
var PerformanceMonitor = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_5__.lazy)(function () {
  return __webpack_require__.e(/* import() */ 672).then(__webpack_require__.bind(__webpack_require__, 40672))["catch"](function (err) {
    console.warn('Failed to load PerformanceMonitor:', err);
    return {
      "default": function _default() {
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", null, "Performance Monitor not available");
      }
    };
  });
});
var DataManagementDemo = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_5__.lazy)(function () {
  return Promise.all(/* import() */[__webpack_require__.e(7481), __webpack_require__.e(2435)]).then(__webpack_require__.bind(__webpack_require__, 97481))["catch"](function (err) {
    console.warn('Failed to load DataManagementDemo:', err);
    return {
      "default": function _default() {
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", null, "Data Management not available");
      }
    };
  });
});
var TestingTools = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_5__.lazy)(function () {
  return __webpack_require__.e(/* import() */ 4605).then(__webpack_require__.bind(__webpack_require__, 54605))["catch"](function (err) {
    console.warn('Failed to load TestingTools:', err);
    return {
      "default": function _default() {
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", null, "Testing Tools not available");
      }
    };
  });
});
var TutorialAIPlugin = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_5__.lazy)(function () {
  return __webpack_require__.e(/* import() */ 3726).then(__webpack_require__.bind(__webpack_require__, 23726))["catch"](function (err) {
    console.warn('Failed to load TutorialAIPlugin:', err);
    return {
      "default": function _default() {
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", null, "Tutorial Assistant not available");
      }
    };
  });
});
var Title = antd__WEBPACK_IMPORTED_MODULE_6__/* .Typography */ .o5.Title,
  Paragraph = antd__WEBPACK_IMPORTED_MODULE_6__/* .Typography */ .o5.Paragraph;
var AppBuilderContainer = styled_components__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  padding: var(--spacing-lg);\n  max-width: 1200px;\n  margin: 0 auto;\n  background-color: var(--color-background);\n  min-height: 100vh;\n  transition: all 0.3s ease;\n\n  @media (max-width: 768px) {\n    padding: var(--spacing-md);\n  }\n\n  @media (max-width: 480px) {\n    padding: var(--spacing-sm);\n  }\n"])));
var AppHeader = styled_components__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  margin-bottom: var(--spacing-xl);\n  padding: var(--spacing-lg);\n  background-color: var(--color-surface);\n  border-radius: var(--border-radius-lg);\n  box-shadow: var(--shadow-sm);\n  border: 1px solid var(--color-border-light);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  transition: all 0.3s ease;\n\n  &:hover {\n    box-shadow: var(--shadow-md);\n  }\n\n  @media (max-width: 768px) {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: var(--spacing-md);\n\n    h2 {\n      margin-bottom: var(--spacing-sm);\n    }\n  }\n"])));
var StyledTabs = (0,styled_components__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_6__/* .Tabs */ .tU)(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  .ant-tabs-nav {\n    margin-bottom: var(--spacing-xl);\n    background-color: var(--color-surface);\n    border-radius: var(--border-radius-lg);\n    padding: var(--spacing-sm);\n    box-shadow: var(--shadow-sm);\n    border: 1px solid var(--color-border-light);\n  }\n\n  .ant-tabs-tab {\n    padding: var(--spacing-md) var(--spacing-lg);\n    transition: all 0.3s ease;\n    border-radius: var(--border-radius-md);\n    margin: 0 var(--spacing-xs);\n    color: var(--color-text-secondary);\n    font-weight: 500;\n\n    &:hover {\n      color: var(--color-primary);\n      background-color: var(--color-background-secondary);\n      transform: translateY(-2px);\n    }\n\n    .anticon {\n      margin-right: var(--spacing-sm);\n      font-size: 16px;\n    }\n  }\n\n  .ant-tabs-tab-active {\n    background-color: var(--color-primary);\n    color: white;\n    box-shadow: var(--shadow-md);\n\n    &:hover {\n      background-color: var(--color-primary-hover);\n      color: white;\n    }\n  }\n\n  .ant-tabs-content-holder {\n    background-color: var(--color-surface);\n    border-radius: var(--border-radius-lg);\n    box-shadow: var(--shadow-sm);\n    border: 1px solid var(--color-border-light);\n    overflow: hidden;\n  }\n\n  .ant-tabs-tabpane {\n    padding: var(--spacing-lg);\n  }\n"])));
var ComponentCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_6__/* .Card */ .Zp)(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  height: 100%;\n  transition: all 0.3s ease;\n  cursor: pointer;\n  background-color: var(--color-surface);\n  border: 1px solid var(--color-border-light);\n  border-radius: var(--border-radius-lg);\n  overflow: hidden;\n\n  &:hover {\n    transform: translateY(-8px);\n    box-shadow: var(--shadow-lg);\n    border-color: var(--color-primary);\n  }\n\n  &:active {\n    transform: translateY(-4px);\n  }\n\n  .ant-card-head {\n    background-color: ", ";\n    color: ", ";\n    border-bottom: 1px solid var(--color-border-light);\n    transition: all 0.3s ease;\n  }\n\n  .ant-card-body {\n    padding: var(--spacing-lg);\n    text-align: center;\n    background-color: var(--color-surface);\n  }\n"])), function (props) {
  return props.active ? 'var(--color-primary)' : 'var(--color-background-secondary)';
}, function (props) {
  return props.active ? 'white' : 'var(--color-text)';
});
var ComponentIcon = styled_components__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay.div(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  font-size: 32px;\n  margin-bottom: var(--spacing-md);\n  color: ", ";\n  transition: all 0.3s ease;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  width: 64px;\n  height: 64px;\n  border-radius: 50%;\n  background-color: ", ";\n  margin: 0 auto var(--spacing-md);\n"])), function (props) {
  return props.active ? 'var(--color-primary)' : 'var(--color-text-secondary)';
}, function (props) {
  return props.active ? 'rgba(24, 144, 255, 0.1)' : 'var(--color-background-secondary)';
});
var WelcomeCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_6__/* .Card */ .Zp)(_templateObject6 || (_templateObject6 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  margin-bottom: var(--spacing-xl);\n  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);\n  border: none;\n  border-radius: var(--border-radius-xl);\n  overflow: hidden;\n  box-shadow: var(--shadow-lg);\n  position: relative;\n\n  /* Add overlay for better text contrast */\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: rgba(0, 0, 0, 0.1);\n    z-index: 1;\n  }\n\n  .ant-card-body {\n    padding: var(--spacing-xxl);\n    color: white;\n    position: relative;\n    z-index: 2;\n  }\n\n  h4, p {\n    color: white !important;\n    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\n    position: relative;\n    z-index: 2;\n  }\n\n  .ant-btn-primary {\n    background-color: white;\n    border-color: white;\n    color: var(--color-primary);\n    font-weight: 600;\n    position: relative;\n    z-index: 2;\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n\n    &:hover {\n      background-color: rgba(255, 255, 255, 0.95);\n      border-color: rgba(255, 255, 255, 0.95);\n      transform: translateY(-2px);\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);\n    }\n  }\n"])));

// Define the spin animation
var SpinAnimation = styled_components__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay.div(_templateObject7 || (_templateObject7 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  @keyframes spin {\n    0% {\n      transform: rotate(0deg);\n    }\n    100% {\n      transform: rotate(360deg);\n    }\n  }\n"])));

/**
 * Enhanced App Builder with improved accessibility, performance, and code organization
 */
var AppBuilderEnhanced = function AppBuilderEnhanced() {
  var dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_7__/* .useDispatch */ .wA)();
  var _useEnhancedTheme = (0,_contexts_EnhancedThemeContext__WEBPACK_IMPORTED_MODULE_11__/* .useEnhancedTheme */ .ZV)(),
    isDarkMode = _useEnhancedTheme.isDarkMode,
    colors = _useEnhancedTheme.colors;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState3, 2),
    error = _useState4[0],
    setError = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)({
      api: 'checking',
      websocket: 'checking'
    }),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState5, 2),
    connectionStatus = _useState6[0],
    setConnectionStatus = _useState6[1];
  // Initialize with the current view from Redux or default to 'components'
  var currentViewFromStore = (0,react_redux__WEBPACK_IMPORTED_MODULE_7__/* .useSelector */ .d4)(function (state) {
    var _state$ui;
    return ((_state$ui = state.ui) === null || _state$ui === void 0 ? void 0 : _state$ui.currentView) || 'components';
  });
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(currentViewFromStore),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState7, 2),
    activeComponent = _useState8[0],
    setActiveComponent = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState9, 2),
    showTutorialModal = _useState0[0],
    setShowTutorialModal = _useState0[1];

  // Enhanced preview state
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)({
      realTimeEnabled: true,
      collaborationEnabled: false,
      performanceMonitoring: true,
      deviceSync: true
    }),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState1, 2),
    previewSettings = _useState10[0],
    setPreviewSettings = _useState10[1];
  var _useState11 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(function () {
      return "session_".concat(Date.now(), "_").concat(Math.random().toString(36).substring(2, 11));
    }),
    _useState12 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState11, 1),
    sessionId = _useState12[0];
  var _useState13 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(function () {
      return "user_".concat(Math.random().toString(36).substring(2, 11));
    }),
    _useState14 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState13, 1),
    userId = _useState14[0];

  // WebSocket and collaboration state
  var websocketConnected = (0,react_redux__WEBPACK_IMPORTED_MODULE_7__/* .useSelector */ .d4)(function (state) {
    var _state$websocket;
    return ((_state$websocket = state.websocket) === null || _state$websocket === void 0 ? void 0 : _state$websocket.connected) || false;
  });
  var components = (0,react_redux__WEBPACK_IMPORTED_MODULE_7__/* .useSelector */ .d4)(function (state) {
    var _state$app;
    return ((_state$app = state.app) === null || _state$app === void 0 ? void 0 : _state$app.components) || [];
  });

  // Initialize enhanced preview hooks
  var responsivePreview = (0,_hooks_useResponsivePreview__WEBPACK_IMPORTED_MODULE_14__/* ["default"] */ .A)({
    initialDevice: 'desktop',
    enableBreakpointDetection: true
  });
  var collaborativePreview = (0,_hooks_useCollaborativePreview__WEBPACK_IMPORTED_MODULE_15__/* ["default"] */ .A)({
    sessionId: sessionId,
    userId: userId,
    username: 'App Builder User',
    enableCollaboration: previewSettings.collaborationEnabled && websocketConnected,
    enableCursorTracking: true,
    enableDeviceSync: previewSettings.deviceSync
  });
  var realTimePreview = (0,_hooks_useRealTimePreview__WEBPACK_IMPORTED_MODULE_13__/* ["default"] */ .A)({
    components: components,
    websocketService: collaborativePreview.wsService,
    enableWebSocket: previewSettings.realTimeEnabled && websocketConnected,
    updateDelay: 300
  });

  // Handle preview settings changes
  var handlePreviewSettingChange = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(function (setting, value) {
    setPreviewSettings(function (prev) {
      return _objectSpread(_objectSpread({}, prev), {}, (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)({}, setting, value));
    });
  }, []);
  (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(function () {
    // Initialize the app
    var initApp = /*#__PURE__*/function () {
      var _ref = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee() {
        var isDev, apiResponse, ws, timeoutId, _t, _t2;
        return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().wrap(function (_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.prev = 0;
              // Set initial view in Redux
              dispatch((0,_redux_reducers_uiReducer__WEBPACK_IMPORTED_MODULE_8__/* .setCurrentView */ .tI)(activeComponent));
              console.log('App Builder initialized successfully');

              // Check if we're in development mode
              isDev = "production" === 'development'; // In development mode, we can continue even if connections fail
              if (isDev) {
                console.log('Development mode: App will continue to load even if connections fail');
              }

              // Quick API connection check
              _context.prev = 1;
              console.log('Checking API connection...');
              _context.next = 2;
              return Promise.race([fetch('/api/status/'), new Promise(function (_, reject) {
                return setTimeout(function () {
                  return reject(new Error('API timeout'));
                }, 1000);
              })]);
            case 2:
              apiResponse = _context.sent;
              if (apiResponse.ok) {
                setConnectionStatus(function (prev) {
                  return _objectSpread(_objectSpread({}, prev), {}, {
                    api: 'connected'
                  });
                });
                console.log('API connection successful');
              } else {
                setConnectionStatus(function (prev) {
                  return _objectSpread(_objectSpread({}, prev), {}, {
                    api: isDev ? 'warning' : 'error'
                  });
                });
                console.warn('API connection failed');
              }
              _context.next = 4;
              break;
            case 3:
              _context.prev = 3;
              _t = _context["catch"](1);
              setConnectionStatus(function (prev) {
                return _objectSpread(_objectSpread({}, prev), {}, {
                  api: isDev ? 'warning' : 'error'
                });
              });
              console.warn('API connection error:', _t.message);
              if (isDev) {
                console.log('Development mode: Continuing with mock API');
              }
            case 4:
              // Quick WebSocket connection check
              try {
                console.log('Checking WebSocket connection...');
                // Skip WebSocket check in development mode to speed up loading
                if (isDev) {
                  setConnectionStatus(function (prev) {
                    return _objectSpread(_objectSpread({}, prev), {}, {
                      websocket: 'warning'
                    });
                  });
                  console.log('Development mode: Skipping WebSocket check');
                } else {
                  // Quick WebSocket test
                  ws = new WebSocket('ws://localhost:8000/ws/app_builder/');
                  timeoutId = setTimeout(function () {
                    ws.close();
                    setConnectionStatus(function (prev) {
                      return _objectSpread(_objectSpread({}, prev), {}, {
                        websocket: 'error'
                      });
                    });
                  }, 1000);
                  ws.onopen = function () {
                    clearTimeout(timeoutId);
                    setConnectionStatus(function (prev) {
                      return _objectSpread(_objectSpread({}, prev), {}, {
                        websocket: 'connected'
                      });
                    });
                    console.log('WebSocket connection successful');
                    ws.close();
                  };
                  ws.onerror = function () {
                    clearTimeout(timeoutId);
                    setConnectionStatus(function (prev) {
                      return _objectSpread(_objectSpread({}, prev), {}, {
                        websocket: 'error'
                      });
                    });
                    console.warn('WebSocket connection failed');
                    ws.close();
                  };
                }
              } catch (wsError) {
                setConnectionStatus(function (prev) {
                  return _objectSpread(_objectSpread({}, prev), {}, {
                    websocket: isDev ? 'warning' : 'error'
                  });
                });
                console.warn('WebSocket connection error:', wsError.message);
              }
              _context.next = 6;
              break;
            case 5:
              _context.prev = 5;
              _t2 = _context["catch"](0);
              console.error('Failed to initialize App Builder:', _t2);
              antd__WEBPACK_IMPORTED_MODULE_6__/* .message */ .iU.error('Failed to initialize App Builder. Please try refreshing the page.');
              setError('Failed to initialize App Builder. Please try refreshing the page.');
            case 6:
              _context.prev = 6;
              // Always set loading to false after initialization
              setTimeout(function () {
                setLoading(false);
              }, 500);
              return _context.finish(6);
            case 7:
            case "end":
              return _context.stop();
          }
        }, _callee, null, [[0, 5, 6, 7], [1, 3]]);
      }));
      return function initApp() {
        return _ref.apply(this, arguments);
      };
    }();

    // Set a timeout to ensure loading state is not stuck
    var timer = setTimeout(function () {
      setLoading(false);
      console.log('Loading timeout triggered - forcing app to load');
    }, 3000);
    initApp();

    // Clean up the timer
    return function () {
      return clearTimeout(timer);
    };
  }, [dispatch, activeComponent]);

  // Handle component selection - memoized for better performance
  var handleComponentSelect = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(function (component) {
    setActiveComponent(component);
    dispatch((0,_redux_reducers_uiReducer__WEBPACK_IMPORTED_MODULE_8__/* .setCurrentView */ .tI)(component));
  }, [dispatch]);

  // Define tab items - memoized for better performance
  // This must be defined before any conditional returns to follow Rules of Hooks
  var tabItems = (0,react__WEBPACK_IMPORTED_MODULE_5__.useMemo)(function () {
    return [{
      key: 'projects',
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("span", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__/* .ProjectOutlined */ .KGW, null), " Projects"),
      children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(react__WEBPACK_IMPORTED_MODULE_5__.Suspense, {
        fallback: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
          style: {
            padding: '20px',
            textAlign: 'center'
          }
        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Spin */ .tK, {
          size: "large"
        }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
          style: {
            marginTop: '10px'
          }
        }, "Loading Projects..."))
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(ProjectManager, null))
    }, {
      key: 'components',
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("span", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__/* .AppstoreOutlined */ .rS9, null), " Component Builder"),
      children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_components_enhanced_SafeComponentWrapper__WEBPACK_IMPORTED_MODULE_17__/* ["default"] */ .A, {
        fallback: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", null, "Using basic component builder...")
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(react__WEBPACK_IMPORTED_MODULE_5__.Suspense, {
        fallback: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
          style: {
            padding: '20px',
            textAlign: 'center'
          }
        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Spin */ .tK, {
          size: "large"
        }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
          style: {
            marginTop: '10px'
          }
        }, "Loading Component Builder..."))
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(ComponentBuilder, null)))
    }, {
      key: 'layouts',
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("span", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__/* .LayoutOutlined */ .hy2, null), " Layout Designer"),
      children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_components_enhanced_SafeComponentWrapper__WEBPACK_IMPORTED_MODULE_17__/* ["default"] */ .A, {
        fallback: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", null, "Using basic layout designer...")
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(react__WEBPACK_IMPORTED_MODULE_5__.Suspense, {
        fallback: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
          style: {
            padding: '20px',
            textAlign: 'center'
          }
        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Spin */ .tK, {
          size: "large"
        }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
          style: {
            marginTop: '10px'
          }
        }, "Loading Layout Designer..."))
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(LayoutDesigner, null)))
    }, {
      key: 'themes',
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("span", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__/* .BgColorsOutlined */ .Ebl, null), " Theme Manager"),
      children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_components_enhanced_SafeComponentWrapper__WEBPACK_IMPORTED_MODULE_17__/* ["default"] */ .A, {
        fallback: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", null, "Using basic theme manager...")
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(react__WEBPACK_IMPORTED_MODULE_5__.Suspense, {
        fallback: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
          style: {
            padding: '20px',
            textAlign: 'center'
          }
        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Spin */ .tK, {
          size: "large"
        }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
          style: {
            marginTop: '10px'
          }
        }, "Loading Theme Manager..."))
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(ThemeManager, null)))
    }, {
      key: 'export',
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("span", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__/* .ExportOutlined */ .PZg, null), " Enhanced Export"),
      children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(react__WEBPACK_IMPORTED_MODULE_5__.Suspense, {
        fallback: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
          style: {
            padding: '20px',
            textAlign: 'center'
          }
        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Spin */ .tK, {
          size: "large"
        }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
          style: {
            marginTop: '10px'
          }
        }, "Loading Enhanced Code Exporter..."))
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(EnhancedCodeExporter, null))
    }, {
      key: 'performance',
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("span", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__/* .DashboardOutlined */ .zpd, null), " Performance"),
      children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(react__WEBPACK_IMPORTED_MODULE_5__.Suspense, {
        fallback: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
          style: {
            padding: '20px',
            textAlign: 'center'
          }
        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Spin */ .tK, {
          size: "large"
        }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
          style: {
            marginTop: '10px'
          }
        }, "Loading Performance Monitor..."))
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(PerformanceMonitor, null))
    }, {
      key: 'websocket',
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("span", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__/* .ApiOutlined */ .bfv, null), " WebSocket Manager"),
      children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(react__WEBPACK_IMPORTED_MODULE_5__.Suspense, {
        fallback: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
          style: {
            padding: '20px',
            textAlign: 'center'
          }
        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Spin */ .tK, {
          size: "large"
        }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
          style: {
            marginTop: '10px'
          }
        }, "Loading WebSocket Manager..."))
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(FixedWebSocketManager, null))
    }, {
      key: 'data',
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("span", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__/* .DatabaseOutlined */ .ose, null), " Data Management"),
      children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(react__WEBPACK_IMPORTED_MODULE_5__.Suspense, {
        fallback: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
          style: {
            padding: '20px',
            textAlign: 'center'
          }
        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Spin */ .tK, {
          size: "large"
        }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
          style: {
            marginTop: '10px'
          }
        }, "Loading Data Management..."))
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(DataManagementDemo, null))
    }, {
      key: 'testing',
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("span", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__/* .BugOutlined */ .NhG, null), " Testing Tools"),
      children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(react__WEBPACK_IMPORTED_MODULE_5__.Suspense, {
        fallback: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
          style: {
            padding: '20px',
            textAlign: 'center'
          }
        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Spin */ .tK, {
          size: "large"
        }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
          style: {
            marginTop: '10px'
          }
        }, "Loading Testing Tools..."))
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(TestingTools, null))
    }, {
      key: 'tutorial',
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("span", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__/* .QuestionCircleOutlined */ .faO, null), " Tutorial Assistant"),
      children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(react__WEBPACK_IMPORTED_MODULE_5__.Suspense, {
        fallback: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
          style: {
            padding: '20px',
            textAlign: 'center'
          }
        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Spin */ .tK, {
          size: "large"
        }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
          style: {
            marginTop: '10px'
          }
        }, "Loading Tutorial Assistant..."))
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
        style: {
          padding: '20px'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(Title, {
        level: 3
      }, "Tutorial Assistant"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(Paragraph, null, "Get help and learn how to use App Builder with our AI-powered tutorial assistant."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(TutorialAIPlugin, null)))
    }];
  }, []);
  if (loading) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(SpinAnimation, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
      style: {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '70vh',
        flexDirection: 'column'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
      style: {
        width: '50px',
        height: '50px',
        border: '5px solid #f3f3f3',
        borderTop: '5px solid #3498db',
        borderRadius: '50%',
        animation: 'spin 1s linear infinite'
      }
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(Title, {
      level: 3,
      style: {
        marginTop: '20px'
      }
    }, "Loading App Builder..."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
      style: {
        marginTop: '20px',
        textAlign: 'center'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
      style: {
        color: 'var(--color-text)',
        backgroundColor: 'var(--color-background-secondary)',
        padding: '8px 16px',
        borderRadius: '8px',
        marginBottom: '8px',
        border: '1px solid var(--color-border-light)'
      }
    }, "API Connection: ", ' ', /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("span", {
      style: {
        color: connectionStatus.api === 'connected' ? '#52c41a' : connectionStatus.api === 'error' ? '#ff4d4f' : connectionStatus.api === 'warning' ? '#faad14' : '#1890ff',
        fontWeight: '600'
      }
    }, connectionStatus.api === 'connected' ? 'Connected' : connectionStatus.api === 'error' ? 'Failed' : connectionStatus.api === 'warning' ? 'Limited (Mock)' : 'Checking...')), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
      style: {
        color: 'var(--color-text)',
        backgroundColor: 'var(--color-background-secondary)',
        padding: '8px 16px',
        borderRadius: '8px',
        border: '1px solid var(--color-border-light)'
      }
    }, "WebSocket Connection: ", ' ', /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("span", {
      style: {
        color: connectionStatus.websocket === 'connected' ? '#52c41a' : connectionStatus.websocket === 'error' ? '#ff4d4f' : connectionStatus.websocket === 'warning' ? '#faad14' : '#1890ff',
        fontWeight: '600'
      }
    }, connectionStatus.websocket === 'connected' ? 'Connected' : connectionStatus.websocket === 'error' ? 'Failed' : connectionStatus.websocket === 'warning' ? 'Limited (Mock)' : 'Checking...')), (connectionStatus.api === 'error' || connectionStatus.websocket === 'error') && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
      style: {
        marginTop: '20px',
        color: '#ff4d4f',
        backgroundColor: 'var(--color-background-secondary)',
        padding: '12px 16px',
        borderRadius: '8px',
        border: '1px solid #ff4d4f',
        fontWeight: '500'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("p", {
      style: {
        margin: '0 0 8px 0'
      }
    }, "Some connections failed. The app will continue to load with limited functionality."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("p", {
      style: {
        margin: 0
      }
    }, "Please ensure the backend server is running at http://localhost:8000")), (connectionStatus.api === 'warning' || connectionStatus.websocket === 'warning') && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
      style: {
        marginTop: '20px',
        color: '#faad14',
        backgroundColor: 'var(--color-background-secondary)',
        padding: '12px 16px',
        borderRadius: '8px',
        border: '1px solid #faad14',
        fontWeight: '500'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("p", {
      style: {
        margin: '0 0 8px 0'
      }
    }, "Some connections are in limited mode. The app will use mock data."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("p", {
      style: {
        margin: 0
      }
    }, "This is normal in development mode when the backend is not running.")))));
  }

  // Show error state if there's an error
  if (error) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
      style: {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '70vh',
        flexDirection: 'column'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
      style: {
        color: 'red',
        fontSize: '48px',
        marginBottom: '20px'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__/* .InfoCircleOutlined */ .rUN, null)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(Title, {
      level: 3,
      style: {
        color: 'red'
      }
    }, "Error"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(Paragraph, {
      style: {
        textAlign: 'center',
        maxWidth: '600px',
        marginTop: '20px'
      }
    }, error), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Button */ .$n, {
      type: "primary",
      style: {
        marginTop: '20px'
      },
      onClick: function onClick() {
        return window.location.reload();
      }
    }, "Refresh Page"));
  }
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(AppBuilderContainer, {
    className: "app-builder-enhanced"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(AppHeader, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(Title, {
    level: 2
  }, "App Builder Enhanced"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(Paragraph, null, "Create and manage your application components with ease"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
    style: {
      display: 'flex',
      gap: '10px',
      marginTop: '5px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Tooltip */ .m_, {
    title: connectionStatus.api === 'connected' ? 'API Connected' : connectionStatus.api === 'warning' ? 'API in Limited Mode (Mock)' : 'API Connection Failed'
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
    style: {
      display: 'inline-flex',
      alignItems: 'center',
      fontSize: '12px',
      color: connectionStatus.api === 'connected' ? '#52c41a' : connectionStatus.api === 'warning' ? '#faad14' : '#ff4d4f',
      backgroundColor: 'var(--color-background-secondary)',
      padding: '4px 8px',
      borderRadius: '12px',
      border: '1px solid var(--color-border-light)',
      fontWeight: '500'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
    style: {
      width: '8px',
      height: '8px',
      borderRadius: '50%',
      backgroundColor: connectionStatus.api === 'connected' ? '#52c41a' : connectionStatus.api === 'warning' ? '#faad14' : '#ff4d4f',
      marginRight: '6px',
      boxShadow: "0 0 4px ".concat(connectionStatus.api === 'connected' ? '#52c41a' : connectionStatus.api === 'warning' ? '#faad14' : '#ff4d4f')
    }
  }), "API")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Tooltip */ .m_, {
    title: connectionStatus.websocket === 'connected' ? 'WebSocket Connected' : connectionStatus.websocket === 'warning' ? 'WebSocket in Limited Mode (Mock)' : 'WebSocket Connection Failed'
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
    style: {
      display: 'inline-flex',
      alignItems: 'center',
      fontSize: '12px',
      color: connectionStatus.websocket === 'connected' ? '#52c41a' : connectionStatus.websocket === 'warning' ? '#faad14' : '#ff4d4f',
      backgroundColor: 'var(--color-background-secondary)',
      padding: '4px 8px',
      borderRadius: '12px',
      border: '1px solid var(--color-border-light)',
      fontWeight: '500'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
    style: {
      width: '8px',
      height: '8px',
      borderRadius: '50%',
      backgroundColor: connectionStatus.websocket === 'connected' ? '#52c41a' : connectionStatus.websocket === 'warning' ? '#faad14' : '#ff4d4f',
      marginRight: '6px',
      boxShadow: "0 0 4px ".concat(connectionStatus.websocket === 'connected' ? '#52c41a' : connectionStatus.websocket === 'warning' ? '#faad14' : '#ff4d4f')
    }
  }), "WebSocket")))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
    style: {
      display: 'flex',
      alignItems: 'center',
      gap: '12px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
    style: {
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
      padding: '8px 12px',
      background: 'rgba(255, 255, 255, 0.1)',
      borderRadius: '8px',
      border: '1px solid rgba(255, 255, 255, 0.2)'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Tooltip */ .m_, {
    title: "Real-time Preview"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Switch */ .dO, {
    size: "small",
    checked: previewSettings.realTimeEnabled,
    onChange: function onChange(checked) {
      return handlePreviewSettingChange('realTimeEnabled', checked);
    },
    checkedChildren: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__/* .EyeOutlined */ .Om2, null),
    unCheckedChildren: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__/* .EyeOutlined */ .Om2, null)
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Tooltip */ .m_, {
    title: "Collaboration"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Switch */ .dO, {
    size: "small",
    checked: previewSettings.collaborationEnabled && websocketConnected,
    onChange: function onChange(checked) {
      return handlePreviewSettingChange('collaborationEnabled', checked);
    },
    disabled: !websocketConnected,
    checkedChildren: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__/* .SyncOutlined */ .OmY, null),
    unCheckedChildren: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__/* .SyncOutlined */ .OmY, null)
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Tooltip */ .m_, {
    title: "Performance Monitoring"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Switch */ .dO, {
    size: "small",
    checked: previewSettings.performanceMonitoring,
    onChange: function onChange(checked) {
      return handlePreviewSettingChange('performanceMonitoring', checked);
    },
    checkedChildren: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__/* .ThunderboltOutlined */ .CwG, null),
    unCheckedChildren: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__/* .ThunderboltOutlined */ .CwG, null)
  })), previewSettings.collaborationEnabled && collaborativePreview.isConnected && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Badge */ .Ex, {
    count: collaborativePreview.collaborators.length,
    showZero: false,
    style: {
      backgroundColor: '#52c41a'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Tooltip */ .m_, {
    title: "Active collaborators"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
    style: {
      width: '20px',
      height: '20px',
      borderRadius: '50%',
      background: '#52c41a',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__/* .SyncOutlined */ .OmY, {
    style: {
      fontSize: '10px',
      color: 'white'
    }
  }))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Tooltip */ .m_, {
    title: "Open Tutorial Assistant"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Button */ .$n, {
    type: "default",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__/* .QuestionCircleOutlined */ .faO, null),
    style: {
      marginRight: '10px'
    },
    onClick: function onClick() {
      return setShowTutorialModal(true);
    }
  }, "Help")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Tooltip */ .m_, {
    title: "Refresh connections"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Button */ .$n, {
    type: "default",
    onClick: function onClick() {
      return window.location.reload();
    }
  }, "Refresh")))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(WelcomeCard, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Row */ .fI, {
    gutter: [24, 24]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Col */ .fv, {
    xs: 24,
    md: 16
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(Title, {
    level: 4
  }, "Welcome to App Builder Enhanced"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(Paragraph, null, "This tool helps you create and manage your application components with ease. Use the tabs below to navigate between different features."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Button */ .$n, {
    type: "primary",
    size: "large",
    onClick: function onClick() {
      return handleComponentSelect('components');
    }
  }, "Start Building")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Col */ .fv, {
    xs: 24,
    md: 8
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
    style: {
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("img", {
    src: "/static/images/app-builder-logo.svg",
    alt: "App Builder Logo",
    style: {
      maxWidth: '100%',
      height: 'auto'
    },
    onError: function onError(e) {
      e.target.onerror = null;
      e.target.style.display = 'none';
    }
  }))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(StyledTabs, {
    activeKey: activeComponent,
    onChange: handleComponentSelect,
    type: "card",
    size: "large",
    items: tabItems
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Card */ .Zp, {
    title: "Getting Started",
    style: {
      marginTop: '24px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Row */ .fI, {
    gutter: [24, 24]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Col */ .fv, {
    xs: 24,
    md: 6
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(ComponentCard, {
    title: "Step 1",
    active: activeComponent === 'components',
    onClick: function onClick() {
      return handleComponentSelect('components');
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(ComponentIcon, {
    active: activeComponent === 'components'
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__/* .AppstoreOutlined */ .rS9, null)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(Paragraph, null, "Use the Component Builder to create UI components"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Col */ .fv, {
    xs: 24,
    md: 6
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(ComponentCard, {
    title: "Step 2",
    active: activeComponent === 'layouts',
    onClick: function onClick() {
      return handleComponentSelect('layouts');
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(ComponentIcon, {
    active: activeComponent === 'layouts'
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__/* .LayoutOutlined */ .hy2, null)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(Paragraph, null, "Design your layout with the Layout Designer"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Col */ .fv, {
    xs: 24,
    md: 6
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(ComponentCard, {
    title: "Step 3",
    active: activeComponent === 'themes',
    onClick: function onClick() {
      return handleComponentSelect('themes');
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(ComponentIcon, {
    active: activeComponent === 'themes'
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__/* .BgColorsOutlined */ .Ebl, null)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(Paragraph, null, "Customize your theme with the Theme Manager"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Col */ .fv, {
    xs: 24,
    md: 6
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(ComponentCard, {
    title: "Step 4",
    active: activeComponent === 'websocket',
    onClick: function onClick() {
      return handleComponentSelect('websocket');
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(ComponentIcon, {
    active: activeComponent === 'websocket'
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__/* .ApiOutlined */ .bfv, null)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(Paragraph, null, "Set up real-time communication with WebSocket Manager"))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Card */ .Zp, {
    title: "Advanced Features",
    style: {
      marginTop: '24px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Row */ .fI, {
    gutter: [24, 24]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Col */ .fv, {
    xs: 24,
    md: 6
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(ComponentCard, {
    title: "Tutorial Assistant",
    active: activeComponent === 'tutorial',
    onClick: function onClick() {
      return handleComponentSelect('tutorial');
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(ComponentIcon, {
    active: activeComponent === 'tutorial'
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__/* .QuestionCircleOutlined */ .faO, null)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(Paragraph, null, "Interactive tutorials and context-aware help system"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Col */ .fv, {
    xs: 24,
    md: 6
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(ComponentCard, {
    title: "Testing Tools",
    active: activeComponent === 'testing',
    onClick: function onClick() {
      return handleComponentSelect('testing');
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(ComponentIcon, {
    active: activeComponent === 'testing'
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__/* .BugOutlined */ .NhG, null)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(Paragraph, null, "Component testing, validation, and accessibility checks"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Col */ .fv, {
    xs: 24,
    md: 6
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(ComponentCard, {
    title: "Data Management",
    active: activeComponent === 'data',
    onClick: function onClick() {
      return handleComponentSelect('data');
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(ComponentIcon, {
    active: activeComponent === 'data'
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__/* .DatabaseOutlined */ .ose, null)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(Paragraph, null, "Data binding, state management, and flow visualization"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Col */ .fv, {
    xs: 24,
    md: 6
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(ComponentCard, {
    title: "Performance Monitor",
    active: activeComponent === 'performance',
    onClick: function onClick() {
      return handleComponentSelect('performance');
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(ComponentIcon, {
    active: activeComponent === 'performance'
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__/* .DashboardOutlined */ .zpd, null)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(Paragraph, null, "Bundle size tracking and optimization suggestions")))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Row */ .fI, {
    gutter: [24, 24],
    style: {
      marginTop: '24px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Col */ .fv, {
    xs: 24,
    md: 6
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(ComponentCard, {
    title: "Enhanced Export",
    active: activeComponent === 'export',
    onClick: function onClick() {
      return handleComponentSelect('export');
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(ComponentIcon, {
    active: activeComponent === 'export'
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__/* .ExportOutlined */ .PZg, null)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(Paragraph, null, "Multi-framework export with TypeScript generation"))))), previewSettings.performanceMonitoring && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_components_performance__WEBPACK_IMPORTED_MODULE_16__/* .PerformanceMonitor */ .r3, {
    renderTime: realTimePreview.isUpdating ? 16 : 8,
    frameRate: 60,
    memoryUsage: performance.memory ? Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) : 0,
    componentCount: components.length,
    visibleComponents: components.length,
    cacheSize: 0,
    updateFrequency: realTimePreview.hasPendingUpdates ? 30 : 0,
    floating: true,
    showAlerts: true,
    optimizationsEnabled: previewSettings.realTimeEnabled,
    onToggleOptimizations: function onToggleOptimizations(enabled) {
      return handlePreviewSettingChange('realTimeEnabled', enabled);
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .FloatButton */ .ff, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_10__/* .QuestionCircleOutlined */ .faO, null),
    type: "primary",
    style: {
      right: previewSettings.performanceMonitoring ? 340 : 24,
      bottom: 24
    },
    onClick: function onClick() {
      return setShowTutorialModal(true);
    },
    tooltip: "Tutorial Assistant"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Modal */ .aF, {
    title: "Tutorial Assistant",
    open: showTutorialModal,
    onCancel: function onCancel() {
      return setShowTutorialModal(false);
    },
    footer: null,
    width: 800,
    style: {
      top: 20
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
    style: {
      padding: '10px 0'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(Paragraph, null, "Get help and learn how to use App Builder with our AI-powered tutorial assistant."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(react__WEBPACK_IMPORTED_MODULE_5__.Suspense, {
    fallback: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
      style: {
        padding: '20px',
        textAlign: 'center'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Spin */ .tK, {
      size: "large"
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
      style: {
        marginTop: '10px'
      }
    }, "Loading Tutorial Assistant..."))
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(TutorialAIPlugin, null)))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AppBuilderEnhanced);

/***/ }),

/***/ 85331:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   tI: () => (/* binding */ setCurrentView)
/* harmony export */ });
/* unused harmony exports TOGGLE_SIDEBAR, SET_CURRENT_VIEW, TOGGLE_PREVIEW_MODE, UI_LOADING_START, UI_LOADING_COMPLETE, toggleSidebar, togglePreviewMode, startLoading, completeLoading */
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);

function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * UI Reducer
 *
 * This reducer handles UI state, including sidebar, current view, and preview mode.
 */

// Action types
var TOGGLE_SIDEBAR = 'TOGGLE_SIDEBAR';
var SET_CURRENT_VIEW = 'SET_CURRENT_VIEW';
var TOGGLE_PREVIEW_MODE = 'TOGGLE_PREVIEW_MODE';
var UI_LOADING_START = 'UI_LOADING_START';
var UI_LOADING_COMPLETE = 'UI_LOADING_COMPLETE';

// Initial state
var initialState = {
  sidebarOpen: true,
  currentView: 'components',
  previewMode: false,
  loading: false
};

/**
 * UI reducer
 * @param {Object} state - Current state
 * @param {Object} action - Action object
 * @returns {Object} New state
 */
var uiReducer = function uiReducer() {
  var state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : initialState;
  var action = arguments.length > 1 ? arguments[1] : undefined;
  switch (action.type) {
    case TOGGLE_SIDEBAR:
      return _objectSpread(_objectSpread({}, state), {}, {
        sidebarOpen: !state.sidebarOpen
      });
    case SET_CURRENT_VIEW:
      return _objectSpread(_objectSpread({}, state), {}, {
        currentView: action.payload
      });
    case TOGGLE_PREVIEW_MODE:
      return _objectSpread(_objectSpread({}, state), {}, {
        previewMode: !state.previewMode
      });
    case UI_LOADING_START:
      return _objectSpread(_objectSpread({}, state), {}, {
        loading: true
      });
    case UI_LOADING_COMPLETE:
      return _objectSpread(_objectSpread({}, state), {}, {
        loading: false
      });
    default:
      return state;
  }
};

// Action creators
var toggleSidebar = function toggleSidebar() {
  return {
    type: TOGGLE_SIDEBAR
  };
};
var setCurrentView = function setCurrentView(view) {
  return {
    type: SET_CURRENT_VIEW,
    payload: view
  };
};
var togglePreviewMode = function togglePreviewMode() {
  return {
    type: TOGGLE_PREVIEW_MODE
  };
};
var startLoading = function startLoading() {
  return {
    type: UI_LOADING_START
  };
};
var completeLoading = function completeLoading() {
  return {
    type: UI_LOADING_COMPLETE
  };
};
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ((/* unused pure expression or super */ null && (uiReducer)));

/***/ })

}]);