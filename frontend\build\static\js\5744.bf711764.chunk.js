"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[5744],{

/***/ 36526:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AU: () => (/* binding */ generateViteConfig),
/* harmony export */   GK: () => (/* binding */ generateGitHubActions),
/* harmony export */   Qd: () => (/* binding */ generatePackageJson),
/* harmony export */   rR: () => (/* binding */ generateESLintConfig),
/* harmony export */   uc: () => (/* binding */ generateReadme),
/* harmony export */   v9: () => (/* binding */ generateTSConfig),
/* harmony export */   wN: () => (/* binding */ generateDockerfile),
/* harmony export */   yF: () => (/* binding */ generateDockerCompose)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(64467);


function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * Project structure generators and configuration file generators
 * This file contains functions to generate complete project structures with build configs, package.json, etc.
 */

/**
 * Generate package.json for different project types
 */
var generatePackageJson = function generatePackageJson(projectType) {
  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var _options$packageManag = options.packageManager,
    packageManager = _options$packageManag === void 0 ? 'npm' : _options$packageManag,
    _options$typescript = options.typescript,
    typescript = _options$typescript === void 0 ? false : _options$typescript,
    _options$styleFramewo = options.styleFramework,
    styleFramework = _options$styleFramewo === void 0 ? 'styled-components' : _options$styleFramewo;
  var basePackage = {
    name: 'app-builder-generated-app',
    version: '0.1.0',
    "private": true,
    description: 'Generated by App Builder',
    author: 'App Builder',
    license: 'MIT'
  };
  switch (projectType) {
    case 'react-app':
      return JSON.stringify(_objectSpread(_objectSpread({}, basePackage), {}, {
        scripts: {
          start: 'react-scripts start',
          build: 'react-scripts build',
          test: 'react-scripts test',
          eject: 'react-scripts eject',
          lint: 'eslint src --ext .js,.jsx,.ts,.tsx',
          'lint:fix': 'eslint src --ext .js,.jsx,.ts,.tsx --fix'
        },
        dependencies: _objectSpread(_objectSpread(_objectSpread(_objectSpread({
          react: '^18.2.0',
          'react-dom': '^18.2.0',
          'react-scripts': '5.0.1',
          'web-vitals': '^2.1.4'
        }, styleFramework === 'styled-components' && {
          'styled-components': '^5.3.9'
        }), styleFramework === 'emotion' && {
          '@emotion/react': '^11.10.6',
          '@emotion/styled': '^11.10.6'
        }), styleFramework === 'tailwind' && {
          'tailwindcss': '^3.2.7'
        }), !typescript && {
          'prop-types': '^15.8.1'
        }),
        devDependencies: _objectSpread(_objectSpread({
          '@testing-library/jest-dom': '^5.16.4',
          '@testing-library/react': '^13.4.0',
          '@testing-library/user-event': '^13.5.0'
        }, typescript && {
          '@types/react': '^18.0.28',
          '@types/react-dom': '^18.0.11',
          '@types/node': '^16.18.23',
          typescript: '^4.9.5'
        }), {}, {
          eslint: '^8.36.0',
          'eslint-plugin-react': '^7.32.2',
          'eslint-plugin-react-hooks': '^4.6.0'
        }),
        browserslist: {
          production: ['>0.2%', 'not dead', 'not op_mini all'],
          development: ['last 1 chrome version', 'last 1 firefox version', 'last 1 safari version']
        }
      }), null, 2);
    case 'nextjs-app':
      return JSON.stringify(_objectSpread(_objectSpread({}, basePackage), {}, {
        scripts: {
          dev: 'next dev',
          build: 'next build',
          start: 'next start',
          lint: 'next lint',
          "export": 'next export'
        },
        dependencies: _objectSpread(_objectSpread({
          next: '^13.2.4',
          react: '^18.2.0',
          'react-dom': '^18.2.0'
        }, styleFramework === 'styled-components' && {
          'styled-components': '^5.3.9'
        }), styleFramework === 'tailwind' && {
          'tailwindcss': '^3.2.7',
          'autoprefixer': '^10.4.14',
          'postcss': '^8.4.21'
        }),
        devDependencies: _objectSpread(_objectSpread({}, typescript && {
          '@types/node': '^18.15.3',
          '@types/react': '^18.0.28',
          '@types/react-dom': '^18.0.11',
          typescript: '^5.0.2'
        }), {}, {
          eslint: '^8.36.0',
          'eslint-config-next': '^13.2.4'
        })
      }), null, 2);
    case 'vue-app':
      return JSON.stringify(_objectSpread(_objectSpread({}, basePackage), {}, {
        scripts: {
          serve: 'vue-cli-service serve',
          build: 'vue-cli-service build',
          test: 'vue-cli-service test:unit',
          lint: 'vue-cli-service lint'
        },
        dependencies: _objectSpread({
          'core-js': '^3.8.3',
          vue: '^3.2.13'
        }, typescript && {
          'vue-tsc': '^1.2.0'
        }),
        devDependencies: _objectSpread({
          '@babel/core': '^7.12.16',
          '@babel/eslint-parser': '^7.12.16',
          '@vue/cli-plugin-babel': '~5.0.0',
          '@vue/cli-plugin-eslint': '~5.0.0',
          '@vue/cli-service': '~5.0.0',
          eslint: '^7.32.0',
          'eslint-plugin-vue': '^8.0.3'
        }, typescript && {
          '@vue/cli-plugin-typescript': '~5.0.0',
          '@vue/eslint-config-typescript': '^9.1.0',
          typescript: '~4.5.5'
        })
      }), null, 2);
    case 'angular-app':
      return JSON.stringify(_objectSpread(_objectSpread({}, basePackage), {}, {
        scripts: {
          ng: 'ng',
          start: 'ng serve',
          build: 'ng build',
          watch: 'ng build --watch --configuration development',
          test: 'ng test'
        },
        dependencies: {
          '@angular/animations': '^15.2.0',
          '@angular/common': '^15.2.0',
          '@angular/compiler': '^15.2.0',
          '@angular/core': '^15.2.0',
          '@angular/forms': '^15.2.0',
          '@angular/platform-browser': '^15.2.0',
          '@angular/platform-browser-dynamic': '^15.2.0',
          '@angular/router': '^15.2.0',
          rxjs: '~7.8.0',
          tslib: '^2.3.0',
          'zone.js': '~0.12.0'
        },
        devDependencies: {
          '@angular-devkit/build-angular': '^15.2.4',
          '@angular/cli': '~15.2.4',
          '@angular/compiler-cli': '^15.2.0',
          '@types/jasmine': '~4.3.0',
          '@types/node': '^12.11.1',
          jasmine: '~4.5.0',
          'karma': '~6.4.0',
          'karma-chrome-launcher': '~3.1.0',
          'karma-coverage': '~2.2.0',
          'karma-jasmine': '~5.1.0',
          'karma-jasmine-html-reporter': '~2.0.0',
          typescript: '~4.9.4'
        }
      }), null, 2);
    default:
      return JSON.stringify(basePackage, null, 2);
  }
};

/**
 * Generate README.md file
 */
var generateReadme = function generateReadme(framework) {
  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var _options$projectName = options.projectName,
    projectName = _options$projectName === void 0 ? 'Generated App' : _options$projectName,
    _options$typescript2 = options.typescript,
    typescript = _options$typescript2 === void 0 ? false : _options$typescript2;
  return "# ".concat(projectName, "\n\nThis project was generated using App Builder.\n\n## Framework: ").concat(framework).concat(typescript ? ' with TypeScript' : '', "\n\n## Getting Started\n\n### Prerequisites\n\n- Node.js (version 14 or higher)\n- npm, yarn, or pnpm\n\n### Installation\n\n```bash\n# Install dependencies\nnpm install\n\n# Or with yarn\nyarn install\n\n# Or with pnpm\npnpm install\n```\n\n### Development\n\n```bash\n# Start development server\nnpm start\n\n# Or with yarn\nyarn start\n\n# Or with pnpm\npnpm start\n```\n\n### Building for Production\n\n```bash\n# Build for production\nnpm run build\n\n# Or with yarn\nyarn build\n\n# Or with pnpm\npnpm build\n```\n\n## Project Structure\n\n```\nsrc/\n\u251C\u2500\u2500 components/     # Reusable components\n\u251C\u2500\u2500 pages/         # Page components\n\u251C\u2500\u2500 styles/        # Stylesheets\n\u251C\u2500\u2500 utils/         # Utility functions\n\u2514\u2500\u2500 App.").concat(typescript ? 'tsx' : 'jsx', "        # Main application component\n```\n\n## Features\n\n- \u2705 Modern ").concat(framework, " application\n- \u2705 Responsive design\n- \u2705 Accessibility features\n").concat(typescript ? '- ✅ TypeScript support' : '', "\n- \u2705 ESLint configuration\n- \u2705 Production-ready build\n\n## Generated by App Builder\n\nThis application was automatically generated by App Builder. You can customize and extend it according to your needs.\n\n## Learn More\n\n- [").concat(framework, " Documentation](").concat(getFrameworkDocsUrl(framework), ")\n- [App Builder Documentation](https://app-builder.example.com/docs)\n\n## License\n\nMIT\n");
};

/**
 * Generate TypeScript configuration
 */
var generateTSConfig = function generateTSConfig() {
  return JSON.stringify({
    compilerOptions: {
      target: 'es5',
      lib: ['dom', 'dom.iterable', 'es6'],
      allowJs: true,
      skipLibCheck: true,
      esModuleInterop: true,
      allowSyntheticDefaultImports: true,
      strict: true,
      forceConsistentCasingInFileNames: true,
      noFallthroughCasesInSwitch: true,
      module: 'esnext',
      moduleResolution: 'node',
      resolveJsonModule: true,
      isolatedModules: true,
      noEmit: true,
      jsx: 'react-jsx'
    },
    include: ['src'],
    exclude: ['node_modules']
  }, null, 2);
};

/**
 * Generate ESLint configuration
 */
var generateESLintConfig = function generateESLintConfig(framework) {
  var typescript = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
  var baseConfig = {
    env: {
      browser: true,
      es2021: true,
      node: true
    },
    "extends": ['eslint:recommended'],
    parserOptions: {
      ecmaVersion: 'latest',
      sourceType: 'module'
    },
    rules: {
      'no-unused-vars': 'warn',
      'no-console': 'warn',
      'prefer-const': 'error'
    }
  };
  if (framework === 'react') {
    baseConfig["extends"].push('plugin:react/recommended', 'plugin:react-hooks/recommended');
    baseConfig.plugins = ['react', 'react-hooks'];
    baseConfig.parserOptions.ecmaFeatures = {
      jsx: true
    };
    baseConfig.settings = {
      react: {
        version: 'detect'
      }
    };
  }
  if (typescript) {
    baseConfig["extends"].push('@typescript-eslint/recommended');
    baseConfig.parser = '@typescript-eslint/parser';
    baseConfig.plugins = [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(baseConfig.plugins || []), ['@typescript-eslint']);
  }
  return JSON.stringify(baseConfig, null, 2);
};

/**
 * Generate Dockerfile
 */
var generateDockerfile = function generateDockerfile(framework) {
  return "# Use official Node.js runtime as base image\nFROM node:18-alpine\n\n# Set working directory\nWORKDIR /app\n\n# Copy package files\nCOPY package*.json ./\n\n# Install dependencies\nRUN npm ci --only=production\n\n# Copy source code\nCOPY . .\n\n# Build the application\nRUN npm run build\n\n# Expose port\nEXPOSE 3000\n\n# Start the application\nCMD [\"npm\", \"start\"]\n";
};

/**
 * Generate Docker Compose file
 */
var generateDockerCompose = function generateDockerCompose() {
  return "version: '3.8'\n\nservices:\n  app:\n    build: .\n    ports:\n      - \"3000:3000\"\n    environment:\n      - NODE_ENV=production\n    volumes:\n      - .:/app\n      - /app/node_modules\n    restart: unless-stopped\n\n  nginx:\n    image: nginx:alpine\n    ports:\n      - \"80:80\"\n    volumes:\n      - ./nginx.conf:/etc/nginx/nginx.conf\n    depends_on:\n      - app\n    restart: unless-stopped\n";
};

/**
 * Generate GitHub Actions workflow
 */
var generateGitHubActions = function generateGitHubActions(framework) {
  return "name: CI/CD Pipeline\n\non:\n  push:\n    branches: [ main, develop ]\n  pull_request:\n    branches: [ main ]\n\njobs:\n  test:\n    runs-on: ubuntu-latest\n    \n    strategy:\n      matrix:\n        node-version: [16.x, 18.x]\n    \n    steps:\n    - uses: actions/checkout@v3\n    \n    - name: Use Node.js ${{ matrix.node-version }}\n      uses: actions/setup-node@v3\n      with:\n        node-version: ${{ matrix.node-version }}\n        cache: 'npm'\n    \n    - name: Install dependencies\n      run: npm ci\n    \n    - name: Run tests\n      run: npm test\n    \n    - name: Build application\n      run: npm run build\n\n  deploy:\n    needs: test\n    runs-on: ubuntu-latest\n    if: github.ref == 'refs/heads/main'\n    \n    steps:\n    - uses: actions/checkout@v3\n    \n    - name: Setup Node.js\n      uses: actions/setup-node@v3\n      with:\n        node-version: '18.x'\n        cache: 'npm'\n    \n    - name: Install dependencies\n      run: npm ci\n    \n    - name: Build application\n      run: npm run build\n    \n    - name: Deploy to production\n      run: echo \"Deploy to your hosting platform\"\n";
};

/**
 * Generate Vite configuration
 */
var generateViteConfig = function generateViteConfig(framework) {
  var typescript = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
  var ext = typescript ? 'ts' : 'js';
  return "import { defineConfig } from 'vite'\n".concat(framework === 'react' ? "import react from '@vitejs/plugin-react'" : '', "\n").concat(framework === 'vue' ? "import vue from '@vitejs/plugin-vue'" : '', "\n\nexport default defineConfig({\n  plugins: [").concat(framework === 'react' ? 'react()' : framework === 'vue' ? 'vue()' : '', "],\n  server: {\n    port: 3000,\n    open: true\n  },\n  build: {\n    outDir: 'dist',\n    sourcemap: true\n  }\n})\n");
};

// Helper functions
var getFrameworkDocsUrl = function getFrameworkDocsUrl(framework) {
  var urls = {
    'React': 'https://reactjs.org/docs',
    'Vue': 'https://vuejs.org/guide/',
    'Angular': 'https://angular.io/docs',
    'Svelte': 'https://svelte.dev/docs',
    'Next.js': 'https://nextjs.org/docs'
  };
  return urls[framework] || 'https://developer.mozilla.org/';
};

/***/ }),

/***/ 99131:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   HM: () => (/* binding */ generateVueCode),
/* harmony export */   Pp: () => (/* binding */ generateNextJSCode),
/* harmony export */   Zh: () => (/* binding */ generateReactNativeCode),
/* harmony export */   _k: () => (/* binding */ generateSvelteCode),
/* harmony export */   hi: () => (/* binding */ generateAngularCode)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);


/**
 * Enhanced code generators for multiple frameworks and project structures
 * This file contains generators for Vue, Angular, Svelte, Next.js, React Native, Flutter, etc.
 */

/**
 * Generate Vue.js code
 */
var generateVueCode = function generateVueCode(appData) {
  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var components = appData.components,
    layouts = appData.layouts,
    styles = appData.styles,
    data = appData.data;
  var typescript = options.typescript,
    includeAccessibility = options.includeAccessibility,
    projectStructure = options.projectStructure;
  if (projectStructure === 'full-project') {
    return generateVueProject(appData, options);
  }
  var fileExtension = typescript ? 'vue' : 'vue';
  var vueCode = "<template>\n  <div class=\"app\"".concat(includeAccessibility ? ' role="main"' : '', ">");

  // Generate template structure
  layouts.forEach(function (layout) {
    var layoutName = layout.name || layout.type || 'layout';
    vueCode += "\n    <div class=\"".concat(layoutName, "-container\">");
    if (layout.components && layout.components.length > 0) {
      layout.components.forEach(function (componentId) {
        var component = components.find(function (c) {
          return c.id === componentId;
        });
        if (component) {
          vueCode += "\n      <".concat(component.type.toLowerCase());
          Object.entries(component.props || {}).forEach(function (_ref) {
            var _ref2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_ref, 2),
              prop = _ref2[0],
              value = _ref2[1];
            if (typeof value === 'string') {
              vueCode += " ".concat(prop, "=\"").concat(value, "\"");
            } else {
              vueCode += " :".concat(prop, "=\"").concat(JSON.stringify(value), "\"");
            }
          });
          vueCode += " />";
        }
      });
    }
    vueCode += "\n    </div>";
  });
  vueCode += "\n  </div>\n</template>\n\n<script".concat(typescript ? ' lang="ts"' : '', ">\nimport { defineComponent, ref, reactive } from 'vue';\n\nexport default defineComponent({\n  name: 'App',\n  setup() {");

  // Add reactive data
  if (data && Object.keys(data).length > 0) {
    Object.entries(data).forEach(function (_ref3) {
      var _ref4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_ref3, 2),
        key = _ref4[0],
        value = _ref4[1];
      vueCode += "\n    const ".concat(key, " = ref(").concat(JSON.stringify(value), ");");
    });
  }
  vueCode += "\n    \n    return {";
  if (data && Object.keys(data).length > 0) {
    Object.keys(data).forEach(function (key) {
      vueCode += "\n      ".concat(key, ",");
    });
  }
  vueCode += "\n    };\n  }\n});\n</script>\n\n<style scoped>\n.app {\n  min-height: 100vh;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\n  line-height: 1.6;\n  color: #333;\n}\n\n".concat(generateVueStyles(styles, components, layouts), "\n</style>");
  return vueCode;
};

/**
 * Generate Angular code
 */
var generateAngularCode = function generateAngularCode(appData) {
  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var components = appData.components,
    layouts = appData.layouts,
    styles = appData.styles,
    data = appData.data;
  var _options$typescript = options.typescript,
    typescript = _options$typescript === void 0 ? true : _options$typescript,
    includeAccessibility = options.includeAccessibility,
    projectStructure = options.projectStructure;
  if (projectStructure === 'full-project') {
    return generateAngularProject(appData, options);
  }

  // Component TypeScript
  var componentCode = "import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-root',\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.css']\n})\nexport class AppComponent {\n  title = 'Generated App';";

  // Add component properties
  if (data && Object.keys(data).length > 0) {
    Object.entries(data).forEach(function (_ref5) {
      var _ref6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_ref5, 2),
        key = _ref6[0],
        value = _ref6[1];
      componentCode += "\n  ".concat(key, " = ").concat(JSON.stringify(value), ";");
    });
  }
  componentCode += "\n}";

  // Template HTML
  var templateCode = "<div class=\"app\"".concat(includeAccessibility ? ' role="main"' : '', ">");
  layouts.forEach(function (layout) {
    var layoutName = layout.name || layout.type || 'layout';
    templateCode += "\n  <div class=\"".concat(layoutName, "-container\">");
    if (layout.components && layout.components.length > 0) {
      layout.components.forEach(function (componentId) {
        var component = components.find(function (c) {
          return c.id === componentId;
        });
        if (component) {
          templateCode += "\n    <app-".concat(component.type.toLowerCase());
          Object.entries(component.props || {}).forEach(function (_ref7) {
            var _ref8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_ref7, 2),
              prop = _ref8[0],
              value = _ref8[1];
            if (typeof value === 'string') {
              templateCode += " ".concat(prop, "=\"").concat(value, "\"");
            } else {
              templateCode += " [".concat(prop, "]=\"").concat(JSON.stringify(value), "\"");
            }
          });
          templateCode += "></app-".concat(component.type.toLowerCase(), ">");
        }
      });
    }
    templateCode += "\n  </div>";
  });
  templateCode += "\n</div>";

  // Styles CSS
  var stylesCode = ".app {\n  min-height: 100vh;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\n  line-height: 1.6;\n  color: #333;\n}\n\n".concat(generateAngularStyles(styles, components, layouts));
  return {
    'app.component.ts': componentCode,
    'app.component.html': templateCode,
    'app.component.css': stylesCode
  };
};

/**
 * Generate Svelte code
 */
var generateSvelteCode = function generateSvelteCode(appData) {
  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var components = appData.components,
    layouts = appData.layouts,
    styles = appData.styles,
    data = appData.data;
  var typescript = options.typescript,
    includeAccessibility = options.includeAccessibility;
  var svelteCode = "<script".concat(typescript ? ' lang="ts"' : '', ">");

  // Add reactive data
  if (data && Object.keys(data).length > 0) {
    Object.entries(data).forEach(function (_ref9) {
      var _ref0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_ref9, 2),
        key = _ref0[0],
        value = _ref0[1];
      svelteCode += "\n  let ".concat(key, " = ").concat(JSON.stringify(value), ";");
    });
  }
  svelteCode += "\n</script>\n\n<div class=\"app\"".concat(includeAccessibility ? ' role="main"' : '', ">");

  // Generate template structure
  layouts.forEach(function (layout) {
    var layoutName = layout.name || layout.type || 'layout';
    svelteCode += "\n  <div class=\"".concat(layoutName, "-container\">");
    if (layout.components && layout.components.length > 0) {
      layout.components.forEach(function (componentId) {
        var component = components.find(function (c) {
          return c.id === componentId;
        });
        if (component) {
          svelteCode += "\n    <".concat(component.type);
          Object.entries(component.props || {}).forEach(function (_ref1) {
            var _ref10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_ref1, 2),
              prop = _ref10[0],
              value = _ref10[1];
            if (typeof value === 'string') {
              svelteCode += " ".concat(prop, "=\"").concat(value, "\"");
            } else {
              svelteCode += " ".concat(prop, "={").concat(JSON.stringify(value), "}");
            }
          });
          svelteCode += " />";
        }
      });
    }
    svelteCode += "\n  </div>";
  });
  svelteCode += "\n</div>\n\n<style>\n  .app {\n    min-height: 100vh;\n    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\n    line-height: 1.6;\n    color: #333;\n  }\n\n  ".concat(generateSvelteStyles(styles, components, layouts), "\n</style>");
  return svelteCode;
};

/**
 * Generate Next.js code
 */
var generateNextJSCode = function generateNextJSCode(appData) {
  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var components = appData.components,
    layouts = appData.layouts,
    styles = appData.styles,
    data = appData.data;
  var typescript = options.typescript,
    includeAccessibility = options.includeAccessibility,
    projectStructure = options.projectStructure;
  if (projectStructure === 'full-project') {
    return generateNextJSProject(appData, options);
  }
  var fileExtension = typescript ? 'tsx' : 'jsx';
  var nextCode = "import React from 'react';\nimport Head from 'next/head';\nimport styles from '../styles/Home.module.css';\n\nexport default function Home() {\n  return (\n    <div className={styles.container}>\n      <Head>\n        <title>Generated App</title>\n        <meta name=\"description\" content=\"Generated by App Builder\" />\n        <link rel=\"icon\" href=\"/favicon.ico\" />\n      </Head>\n\n      <main className={styles.main}".concat(includeAccessibility ? ' role="main"' : '', ">");

  // Generate layout structure
  layouts.forEach(function (layout) {
    var layoutName = layout.name || layout.type || 'layout';
    nextCode += "\n        <div className={styles.".concat(layoutName, "Container}>");
    if (layout.components && layout.components.length > 0) {
      layout.components.forEach(function (componentId) {
        var component = components.find(function (c) {
          return c.id === componentId;
        });
        if (component) {
          nextCode += "\n          <".concat(component.type);
          Object.entries(component.props || {}).forEach(function (_ref11) {
            var _ref12 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_ref11, 2),
              prop = _ref12[0],
              value = _ref12[1];
            if (typeof value === 'string') {
              nextCode += " ".concat(prop, "=\"").concat(value, "\"");
            } else {
              nextCode += " ".concat(prop, "={").concat(JSON.stringify(value), "}");
            }
          });
          nextCode += " />";
        }
      });
    }
    nextCode += "\n        </div>";
  });
  nextCode += "\n      </main>\n    </div>\n  );\n}";
  return (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)((0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)((0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)({}, "pages/index.".concat(fileExtension), nextCode), 'styles/Home.module.css', generateNextJSStyles(styles, components, layouts)), 'package.json', generatePackageJson('nextjs-app', options));
};

/**
 * Generate React Native code
 */
var generateReactNativeCode = function generateReactNativeCode(appData) {
  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var components = appData.components,
    layouts = appData.layouts,
    styles = appData.styles,
    data = appData.data;
  var typescript = options.typescript,
    includeAccessibility = options.includeAccessibility;
  var fileExtension = typescript ? 'tsx' : 'jsx';
  var rnCode = "import React".concat(data && Object.keys(data).length > 0 ? ', { useState }' : '', " from 'react';\nimport {\n  SafeAreaView,\n  ScrollView,\n  StatusBar,\n  StyleSheet,\n  Text,\n  View,\n} from 'react-native';\n\nconst App = () => {");

  // Add state management
  if (data && Object.keys(data).length > 0) {
    Object.entries(data).forEach(function (_ref14) {
      var _ref15 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_ref14, 2),
        key = _ref15[0],
        value = _ref15[1];
      rnCode += "\n  const [".concat(key, ", set").concat(pascalCase(key), "] = useState(").concat(JSON.stringify(value), ");");
    });
  }
  rnCode += "\n  return (\n    <SafeAreaView style={styles.container}>\n      <StatusBar barStyle=\"dark-content\" />\n      <ScrollView contentInsetAdjustmentBehavior=\"automatic\">";

  // Generate layout structure
  layouts.forEach(function (layout) {
    rnCode += "\n        <View style={styles.".concat(layout.name || layout.type || 'layout', "Container}>");
    if (layout.components && layout.components.length > 0) {
      layout.components.forEach(function (componentId) {
        var component = components.find(function (c) {
          return c.id === componentId;
        });
        if (component) {
          rnCode += "\n          <".concat(mapToReactNativeComponent(component.type));
          Object.entries(component.props || {}).forEach(function (_ref16) {
            var _ref17 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_ref16, 2),
              prop = _ref17[0],
              value = _ref17[1];
            var mappedProp = mapToReactNativeProp(prop, component.type);
            if (typeof value === 'string') {
              rnCode += " ".concat(mappedProp, "=\"").concat(value, "\"");
            } else {
              rnCode += " ".concat(mappedProp, "={").concat(JSON.stringify(value), "}");
            }
          });
          rnCode += " />";
        }
      });
    }
    rnCode += "\n        </View>";
  });
  rnCode += "\n      </ScrollView>\n    </SafeAreaView>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#fff',\n  },\n  ".concat(generateReactNativeStyles(styles, components, layouts), "\n});\n\nexport default App;");
  return rnCode;
};

// Helper functions for style generation
var generateVueStyles = function generateVueStyles(styles, components, layouts) {
  // Implementation for Vue-specific styles
  return Object.entries(styles).map(function (_ref18) {
    var _ref19 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_ref18, 2),
      selector = _ref19[0],
      style = _ref19[1];
    return "".concat(selector, " {\n  ").concat(Object.entries(style).map(function (_ref20) {
      var _ref21 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_ref20, 2),
        prop = _ref21[0],
        value = _ref21[1];
      return "".concat(prop, ": ").concat(value, ";");
    }).join('\n  '), "\n}");
  }).join('\n\n');
};
var generateAngularStyles = function generateAngularStyles(styles, components, layouts) {
  // Implementation for Angular-specific styles
  return Object.entries(styles).map(function (_ref22) {
    var _ref23 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_ref22, 2),
      selector = _ref23[0],
      style = _ref23[1];
    return "".concat(selector, " {\n  ").concat(Object.entries(style).map(function (_ref24) {
      var _ref25 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_ref24, 2),
        prop = _ref25[0],
        value = _ref25[1];
      return "".concat(prop, ": ").concat(value, ";");
    }).join('\n  '), "\n}");
  }).join('\n\n');
};
var generateSvelteStyles = function generateSvelteStyles(styles, components, layouts) {
  // Implementation for Svelte-specific styles
  return Object.entries(styles).map(function (_ref26) {
    var _ref27 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_ref26, 2),
      selector = _ref27[0],
      style = _ref27[1];
    return "".concat(selector, " {\n    ").concat(Object.entries(style).map(function (_ref28) {
      var _ref29 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_ref28, 2),
        prop = _ref29[0],
        value = _ref29[1];
      return "".concat(prop, ": ").concat(value, ";");
    }).join('\n    '), "\n  }");
  }).join('\n\n');
};
var generateNextJSStyles = function generateNextJSStyles(styles, components, layouts) {
  // Implementation for Next.js CSS modules
  return ".container {\n  padding: 0 2rem;\n}\n\n.main {\n  min-height: 100vh;\n  padding: 4rem 0;\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n}\n\n".concat(Object.entries(styles).map(function (_ref30) {
    var _ref31 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_ref30, 2),
      selector = _ref31[0],
      style = _ref31[1];
    return "".concat(selector.replace(/[^a-zA-Z0-9]/g, ''), " {\n  ").concat(Object.entries(style).map(function (_ref32) {
      var _ref33 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_ref32, 2),
        prop = _ref33[0],
        value = _ref33[1];
      return "".concat(prop, ": ").concat(value, ";");
    }).join('\n  '), "\n}");
  }).join('\n\n'));
};
var generateReactNativeStyles = function generateReactNativeStyles(styles, components, layouts) {
  // Implementation for React Native StyleSheet
  return layouts.map(function (layout) {
    var layoutName = layout.name || layout.type || 'layout';
    return "".concat(layoutName, "Container: {\n    padding: 16,\n    marginVertical: 8,\n  },");
  }).join('\n  ');
};

// Helper functions
var pascalCase = function pascalCase(str) {
  return str.replace(/(?:^|[\s-_])(\w)/g, function (match, letter) {
    return letter.toUpperCase();
  }).replace(/[\s-_]/g, '');
};
var mapToReactNativeComponent = function mapToReactNativeComponent(type) {
  var componentMap = {
    'Button': 'TouchableOpacity',
    'Text': 'Text',
    'Input': 'TextInput',
    'Image': 'Image',
    'Card': 'View',
    'Section': 'View',
    'Header': 'View'
  };
  return componentMap[type] || 'View';
};
var mapToReactNativeProp = function mapToReactNativeProp(prop, componentType) {
  var propMap = {
    'text': 'title',
    'content': 'children',
    'src': 'source'
  };
  return propMap[prop] || prop;
};

/***/ })

}]);