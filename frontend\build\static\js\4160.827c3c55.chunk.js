"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[4160],{

/***/ 11398:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(57528);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(33966);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(36031);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(71606);




var _templateObject, _templateObject2, _templateObject3;

function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }




var Title = antd__WEBPACK_IMPORTED_MODULE_6__/* .Typography */ .o5.Title,
  Text = antd__WEBPACK_IMPORTED_MODULE_6__/* .Typography */ .o5.Text,
  Paragraph = antd__WEBPACK_IMPORTED_MODULE_6__/* .Typography */ .o5.Paragraph;
var Panel = antd__WEBPACK_IMPORTED_MODULE_6__/* .Collapse */ .SD.Panel;

// Styled components
var MonitorContainer = styled_components__WEBPACK_IMPORTED_MODULE_8__/* .styled */ .I4.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  position: fixed;\n  bottom: 20px;\n  left: 20px;\n  z-index: 1000;\n  max-width: 400px;\n  transition: all 0.3s ease;\n  transform: ", ";\n"])), function (props) {
  return props.minimized ? 'translateY(calc(100% - 40px))' : 'translateY(0)';
});
var MinimizeButton = (0,styled_components__WEBPACK_IMPORTED_MODULE_8__/* .styled */ .I4)(antd__WEBPACK_IMPORTED_MODULE_6__/* .Button */ .$n)(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  position: absolute;\n  top: 10px;\n  right: 10px;\n  z-index: 1001;\n"])));
var StatusIndicator = styled_components__WEBPACK_IMPORTED_MODULE_8__/* .styled */ .I4.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  display: inline-block;\n  width: 10px;\n  height: 10px;\n  border-radius: 50%;\n  margin-right: 8px;\n  background-color: ", ";\n"])), function (props) {
  return props.status === 'good' ? '#52c41a' : props.status === 'warning' ? '#faad14' : '#f5222d';
});

/**
 * EnhancedPerformanceMonitor component
 * 
 * This component monitors various performance metrics of the application
 * and displays them in a user-friendly dashboard.
 */
var EnhancedPerformanceMonitor = function EnhancedPerformanceMonitor(_ref) {
  var _ref$enabled = _ref.enabled,
    enabled = _ref$enabled === void 0 ? true : _ref$enabled,
    _ref$initiallyMinimiz = _ref.initiallyMinimized,
    initiallyMinimized = _ref$initiallyMinimiz === void 0 ? true : _ref$initiallyMinimiz,
    _ref$refreshInterval = _ref.refreshInterval,
    refreshInterval = _ref$refreshInterval === void 0 ? 5000 : _ref$refreshInterval,
    _ref$wsUrl = _ref.wsUrl,
    wsUrl = _ref$wsUrl === void 0 ? 'ws://localhost:8000/ws' : _ref$wsUrl,
    _ref$showNetworkStatu = _ref.showNetworkStatus,
    showNetworkStatus = _ref$showNetworkStatu === void 0 ? true : _ref$showNetworkStatu,
    _ref$showMemoryUsage = _ref.showMemoryUsage,
    showMemoryUsage = _ref$showMemoryUsage === void 0 ? true : _ref$showMemoryUsage,
    _ref$showRenderCounts = _ref.showRenderCounts,
    showRenderCounts = _ref$showRenderCounts === void 0 ? true : _ref$showRenderCounts,
    _ref$showWebSocketSta = _ref.showWebSocketStatus,
    showWebSocketStatus = _ref$showWebSocketSta === void 0 ? true : _ref$showWebSocketSta;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(initiallyMinimized),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState, 2),
    minimized = _useState2[0],
    setMinimized = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)({
      fps: 0,
      memory: {
        used: 0,
        total: 0,
        limit: 0
      },
      network: {
        latency: 0,
        status: 'unknown'
      },
      websocket: {
        connected: false,
        latency: 0
      },
      renderCounts: {}
    }),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState3, 2),
    metrics = _useState4[0],
    setMetrics = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(enabled),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState5, 2),
    isActive = _useState6[0],
    setIsActive = _useState6[1];
  var rafId = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(null);
  var lastFrameTime = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(performance.now());
  var frameCount = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(0);
  var wsRef = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(null);
  var pingInterval = (0,react__WEBPACK_IMPORTED_MODULE_5__.useRef)(null);

  // Toggle minimized state
  var toggleMinimized = function toggleMinimized() {
    setMinimized(function (prev) {
      return !prev;
    });
  };

  // Toggle active state
  var toggleActive = function toggleActive(checked) {
    setIsActive(checked);
  };

  // Measure FPS
  var measureFPS = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(function () {
    if (!isActive) return;
    var now = performance.now();
    frameCount.current += 1;

    // Update FPS every second
    if (now - lastFrameTime.current >= 1000) {
      setMetrics(function (prev) {
        return _objectSpread(_objectSpread({}, prev), {}, {
          fps: Math.round(frameCount.current * 1000 / (now - lastFrameTime.current))
        });
      });
      frameCount.current = 0;
      lastFrameTime.current = now;
    }
    rafId.current = requestAnimationFrame(measureFPS);
  }, [isActive]);

  // Measure memory usage
  var measureMemory = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(/*#__PURE__*/(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee() {
    var _performance$memory, usedJSHeapSize, totalJSHeapSize, jsHeapSizeLimit;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().wrap(function (_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          if (!(!isActive || !showMemoryUsage)) {
            _context.next = 1;
            break;
          }
          return _context.abrupt("return");
        case 1:
          try {
            // Use performance.memory if available (Chrome only)
            if (performance.memory) {
              _performance$memory = performance.memory, usedJSHeapSize = _performance$memory.usedJSHeapSize, totalJSHeapSize = _performance$memory.totalJSHeapSize, jsHeapSizeLimit = _performance$memory.jsHeapSizeLimit;
              setMetrics(function (prev) {
                return _objectSpread(_objectSpread({}, prev), {}, {
                  memory: {
                    used: Math.round(usedJSHeapSize / (1024 * 1024)),
                    total: Math.round(totalJSHeapSize / (1024 * 1024)),
                    limit: Math.round(jsHeapSizeLimit / (1024 * 1024))
                  }
                });
              });
            }
          } catch (error) {
            console.error('Error measuring memory:', error);
          }
        case 2:
        case "end":
          return _context.stop();
      }
    }, _callee);
  })), [isActive, showMemoryUsage]);

  // Measure network status
  var measureNetwork = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(/*#__PURE__*/(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee2() {
    var startTime, response, endTime, _t;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_4___default().wrap(function (_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          if (!(!isActive || !showNetworkStatus)) {
            _context2.next = 1;
            break;
          }
          return _context2.abrupt("return");
        case 1:
          _context2.prev = 1;
          startTime = performance.now();
          _context2.next = 2;
          return fetch('/api/health/');
        case 2:
          response = _context2.sent;
          endTime = performance.now();
          setMetrics(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              network: {
                latency: Math.round(endTime - startTime),
                status: response.ok ? 'good' : 'error'
              }
            });
          });
          _context2.next = 4;
          break;
        case 3:
          _context2.prev = 3;
          _t = _context2["catch"](1);
          setMetrics(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              network: {
                latency: 0,
                status: 'error'
              }
            });
          });
        case 4:
        case "end":
          return _context2.stop();
      }
    }, _callee2, null, [[1, 3]]);
  })), [isActive, showNetworkStatus]);

  // Initialize WebSocket connection
  var initWebSocket = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(function () {
    if (!isActive || !showWebSocketStatus) return;
    try {
      // Close existing connection if any
      if (wsRef.current) {
        wsRef.current.close();
      }

      // Create new connection
      wsRef.current = new WebSocket(wsUrl);

      // Set up event handlers
      wsRef.current.onopen = function () {
        setMetrics(function (prev) {
          return _objectSpread(_objectSpread({}, prev), {}, {
            websocket: _objectSpread(_objectSpread({}, prev.websocket), {}, {
              connected: true
            })
          });
        });

        // Start ping interval
        pingInterval.current = setInterval(function () {
          if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
            var pingTime = performance.now();
            wsRef.current.send(JSON.stringify({
              type: 'ping',
              time: pingTime
            }));
          }
        }, 5000);
      };
      wsRef.current.onclose = function () {
        setMetrics(function (prev) {
          return _objectSpread(_objectSpread({}, prev), {}, {
            websocket: _objectSpread(_objectSpread({}, prev.websocket), {}, {
              connected: false
            })
          });
        });

        // Clear ping interval
        if (pingInterval.current) {
          clearInterval(pingInterval.current);
        }
      };
      wsRef.current.onmessage = function (event) {
        try {
          var data = JSON.parse(event.data);
          if (data.type === 'pong' && data.time) {
            var pongTime = performance.now();
            var latency = Math.round(pongTime - data.time);
            setMetrics(function (prev) {
              return _objectSpread(_objectSpread({}, prev), {}, {
                websocket: _objectSpread(_objectSpread({}, prev.websocket), {}, {
                  latency: latency
                })
              });
            });
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };
      wsRef.current.onerror = function (error) {
        console.error('WebSocket error:', error);
        setMetrics(function (prev) {
          return _objectSpread(_objectSpread({}, prev), {}, {
            websocket: {
              connected: false,
              latency: 0
            }
          });
        });
      };
    } catch (error) {
      console.error('Error initializing WebSocket:', error);
    }
  }, [isActive, showWebSocketStatus, wsUrl]);

  // Initialize performance monitoring
  (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(function () {
    if (isActive) {
      // Start FPS measurement
      rafId.current = requestAnimationFrame(measureFPS);

      // Start memory measurement
      var memoryInterval = setInterval(measureMemory, refreshInterval);

      // Start network measurement
      var networkInterval = setInterval(measureNetwork, refreshInterval);

      // Initialize WebSocket
      initWebSocket();

      // Clean up
      return function () {
        cancelAnimationFrame(rafId.current);
        clearInterval(memoryInterval);
        clearInterval(networkInterval);
        if (pingInterval.current) {
          clearInterval(pingInterval.current);
        }
        if (wsRef.current) {
          wsRef.current.close();
        }
      };
    }
  }, [isActive, measureFPS, measureMemory, measureNetwork, initWebSocket, refreshInterval]);

  // Don't render if not enabled
  if (!enabled) {
    return null;
  }
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(MonitorContainer, {
    minimized: minimized
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Card */ .Zp, {
    title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
      style: {
        display: 'flex',
        alignItems: 'center'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .DashboardOutlined */ .zpd, {
      style: {
        marginRight: 8
      }
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("span", null, "Performance Monitor")),
    extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Switch */ .dO, {
      checked: isActive,
      onChange: toggleActive,
      size: "small",
      checkedChildren: "On",
      unCheckedChildren: "Off"
    }),
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(MinimizeButton, {
    type: "text",
    icon: minimized ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .LineChartOutlined */ .BdS, null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_7__/* .LineChartOutlined */ .BdS, null),
    onClick: toggleMinimized,
    size: "small"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Row */ .fI, {
    gutter: [16, 16]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Col */ .fv, {
    span: 12
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Statistic */ .jL, {
    title: "FPS",
    value: metrics.fps,
    suffix: "fps",
    valueStyle: {
      color: metrics.fps > 30 ? '#3f8600' : metrics.fps > 15 ? '#faad14' : '#cf1322'
    }
  })), showMemoryUsage && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Col */ .fv, {
    span: 12
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Statistic */ .jL, {
    title: "Memory",
    value: metrics.memory.used,
    suffix: "MB",
    valueStyle: {
      color: metrics.memory.used < metrics.memory.limit * 0.8 ? '#3f8600' : '#cf1322'
    }
  }), metrics.memory.limit > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(antd__WEBPACK_IMPORTED_MODULE_6__/* .Progress */ .ke, {
    percent: Math.round(metrics.memory.used / metrics.memory.limit * 100),
    size: "small",
    status: metrics.memory.used < metrics.memory.limit * 0.8 ? 'normal' : 'exception'
  }))), showNetworkStatus && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
    style: {
      marginTop: 16
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(Text, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(StatusIndicator, {
    status: metrics.network.status
  }), "Network: ", metrics.network.status === 'good' ? 'Connected' : 'Disconnected', metrics.network.latency > 0 && " (".concat(metrics.network.latency, "ms)"))), showWebSocketStatus && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement("div", {
    style: {
      marginTop: 8
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(Text, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_5__.createElement(StatusIndicator, {
    status: metrics.websocket.connected ? 'good' : 'error'
  }), "WebSocket: ", metrics.websocket.connected ? 'Connected' : 'Disconnected', metrics.websocket.connected && metrics.websocket.latency > 0 && " (".concat(metrics.websocket.latency, "ms)")))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EnhancedPerformanceMonitor);

/***/ }),

/***/ 11937:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(57528);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(33966);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(36031);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(71606);





var _templateObject, _templateObject2, _templateObject3, _templateObject4;
function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t["return"] || t["return"](); } finally { if (u) throw o; } } }; }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }

function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * Testing Tools Component
 * 
 * Comprehensive testing capabilities including component testing, layout validation,
 * and accessibility compliance checks integrated into the App Builder.
 */





var Title = antd__WEBPACK_IMPORTED_MODULE_7__/* .Typography */ .o5.Title,
  Text = antd__WEBPACK_IMPORTED_MODULE_7__/* .Typography */ .o5.Text,
  Paragraph = antd__WEBPACK_IMPORTED_MODULE_7__/* .Typography */ .o5.Paragraph;
var TabPane = antd__WEBPACK_IMPORTED_MODULE_7__/* .Tabs */ .tU.TabPane;
var Option = antd__WEBPACK_IMPORTED_MODULE_7__/* .Select */ .l6.Option;

// Styled Components
var TestingContainer = styled_components__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  padding: 16px;\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n"])));
var TestResultCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp)(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  margin-bottom: 16px;\n  \n  .ant-card-head {\n    background: ", ";\n  }\n"])), function (props) {
  switch (props.status) {
    case 'passed':
      return '#f6ffed';
    case 'failed':
      return '#fff2f0';
    case 'warning':
      return '#fffbe6';
    default:
      return '#fafafa';
  }
});
var TestMetrics = styled_components__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 16px;\n  margin-bottom: 24px;\n"])));
var TestProgress = styled_components__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay.div(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  margin: 16px 0;\n"])));

// Test Types Configuration
var TEST_TYPES = {
  COMPONENT: {
    id: 'component',
    name: 'Component Tests',
    description: 'Test individual component functionality and props',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .BugOutlined */ .NhG, null),
    color: '#1890ff'
  },
  LAYOUT: {
    id: 'layout',
    name: 'Layout Validation',
    description: 'Validate responsive layouts and positioning',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .DesktopOutlined */ .zlw, null),
    color: '#52c41a'
  },
  ACCESSIBILITY: {
    id: 'accessibility',
    name: 'Accessibility Tests',
    description: 'WCAG 2.1 AA compliance and screen reader support',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .SafetyOutlined */ .lMf, null),
    color: '#722ed1'
  },
  PERFORMANCE: {
    id: 'performance',
    name: 'Performance Tests',
    description: 'Render performance and optimization checks',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .ThunderboltOutlined */ .CwG, null),
    color: '#fa8c16'
  },
  RESPONSIVE: {
    id: 'responsive',
    name: 'Responsive Tests',
    description: 'Cross-device and viewport testing',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .MobileOutlined */ .jHj, null),
    color: '#eb2f96'
  }
};

// Mock test results for demonstration
var generateMockTestResults = function generateMockTestResults(testType, components) {
  var results = [];
  var testCount = Math.floor(Math.random() * 10) + 5;
  for (var i = 0; i < testCount; i++) {
    var _TEST_TYPES$testType$;
    var status = Math.random() > 0.8 ? 'failed' : Math.random() > 0.9 ? 'warning' : 'passed';
    results.push({
      id: "".concat(testType, "-").concat(i),
      name: "".concat(((_TEST_TYPES$testType$ = TEST_TYPES[testType.toUpperCase()]) === null || _TEST_TYPES$testType$ === void 0 ? void 0 : _TEST_TYPES$testType$.name) || testType, " Test ").concat(i + 1),
      status: status,
      duration: Math.floor(Math.random() * 1000) + 100,
      message: status === 'failed' ? 'Test assertion failed' : status === 'warning' ? 'Performance threshold exceeded' : 'Test passed successfully',
      details: {
        assertions: Math.floor(Math.random() * 10) + 1,
        coverage: Math.floor(Math.random() * 30) + 70
      }
    });
  }
  return results;
};

/**
 * TestingTools Component
 */
var TestingTools = function TestingTools(_ref) {
  var _ref$components = _ref.components,
    components = _ref$components === void 0 ? [] : _ref$components,
    onTestComplete = _ref.onTestComplete,
    onTestStart = _ref.onTestStart,
    _ref$enabledTests = _ref.enabledTests,
    enabledTests = _ref$enabledTests === void 0 ? Object.keys(TEST_TYPES) : _ref$enabledTests,
    _ref$autoRun = _ref.autoRun,
    autoRun = _ref$autoRun === void 0 ? false : _ref$autoRun,
    _ref$showMetrics = _ref.showMetrics,
    showMetrics = _ref$showMetrics === void 0 ? true : _ref$showMetrics,
    _ref$compact = _ref.compact,
    compact = _ref$compact === void 0 ? false : _ref$compact;
  // State
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)('overview'),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState, 2),
    activeTab = _useState2[0],
    setActiveTab = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)({}),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState3, 2),
    testResults = _useState4[0],
    setTestResults = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(new Set()),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState5, 2),
    runningTests = _useState6[0],
    setRunningTests = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)({}),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState7, 2),
    testProgress = _useState8[0],
    setTestProgress = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)({
      includePerformance: true,
      includeAccessibility: true,
      includeResponsive: true,
      strictMode: false,
      generateReport: true
    }),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState9, 2),
    testSettings = _useState0[0],
    setTestSettings = _useState0[1];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(false),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState1, 2),
    showSettings = _useState10[0],
    setShowSettings = _useState10[1];

  // Computed metrics
  var testMetrics = (0,react__WEBPACK_IMPORTED_MODULE_6__.useMemo)(function () {
    var allResults = Object.values(testResults).flat();
    var total = allResults.length;
    var passed = allResults.filter(function (r) {
      return r.status === 'passed';
    }).length;
    var failed = allResults.filter(function (r) {
      return r.status === 'failed';
    }).length;
    var warnings = allResults.filter(function (r) {
      return r.status === 'warning';
    }).length;
    return {
      total: total,
      passed: passed,
      failed: failed,
      warnings: warnings,
      passRate: total > 0 ? Math.round(passed / total * 100) : 0,
      avgDuration: total > 0 ? Math.round(allResults.reduce(function (sum, r) {
        return sum + r.duration;
      }, 0) / total) : 0
    };
  }, [testResults]);

  // Run tests for a specific type
  var runTests = (0,react__WEBPACK_IMPORTED_MODULE_6__.useCallback)(/*#__PURE__*/function () {
    var _ref2 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default().mark(function _callee(testType) {
      var _TEST_TYPES$testType$2, progressInterval, results, metrics, _t;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default().wrap(function (_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            if (!runningTests.has(testType)) {
              _context.next = 1;
              break;
            }
            return _context.abrupt("return");
          case 1:
            setRunningTests(function (prev) {
              return new Set([].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(prev), [testType]));
            });
            setTestProgress(function (prev) {
              return _objectSpread(_objectSpread({}, prev), {}, (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)({}, testType, 0));
            });
            if (onTestStart) {
              onTestStart(testType);
            }
            _context.prev = 2;
            // Simulate test execution with progress updates
            progressInterval = setInterval(function () {
              setTestProgress(function (prev) {
                return _objectSpread(_objectSpread({}, prev), {}, (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)({}, testType, Math.min((prev[testType] || 0) + Math.random() * 20, 95)));
              });
            }, 200); // Simulate async test execution
            _context.next = 3;
            return new Promise(function (resolve) {
              return setTimeout(resolve, 2000 + Math.random() * 3000);
            });
          case 3:
            clearInterval(progressInterval);
            setTestProgress(function (prev) {
              return _objectSpread(_objectSpread({}, prev), {}, (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)({}, testType, 100));
            });

            // Generate mock results
            results = generateMockTestResults(testType, components);
            setTestResults(function (prev) {
              return _objectSpread(_objectSpread({}, prev), {}, (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)({}, testType, results));
            });
            if (onTestComplete) {
              onTestComplete(testType, results);
            }

            // Show notification
            metrics = {
              total: results.length,
              passed: results.filter(function (r) {
                return r.status === 'passed';
              }).length,
              failed: results.filter(function (r) {
                return r.status === 'failed';
              }).length
            };
            antd__WEBPACK_IMPORTED_MODULE_7__/* .notification */ .Ew.success({
              message: "".concat((_TEST_TYPES$testType$2 = TEST_TYPES[testType.toUpperCase()]) === null || _TEST_TYPES$testType$2 === void 0 ? void 0 : _TEST_TYPES$testType$2.name, " Complete"),
              description: "".concat(metrics.passed, "/").concat(metrics.total, " tests passed"),
              duration: 3
            });
            _context.next = 5;
            break;
          case 4:
            _context.prev = 4;
            _t = _context["catch"](2);
            antd__WEBPACK_IMPORTED_MODULE_7__/* .notification */ .Ew.error({
              message: 'Test Execution Failed',
              description: _t.message,
              duration: 5
            });
          case 5:
            _context.prev = 5;
            setRunningTests(function (prev) {
              var newSet = new Set(prev);
              newSet["delete"](testType);
              return newSet;
            });
            return _context.finish(5);
          case 6:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[2, 4, 5, 6]]);
    }));
    return function (_x) {
      return _ref2.apply(this, arguments);
    };
  }(), [runningTests, components, onTestStart, onTestComplete]);

  // Run all enabled tests
  var runAllTests = (0,react__WEBPACK_IMPORTED_MODULE_6__.useCallback)(/*#__PURE__*/(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default().mark(function _callee2() {
    var _iterator, _step, testType, _t2;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default().wrap(function (_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _iterator = _createForOfIteratorHelper(enabledTests);
          _context2.prev = 1;
          _iterator.s();
        case 2:
          if ((_step = _iterator.n()).done) {
            _context2.next = 4;
            break;
          }
          testType = _step.value;
          if (runningTests.has(testType)) {
            _context2.next = 3;
            break;
          }
          _context2.next = 3;
          return runTests(testType);
        case 3:
          _context2.next = 2;
          break;
        case 4:
          _context2.next = 6;
          break;
        case 5:
          _context2.prev = 5;
          _t2 = _context2["catch"](1);
          _iterator.e(_t2);
        case 6:
          _context2.prev = 6;
          _iterator.f();
          return _context2.finish(6);
        case 7:
        case "end":
          return _context2.stop();
      }
    }, _callee2, null, [[1, 5, 6, 7]]);
  })), [enabledTests, runTests, runningTests]);

  // Clear test results
  var clearResults = (0,react__WEBPACK_IMPORTED_MODULE_6__.useCallback)(function () {
    setTestResults({});
    setTestProgress({});
  }, []);

  // Auto-run tests when components change
  (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(function () {
    if (autoRun && components.length > 0) {
      var timer = setTimeout(function () {
        runAllTests();
      }, 1000);
      return function () {
        return clearTimeout(timer);
      };
    }
  }, [autoRun, components, runAllTests]);
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(TestingContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
    style: {
      marginBottom: 16
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Row */ .fI, {
    justify: "space-between",
    align: "middle"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Col */ .fv, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Title, {
    level: 4,
    style: {
      margin: 0
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .BugOutlined */ .NhG, {
    style: {
      marginRight: 8,
      color: '#1890ff'
    }
  }), "Testing Tools")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Col */ .fv, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .SettingOutlined */ .JO7, null),
    onClick: function onClick() {
      return setShowSettings(true);
    }
  }, "Settings"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
    type: "primary",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .PlayCircleOutlined */ .VgC, null),
    onClick: runAllTests,
    loading: runningTests.size > 0,
    disabled: components.length === 0
  }, "Run All Tests"))))), showMetrics && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(TestMetrics, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp, {
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Statistic */ .jL, {
    title: "Total Tests",
    value: testMetrics.total,
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .BugOutlined */ .NhG, null)
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp, {
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Statistic */ .jL, {
    title: "Pass Rate",
    value: testMetrics.passRate,
    suffix: "%",
    valueStyle: {
      color: testMetrics.passRate >= 80 ? '#3f8600' : '#cf1322'
    },
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .CheckCircleOutlined */ .hWy, null)
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp, {
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Statistic */ .jL, {
    title: "Failed Tests",
    value: testMetrics.failed,
    valueStyle: {
      color: testMetrics.failed > 0 ? '#cf1322' : '#3f8600'
    },
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .CloseCircleOutlined */ .bBN, null)
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp, {
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Statistic */ .jL, {
    title: "Avg Duration",
    value: testMetrics.avgDuration,
    suffix: "ms",
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .ThunderboltOutlined */ .CwG, null)
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Tabs */ .tU, {
    activeKey: activeTab,
    onChange: setActiveTab
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(TabPane, {
    tab: "Overview",
    key: "overview"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Row */ .fI, {
    gutter: [16, 16]
  }, enabledTests.map(function (testType) {
    var config = TEST_TYPES[testType.toUpperCase()];
    var results = testResults[testType] || [];
    var isRunning = runningTests.has(testType);
    var progress = testProgress[testType] || 0;
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Col */ .fv, {
      xs: 24,
      sm: 12,
      lg: 8,
      key: testType
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(TestResultCard, {
      size: "small",
      status: results.length > 0 ? results.some(function (r) {
        return r.status === 'failed';
      }) ? 'failed' : results.some(function (r) {
        return r.status === 'warning';
      }) ? 'warning' : 'passed' : 'default',
      title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, config === null || config === void 0 ? void 0 : config.icon, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("span", null, config === null || config === void 0 ? void 0 : config.name), results.length > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Badge */ .Ex, {
        count: results.filter(function (r) {
          return r.status === 'passed';
        }).length,
        style: {
          backgroundColor: '#52c41a'
        }
      })),
      extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
        size: "small",
        type: "primary",
        icon: isRunning ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .PauseCircleOutlined */ .Xcy, null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .PlayCircleOutlined */ .VgC, null),
        onClick: function onClick() {
          return runTests(testType);
        },
        loading: isRunning,
        disabled: components.length === 0
      }, isRunning ? 'Running' : 'Run')
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Paragraph, {
      style: {
        margin: 0,
        marginBottom: 12
      }
    }, config === null || config === void 0 ? void 0 : config.description), isRunning && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(TestProgress, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Progress */ .ke, {
      percent: Math.round(progress),
      size: "small",
      status: progress < 100 ? 'active' : 'success'
    })), results.length > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, {
      type: "secondary"
    }, results.filter(function (r) {
      return r.status === 'passed';
    }).length, "/", results.length, " tests passed"))));
  }))), enabledTests.map(function (testType) {
    var config = TEST_TYPES[testType.toUpperCase()];
    var results = testResults[testType] || [];
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(TabPane, {
      tab: config === null || config === void 0 ? void 0 : config.name,
      key: testType
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
      style: {
        marginBottom: 16
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Row */ .fI, {
      justify: "space-between",
      align: "middle"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Col */ .fv, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Title, {
      level: 5
    }, config === null || config === void 0 ? void 0 : config.icon, " ", config === null || config === void 0 ? void 0 : config.name)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Col */ .fv, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .ReloadOutlined */ .KF4, null),
      onClick: function onClick() {
        return runTests(testType);
      },
      loading: runningTests.has(testType),
      disabled: components.length === 0
    }, "Re-run"), results.length > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .DownloadOutlined */ .jsW, null)
    }, "Export Results"))))), runningTests.has(testType) && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(TestProgress, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Progress */ .ke, {
      percent: Math.round(testProgress[testType] || 0),
      status: "active"
    })), results.length > 0 ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .List */ .B8, {
      dataSource: results,
      renderItem: function renderItem(result) {
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .List */ .B8.Item, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .List */ .B8.Item.Meta, {
          avatar: result.status === 'passed' ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .CheckCircleOutlined */ .hWy, {
            style: {
              color: '#52c41a'
            }
          }) : result.status === 'failed' ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .CloseCircleOutlined */ .bBN, {
            style: {
              color: '#ff4d4f'
            }
          }) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .ExclamationCircleOutlined */ .G2i, {
            style: {
              color: '#faad14'
            }
          }),
          title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("span", null, result.name), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Tag */ .vw, {
            color: result.status === 'passed' ? 'green' : result.status === 'failed' ? 'red' : 'orange'
          }, result.status.toUpperCase())),
          description: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, null, result.message), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("br", null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, {
            type: "secondary"
          }, "Duration: ", result.duration, "ms | Assertions: ", result.details.assertions, " | Coverage: ", result.details.coverage, "%"))
        }));
      }
    }) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Alert */ .Fc, {
      message: "No test results",
      description: "Run ".concat(config === null || config === void 0 ? void 0 : config.name, " to see results here."),
      type: "info",
      showIcon: true
    }));
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Modal */ .aF, {
    title: "Test Settings",
    open: showSettings,
    onCancel: function onCancel() {
      return setShowSettings(false);
    },
    footer: [/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      key: "cancel",
      onClick: function onClick() {
        return setShowSettings(false);
      }
    }, "Cancel"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      key: "save",
      type: "primary",
      onClick: function onClick() {
        return setShowSettings(false);
      }
    }, "Save Settings")]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, {
    strong: true
  }, "Test Options"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
    style: {
      marginTop: 8
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Row */ .fI, {
    gutter: [16, 8]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Col */ .fv, {
    span: 24
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Switch */ .dO, {
    checked: testSettings.includePerformance,
    onChange: function onChange(checked) {
      return setTestSettings(function (prev) {
        return _objectSpread(_objectSpread({}, prev), {}, {
          includePerformance: checked
        });
      });
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, {
    style: {
      marginLeft: 8
    }
  }, "Include Performance Tests")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Col */ .fv, {
    span: 24
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Switch */ .dO, {
    checked: testSettings.includeAccessibility,
    onChange: function onChange(checked) {
      return setTestSettings(function (prev) {
        return _objectSpread(_objectSpread({}, prev), {}, {
          includeAccessibility: checked
        });
      });
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, {
    style: {
      marginLeft: 8
    }
  }, "Include Accessibility Tests")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Col */ .fv, {
    span: 24
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Switch */ .dO, {
    checked: testSettings.includeResponsive,
    onChange: function onChange(checked) {
      return setTestSettings(function (prev) {
        return _objectSpread(_objectSpread({}, prev), {}, {
          includeResponsive: checked
        });
      });
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, {
    style: {
      marginLeft: 8
    }
  }, "Include Responsive Tests")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Col */ .fv, {
    span: 24
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Switch */ .dO, {
    checked: testSettings.strictMode,
    onChange: function onChange(checked) {
      return setTestSettings(function (prev) {
        return _objectSpread(_objectSpread({}, prev), {}, {
          strictMode: checked
        });
      });
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, {
    style: {
      marginLeft: 8
    }
  }, "Strict Mode (Fail on Warnings)")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Col */ .fv, {
    span: 24
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Switch */ .dO, {
    checked: testSettings.generateReport,
    onChange: function onChange(checked) {
      return setTestSettings(function (prev) {
        return _objectSpread(_objectSpread({}, prev), {}, {
          generateReport: checked
        });
      });
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, {
    style: {
      marginLeft: 8
    }
  }, "Generate Test Reports"))))))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TestingTools);

/***/ }),

/***/ 49697:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(82284);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(33966);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(36031);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(71468);
/* harmony import */ var _redux_actions_websocket_actions__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(41533);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(71606);



var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5;






var Title = antd__WEBPACK_IMPORTED_MODULE_4__/* .Typography */ .o5.Title,
  Text = antd__WEBPACK_IMPORTED_MODULE_4__/* .Typography */ .o5.Text,
  Paragraph = antd__WEBPACK_IMPORTED_MODULE_4__/* .Typography */ .o5.Paragraph;
var Panel = antd__WEBPACK_IMPORTED_MODULE_4__/* .Collapse */ .SD.Panel;
var TextArea = antd__WEBPACK_IMPORTED_MODULE_4__/* .Input */ .pd.TextArea;

// Styled components
var StatusBadge = (0,styled_components__WEBPACK_IMPORTED_MODULE_8__/* .styled */ .I4)(antd__WEBPACK_IMPORTED_MODULE_4__/* .Badge */ .Ex)(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  .ant-badge-status-dot {\n    width: 10px;\n    height: 10px;\n  }\n"])));
var MessageContainer = styled_components__WEBPACK_IMPORTED_MODULE_8__/* .styled */ .I4.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  max-height: 300px;\n  overflow-y: auto;\n  margin-bottom: 16px;\n  padding: 8px;\n  border: 1px solid #d9d9d9;\n  border-radius: 4px;\n  background-color: ", ";\n"])), function (props) {
  return props.theme === 'dark' ? '#1f1f1f' : '#f5f5f5';
});
var MessageItem = styled_components__WEBPACK_IMPORTED_MODULE_8__/* .styled */ .I4.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  padding: 8px;\n  margin-bottom: 8px;\n  border-radius: 4px;\n  background-color: ", ";\n  border-left: 4px solid ", ";\n  color: ", ";\n  word-break: break-word;\n"])), function (props) {
  return props.type === 'sent' ? props.theme === 'dark' ? '#177ddc' : '#e6f7ff' : props.theme === 'dark' ? '#2b2b2b' : '#ffffff';
}, function (props) {
  return props.type === 'sent' ? '#1890ff' : props.status === 'error' ? '#ff4d4f' : props.status === 'warning' ? '#faad14' : '#52c41a';
}, function (props) {
  return props.theme === 'dark' ? '#ffffff' : '#000000';
});
var TimeStamp = (0,styled_components__WEBPACK_IMPORTED_MODULE_8__/* .styled */ .I4)(Text)(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  font-size: 12px;\n  color: ", ";\n  margin-left: 8px;\n"])), function (props) {
  return props.theme === 'dark' ? '#8c8c8c' : '#8c8c8c';
});
var ConnectionStatus = styled_components__WEBPACK_IMPORTED_MODULE_8__/* .styled */ .I4.div(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  margin-bottom: 16px;\n"])));

/**
 * WebSocketManager component
 * 
 * This component provides a user interface for managing WebSocket connections
 * and viewing WebSocket messages.
 */
var WebSocketManager = function WebSocketManager(_ref) {
  var _ref$visible = _ref.visible,
    visible = _ref$visible === void 0 ? false : _ref$visible,
    _ref$onClose = _ref.onClose,
    onClose = _ref$onClose === void 0 ? function () {} : _ref$onClose,
    _ref$placement = _ref.placement,
    placement = _ref$placement === void 0 ? 'right' : _ref$placement,
    _ref$width = _ref.width,
    width = _ref$width === void 0 ? 600 : _ref$width;
  var dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_6__/* .useDispatch */ .wA)();
  var _Form$useForm = antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.useForm(),
    _Form$useForm2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_Form$useForm, 1),
    form = _Form$useForm2[0];
  var _Form$useForm3 = antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.useForm(),
    _Form$useForm4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_Form$useForm3, 1),
    messageForm = _Form$useForm4[0];
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    autoScroll = _useState2[0],
    setAutoScroll = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(''),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState3, 2),
    filterText = _useState4[0],
    setFilterText = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState5, 2),
    showSettings = _useState6[0],
    setShowSettings = _useState6[1];

  // Get WebSocket state from Redux store with error handling
  var connected = (0,react_redux__WEBPACK_IMPORTED_MODULE_6__/* .useSelector */ .d4)(function (state) {
    var _state$websocket;
    return (state === null || state === void 0 || (_state$websocket = state.websocket) === null || _state$websocket === void 0 ? void 0 : _state$websocket.connected) || false;
  });
  var connecting = (0,react_redux__WEBPACK_IMPORTED_MODULE_6__/* .useSelector */ .d4)(function (state) {
    var _state$websocket2;
    return (state === null || state === void 0 || (_state$websocket2 = state.websocket) === null || _state$websocket2 === void 0 ? void 0 : _state$websocket2.connecting) || false;
  });
  var error = (0,react_redux__WEBPACK_IMPORTED_MODULE_6__/* .useSelector */ .d4)(function (state) {
    var _state$websocket3;
    return (state === null || state === void 0 || (_state$websocket3 = state.websocket) === null || _state$websocket3 === void 0 ? void 0 : _state$websocket3.error) || null;
  });
  var messages = (0,react_redux__WEBPACK_IMPORTED_MODULE_6__/* .useSelector */ .d4)(function (state) {
    var _state$websocket4;
    return (state === null || state === void 0 || (_state$websocket4 = state.websocket) === null || _state$websocket4 === void 0 ? void 0 : _state$websocket4.messages) || [];
  });
  var url = (0,react_redux__WEBPACK_IMPORTED_MODULE_6__/* .useSelector */ .d4)(function (state) {
    var _state$websocket5;
    return (state === null || state === void 0 || (_state$websocket5 = state.websocket) === null || _state$websocket5 === void 0 ? void 0 : _state$websocket5.url) || null;
  });
  var reconnectAttempts = (0,react_redux__WEBPACK_IMPORTED_MODULE_6__/* .useSelector */ .d4)(function (state) {
    var _state$websocket6;
    return (state === null || state === void 0 || (_state$websocket6 = state.websocket) === null || _state$websocket6 === void 0 ? void 0 : _state$websocket6.reconnectAttempts) || 0;
  });
  var reconnectInterval = (0,react_redux__WEBPACK_IMPORTED_MODULE_6__/* .useSelector */ .d4)(function (state) {
    var _state$websocket7;
    return (state === null || state === void 0 || (_state$websocket7 = state.websocket) === null || _state$websocket7 === void 0 ? void 0 : _state$websocket7.reconnectInterval) || 5000;
  });

  // Get theme from Redux store
  var isDarkMode = (0,react_redux__WEBPACK_IMPORTED_MODULE_6__/* .useSelector */ .d4)(function (state) {
    var _state$themes, _state$themes2;
    var activeThemeId = (_state$themes = state.themes) === null || _state$themes === void 0 ? void 0 : _state$themes.activeTheme;
    var themes = ((_state$themes2 = state.themes) === null || _state$themes2 === void 0 ? void 0 : _state$themes2.themes) || [];
    var activeTheme = themes.find(function (theme) {
      return theme.id === activeThemeId;
    });
    return (activeTheme === null || activeTheme === void 0 ? void 0 : activeTheme.isDark) || false;
  });

  // Auto-scroll to bottom of message container
  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(function () {
    if (autoScroll) {
      var container = document.getElementById('ws-message-container');
      if (container) {
        container.scrollTop = container.scrollHeight;
      }
    }
  }, [messages, autoScroll]);

  // Connect to WebSocket
  var handleConnect = function handleConnect() {
    form.validateFields().then(function (values) {
      var url = values.url,
        reconnectAttempts = values.reconnectAttempts,
        reconnectInterval = values.reconnectInterval,
        protocols = values.protocols;
      dispatch((0,_redux_actions_websocket_actions__WEBPACK_IMPORTED_MODULE_7__/* .wsConnect */ .Lw)(url, {
        reconnectAttempts: reconnectAttempts,
        reconnectInterval: reconnectInterval,
        protocols: protocols ? protocols.split(',').map(function (p) {
          return p.trim();
        }) : undefined
      }));
    });
  };

  // Disconnect from WebSocket
  var handleDisconnect = function handleDisconnect() {
    dispatch((0,_redux_actions_websocket_actions__WEBPACK_IMPORTED_MODULE_7__/* .wsDisconnect */ .rD)());
  };

  // Send message
  var handleSendMessage = function handleSendMessage() {
    messageForm.validateFields().then(function (values) {
      var message = values.message;
      try {
        // Try to parse as JSON
        var jsonMessage = JSON.parse(message);
        dispatch((0,_redux_actions_websocket_actions__WEBPACK_IMPORTED_MODULE_7__/* .wsSendMessage */ .EZ)(jsonMessage));
      } catch (error) {
        // Send as plain text
        dispatch((0,_redux_actions_websocket_actions__WEBPACK_IMPORTED_MODULE_7__/* .wsSendMessage */ .EZ)(message));
      }

      // Clear message input
      messageForm.resetFields();
    });
  };

  // Clear messages
  var handleClearMessages = function handleClearMessages() {
    dispatch((0,_redux_actions_websocket_actions__WEBPACK_IMPORTED_MODULE_7__/* .wsClearMessages */ .G2)());
  };

  // Toggle settings drawer
  var toggleSettings = function toggleSettings() {
    setShowSettings(!showSettings);
  };

  // Filter messages
  var filteredMessages = messages.filter(function (message) {
    if (!filterText) return true;
    var messageStr = (0,_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(message.data) === 'object' ? JSON.stringify(message.data) : String(message.data);
    return messageStr.toLowerCase().includes(filterText.toLowerCase());
  });

  // Format message for display
  var formatMessage = function formatMessage(message) {
    if ((0,_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(message) === 'object') {
      try {
        return JSON.stringify(message, null, 2);
      } catch (error) {
        return String(message);
      }
    }
    return String(message);
  };

  // Get connection status
  var getConnectionStatus = function getConnectionStatus() {
    if (connected) {
      return {
        status: 'success',
        text: 'Connected',
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .CheckCircleOutlined */ .hWy, null)
      };
    } else if (connecting) {
      return {
        status: 'processing',
        text: 'Connecting',
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Spin */ .tK, {
          size: "small"
        })
      };
    } else if (error) {
      return {
        status: 'error',
        text: 'Error',
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .CloseCircleOutlined */ .bBN, null)
      };
    } else {
      return {
        status: 'default',
        text: 'Disconnected',
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .DisconnectOutlined */ .Bu6, null)
      };
    }
  };
  var connectionStatus = getConnectionStatus();

  // Initialize form with current values
  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(function () {
    form.setFieldsValue({
      url: url || 'ws://localhost:8000/ws',
      reconnectAttempts: reconnectAttempts || 5,
      reconnectInterval: reconnectInterval || 3000,
      protocols: ''
    });
  }, [form, url, reconnectAttempts, reconnectInterval]);
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Drawer */ ._s, {
    title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
      style: {
        display: 'flex',
        alignItems: 'center'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ApiOutlined */ .bfv, {
      style: {
        marginRight: 8
      }
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", null, "WebSocket Manager")),
    placement: placement,
    width: width,
    onClose: onClose,
    open: visible,
    extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .SettingOutlined */ .JO7, null),
      onClick: toggleSettings
    }))
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ConnectionStatus, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(StatusBadge, {
    status: connectionStatus.status
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
    style: {
      marginLeft: 8
    }
  }, connectionStatus.text), error && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Tooltip */ .m_, {
    title: error.message
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .WarningOutlined */ .v7y, {
    style: {
      marginLeft: 8,
      color: '#ff4d4f'
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
    style: {
      marginLeft: 'auto'
    }
  }, connected ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .DisconnectOutlined */ .Bu6, null),
    onClick: handleDisconnect,
    danger: true
  }, "Disconnect") : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ApiOutlined */ .bfv, null),
    onClick: handleConnect,
    type: "primary",
    loading: connecting
  }, "Connect"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Collapse */ .SD, {
    defaultActiveKey: ['1', '2']
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Panel, {
    header: "Connection Settings",
    key: "1",
    extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ReloadOutlined */ .KF4, null),
      size: "small",
      onClick: function onClick(e) {
        e.stopPropagation();
        form.resetFields();
      }
    })
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV, {
    form: form,
    layout: "vertical"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.Item, {
    name: "url",
    label: "WebSocket URL",
    rules: [{
      required: true,
      message: 'Please enter WebSocket URL'
    }]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Input */ .pd, {
    placeholder: "ws://localhost:8000/ws",
    disabled: connected || connecting
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.Item, {
    name: "protocols",
    label: "Protocols (comma-separated)"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Input */ .pd, {
    placeholder: "protocol1, protocol2",
    disabled: connected || connecting
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.Item, {
    name: "reconnectAttempts",
    label: "Reconnect Attempts",
    rules: [{
      required: true,
      message: 'Please enter reconnect attempts'
    }]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Input */ .pd, {
    type: "number",
    min: 0,
    max: 10,
    disabled: connected || connecting
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.Item, {
    name: "reconnectInterval",
    label: "Reconnect Interval (ms)",
    rules: [{
      required: true,
      message: 'Please enter reconnect interval'
    }]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Input */ .pd, {
    type: "number",
    min: 1000,
    max: 10000,
    disabled: connected || connecting
  })))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Panel, {
    header: "Messages",
    key: "2",
    extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Tooltip */ .m_, {
      title: "Auto-scroll to bottom"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Switch */ .dO, {
      checked: autoScroll,
      onChange: setAutoScroll,
      size: "small"
    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ClearOutlined */ .ohj, null),
      size: "small",
      onClick: function onClick(e) {
        e.stopPropagation();
        handleClearMessages();
      }
    }))
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Input */ .pd, {
    placeholder: "Filter messages",
    value: filterText,
    onChange: function onChange(e) {
      return setFilterText(e.target.value);
    },
    allowClear: true,
    style: {
      marginBottom: 16
    },
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .InfoCircleOutlined */ .rUN, null)
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(MessageContainer, {
    id: "ws-message-container",
    theme: isDarkMode ? 'dark' : 'light'
  }, filteredMessages.length === 0 ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
    type: "secondary"
  }, "No messages") : filteredMessages.map(function (message, index) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(MessageItem, {
      key: index,
      type: message.type,
      status: message.status,
      theme: isDarkMode ? 'dark' : 'light'
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
      style: {
        display: 'flex',
        justifyContent: 'space-between',
        marginBottom: 4
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Tag */ .vw, {
      color: message.type === 'sent' ? 'blue' : 'green'
    }, message.type === 'sent' ? 'Sent' : 'Received'), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(TimeStamp, {
      theme: isDarkMode ? 'dark' : 'light'
    }, new Date(message.timestamp).toLocaleTimeString())), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("pre", {
      style: {
        margin: 0,
        whiteSpace: 'pre-wrap'
      }
    }, formatMessage(message.data)));
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV, {
    form: messageForm,
    layout: "inline",
    style: {
      display: 'flex',
      marginTop: 16
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.Item, {
    name: "message",
    style: {
      flex: 1,
      marginRight: 8
    },
    rules: [{
      required: true,
      message: 'Please enter a message'
    }]
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(TextArea, {
    placeholder: "Enter message (plain text or JSON)",
    autoSize: {
      minRows: 1,
      maxRows: 6
    },
    disabled: !connected
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.Item, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
    type: "primary",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .SendOutlined */ .jnF, null),
    onClick: handleSendMessage,
    disabled: !connected
  }, "Send"))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Drawer */ ._s, {
    title: "Advanced Settings",
    placement: "right",
    closable: true,
    onClose: toggleSettings,
    open: showSettings,
    width: 400
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Paragraph, null, "These settings allow you to configure the WebSocket connection behavior."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Divider */ .cG, null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV, {
    layout: "vertical"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.Item, {
    label: "Ping Interval (ms)",
    name: "pingInterval",
    initialValue: 30000
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Input */ .pd, {
    type: "number",
    min: 1000,
    max: 60000
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.Item, {
    label: "Ping Message",
    name: "pingMessage",
    initialValue: "{\"type\":\"ping\"}"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Input */ .pd, null)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.Item, {
    label: "Auto Reconnect",
    name: "autoReconnect",
    valuePropName: "checked",
    initialValue: true
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Switch */ .dO, null)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.Item, {
    label: "Debug Mode",
    name: "debugMode",
    valuePropName: "checked",
    initialValue: false
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Switch */ .dO, null)))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (WebSocketManager);

/***/ }),

/***/ 79459:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5544);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(96540);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(71468);
/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(2543);
/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_5__);



function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }




/**
 * Custom hook for real-time preview functionality
 * Handles instant updates, performance optimization, and WebSocket synchronization
 */
var useRealTimePreview = function useRealTimePreview(_ref) {
  var _ref$components = _ref.components,
    components = _ref$components === void 0 ? [] : _ref$components,
    onUpdateComponent = _ref.onUpdateComponent,
    onAddComponent = _ref.onAddComponent,
    onDeleteComponent = _ref.onDeleteComponent,
    websocketService = _ref.websocketService,
    _ref$updateDelay = _ref.updateDelay,
    updateDelay = _ref$updateDelay === void 0 ? 300 : _ref$updateDelay,
    _ref$throttleDelay = _ref.throttleDelay,
    throttleDelay = _ref$throttleDelay === void 0 ? 100 : _ref$throttleDelay,
    _ref$enableWebSocket = _ref.enableWebSocket,
    enableWebSocket = _ref$enableWebSocket === void 0 ? true : _ref$enableWebSocket;
  // State for tracking updates
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState, 2),
    isUpdating = _useState2[0],
    setIsUpdating = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState3, 2),
    lastUpdateTime = _useState4[0],
    setLastUpdateTime = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(new Map()),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState5, 2),
    pendingUpdates = _useState6[0],
    setPendingUpdates = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState7, 2),
    updateQueue = _useState8[0],
    setUpdateQueue = _useState8[1];

  // Refs for cleanup and performance
  var updateTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);
  var websocketRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(websocketService);
  var componentCacheRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(new Map());

  // Redux state
  var dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_4__/* .useDispatch */ .wA)();
  var websocketConnected = (0,react_redux__WEBPACK_IMPORTED_MODULE_4__/* .useSelector */ .d4)(function (state) {
    try {
      var _state$websocket;
      return (state === null || state === void 0 || (_state$websocket = state.websocket) === null || _state$websocket === void 0 ? void 0 : _state$websocket.connected) || false;
    } catch (error) {
      console.warn('Error accessing websocket state:', error);
      return false;
    }
  });

  // Update WebSocket reference when service changes
  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(function () {
    websocketRef.current = websocketService;
  }, [websocketService]);

  // Debounced update function for batching changes
  var debouncedUpdate = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((0,lodash__WEBPACK_IMPORTED_MODULE_5__.debounce)(function (updates) {
    if (updates.length === 0) return;
    setIsUpdating(true);

    // Process all pending updates
    updates.forEach(function (_ref2) {
      var type = _ref2.type,
        componentId = _ref2.componentId,
        data = _ref2.data;
      switch (type) {
        case 'update':
          if (onUpdateComponent) {
            onUpdateComponent(componentId, data);
          }
          break;
        case 'add':
          if (onAddComponent) {
            onAddComponent(data);
          }
          break;
        case 'delete':
          if (onDeleteComponent) {
            onDeleteComponent(componentId);
          }
          break;
        default:
          console.warn('Unknown update type:', type);
      }
    });

    // Send WebSocket updates if enabled and connected
    if (enableWebSocket && websocketConnected && websocketRef.current) {
      updates.forEach(function (_ref3) {
        var type = _ref3.type,
          componentId = _ref3.componentId,
          data = _ref3.data;
        websocketRef.current.send({
          type: "component_".concat(type),
          component_id: componentId,
          component_data: data,
          timestamp: new Date().toISOString()
        });
      });
    }
    setLastUpdateTime(new Date());
    setUpdateQueue([]);
    setPendingUpdates(new Map());

    // Clear updating state after a short delay
    setTimeout(function () {
      return setIsUpdating(false);
    }, 500);
  }, updateDelay), [onUpdateComponent, onAddComponent, onDeleteComponent, enableWebSocket, websocketConnected, updateDelay]);

  // Throttled function for immediate visual feedback
  var throttledVisualUpdate = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((0,lodash__WEBPACK_IMPORTED_MODULE_5__.throttle)(function (componentId, updates) {
    // Update component cache for immediate visual feedback
    var currentCache = componentCacheRef.current.get(componentId) || {};
    componentCacheRef.current.set(componentId, _objectSpread(_objectSpread({}, currentCache), updates));

    // Force re-render by updating a timestamp
    setLastUpdateTime(new Date());
  }, throttleDelay), [throttleDelay]);

  // Main update function
  var updateComponent = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (componentId, updates) {
    var immediate = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;
    if (!componentId) return;

    // Add to pending updates
    var currentPending = pendingUpdates.get(componentId) || {};
    var newPending = _objectSpread(_objectSpread({}, currentPending), updates);
    setPendingUpdates(function (prev) {
      return new Map(prev.set(componentId, newPending));
    });

    // Add to update queue
    setUpdateQueue(function (prev) {
      return [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(prev.filter(function (item) {
        return !(item.type === 'update' && item.componentId === componentId);
      })), [{
        type: 'update',
        componentId: componentId,
        data: newPending
      }]);
    });

    // Immediate visual feedback
    if (immediate) {
      throttledVisualUpdate(componentId, updates);
    }

    // Trigger debounced update
    debouncedUpdate(updateQueue);
  }, [pendingUpdates, updateQueue, debouncedUpdate, throttledVisualUpdate]);

  // Add component function
  var addComponent = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (componentData) {
    var immediate = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
    var componentId = componentData.id || Date.now().toString();
    var newComponent = _objectSpread(_objectSpread({}, componentData), {}, {
      id: componentId
    });
    setUpdateQueue(function (prev) {
      return [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(prev), [{
        type: 'add',
        componentId: componentId,
        data: newComponent
      }]);
    });
    if (immediate) {
      componentCacheRef.current.set(componentId, newComponent);
      setLastUpdateTime(new Date());
    }
    debouncedUpdate(updateQueue);
    return componentId;
  }, [updateQueue, debouncedUpdate]);

  // Delete component function
  var deleteComponent = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (componentId) {
    var immediate = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
    if (!componentId) return;
    setUpdateQueue(function (prev) {
      return [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(prev), [{
        type: 'delete',
        componentId: componentId
      }]);
    });
    if (immediate) {
      componentCacheRef.current["delete"](componentId);
      setLastUpdateTime(new Date());
    }
    debouncedUpdate(updateQueue);
  }, [updateQueue, debouncedUpdate]);

  // Get component with cached updates
  var getComponent = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (componentId) {
    var originalComponent = components.find(function (c) {
      return c.id === componentId;
    });
    var cachedUpdates = componentCacheRef.current.get(componentId);
    var pendingUpdate = pendingUpdates.get(componentId);
    return _objectSpread(_objectSpread(_objectSpread({}, originalComponent), cachedUpdates), pendingUpdate);
  }, [components, pendingUpdates]);

  // Get all components with cached updates
  var getAllComponents = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function () {
    return components.map(function (component) {
      return getComponent(component.id);
    });
  }, [components, getComponent]);

  // Force update function for manual refresh
  var forceUpdate = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function () {
    debouncedUpdate.flush();
    componentCacheRef.current.clear();
    setPendingUpdates(new Map());
    setUpdateQueue([]);
  }, [debouncedUpdate]);

  // WebSocket message handler
  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(function () {
    if (!enableWebSocket || !websocketRef.current) return;
    var handleWebSocketMessage = function handleWebSocketMessage(message) {
      var _message$type;
      if ((_message$type = message.type) !== null && _message$type !== void 0 && _message$type.startsWith('component_')) {
        var component_id = message.component_id,
          component_data = message.component_data,
          timestamp = message.timestamp;

        // Update component cache with remote changes
        if (component_data && component_id) {
          componentCacheRef.current.set(component_id, component_data);
          setLastUpdateTime(new Date(timestamp));
        }
      }
    };
    websocketRef.current.addEventListener('message', handleWebSocketMessage);
    return function () {
      if (websocketRef.current) {
        websocketRef.current.removeEventListener('message', handleWebSocketMessage);
      }
    };
  }, [enableWebSocket]);

  // Cleanup on unmount
  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(function () {
    return function () {
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
      debouncedUpdate.cancel();
      throttledVisualUpdate.cancel();
    };
  }, [debouncedUpdate, throttledVisualUpdate]);
  return {
    // State
    isUpdating: isUpdating,
    lastUpdateTime: lastUpdateTime,
    websocketConnected: websocketConnected,
    hasPendingUpdates: pendingUpdates.size > 0,
    // Functions
    updateComponent: updateComponent,
    addComponent: addComponent,
    deleteComponent: deleteComponent,
    getComponent: getComponent,
    getAllComponents: getAllComponents,
    forceUpdate: forceUpdate,
    // Utilities
    clearCache: function clearCache() {
      return componentCacheRef.current.clear();
    },
    getPendingUpdates: function getPendingUpdates() {
      return Array.from(pendingUpdates.entries());
    },
    getUpdateQueueSize: function getUpdateQueueSize() {
      return updateQueue.length;
    }
  };
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useRealTimePreview);

/***/ }),

/***/ 99578:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(57528);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(33966);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(36031);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(71606);





var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6;

function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }





// Import the core feature components with fallbacks
var ComponentBuilder, LayoutDesigner, ThemeManager, WebSocketManager;

// Import the new MVP feature components with fallbacks
var IntegratedTutorialAssistant, TestingTools, DataManagementDemo, EnhancedPerformanceMonitor, EnhancedCodeExporter;
try {
  ComponentBuilder = (__webpack_require__(16030)["default"]);
} catch (error) {
  console.warn('ComponentBuilder not available, using fallback');
  ComponentBuilder = function ComponentBuilder() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp, {
      title: "Component Builder"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Component Builder is loading..."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      type: "primary"
    }, "Add Button"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      style: {
        marginLeft: '8px'
      }
    }, "Add Text"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      style: {
        marginLeft: '8px'
      }
    }, "Add Input"));
  };
}
try {
  LayoutDesigner = (__webpack_require__(95505)["default"]);
} catch (error) {
  console.warn('LayoutDesigner not available, using fallback');
  LayoutDesigner = function LayoutDesigner() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp, {
      title: "Layout Designer"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Layout Designer is loading..."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      type: "primary"
    }, "Grid Layout"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      style: {
        marginLeft: '8px'
      }
    }, "Flex Layout"));
  };
}
try {
  ThemeManager = (__webpack_require__(71667)["default"]);
} catch (error) {
  console.warn('ThemeManager not available, using fallback');
  ThemeManager = function ThemeManager() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp, {
      title: "Theme Manager"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Theme Manager is loading..."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      type: "primary"
    }, "Primary Color"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      style: {
        marginLeft: '8px'
      }
    }, "Typography"));
  };
}
try {
  WebSocketManager = (__webpack_require__(49697)/* ["default"] */ .A);
} catch (error) {
  console.warn('WebSocketManager not available, using fallback');
  WebSocketManager = function WebSocketManager(_ref) {
    var onConnectionChange = _ref.onConnectionChange;
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp, {
      title: "WebSocket Manager"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, {
      direction: "vertical",
      style: {
        width: '100%'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Real-time collaboration features"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      type: "primary",
      onClick: function onClick() {
        console.log('WebSocket connection simulated');
        if (onConnectionChange) onConnectionChange(true);
      }
    }, "Connect"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      onClick: function onClick() {
        console.log('WebSocket disconnection simulated');
        if (onConnectionChange) onConnectionChange(false);
      }
    }, "Disconnect")));
  };
}

// Import Tutorial Assistant
try {
  IntegratedTutorialAssistant = (__webpack_require__(9771)/* ["default"] */ .A);
} catch (error) {
  console.warn('IntegratedTutorialAssistant not available, using fallback');
  IntegratedTutorialAssistant = function IntegratedTutorialAssistant() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp, {
      title: "Tutorial Assistant",
      style: {
        height: '100%'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Interactive tutorials and context-aware help system"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      type: "primary"
    }, "Start Tutorial"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      style: {
        marginLeft: '8px'
      }
    }, "View Help"));
  };
}

// Import Testing Tools
try {
  TestingTools = (__webpack_require__(11937)/* ["default"] */ .A);
} catch (error) {
  console.warn('TestingTools not available, using fallback');
  TestingTools = function TestingTools() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp, {
      title: "Testing Tools",
      style: {
        height: '100%'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Component testing, validation, and accessibility checks"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      type: "primary"
    }, "Run Tests"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      style: {
        marginLeft: '8px'
      }
    }, "Accessibility Check"));
  };
}

// Import Data Management
try {
  DataManagementDemo = (__webpack_require__(97481)["default"]);
} catch (error) {
  console.warn('DataManagementDemo not available, using fallback');
  DataManagementDemo = function DataManagementDemo() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp, {
      title: "Data Management",
      style: {
        height: '100%'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Data binding, state management, and flow visualization"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      type: "primary"
    }, "Manage Data"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      style: {
        marginLeft: '8px'
      }
    }, "View Flow"));
  };
}

// Import Performance Monitor
try {
  EnhancedPerformanceMonitor = (__webpack_require__(11398)/* ["default"] */ .A);
} catch (error) {
  console.warn('EnhancedPerformanceMonitor not available, using fallback');
  EnhancedPerformanceMonitor = function EnhancedPerformanceMonitor() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp, {
      title: "Performance Monitor",
      style: {
        height: '100%'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Bundle size tracking and optimization suggestions"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      type: "primary"
    }, "Analyze Performance"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      style: {
        marginLeft: '8px'
      }
    }, "View Metrics"));
  };
}

// Import Enhanced Code Exporter
try {
  EnhancedCodeExporter = (__webpack_require__(4090)/* ["default"] */ .A);
} catch (error) {
  console.warn('EnhancedCodeExporter not available, using fallback');
  EnhancedCodeExporter = function EnhancedCodeExporter() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp, {
      title: "Enhanced Export",
      style: {
        height: '100%'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Multi-framework export with TypeScript generation"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      type: "primary"
    }, "Export React"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      style: {
        marginLeft: '8px'
      }
    }, "Export Vue"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
      style: {
        marginLeft: '8px'
      }
    }, "Export Angular"));
  };
}
var Title = antd__WEBPACK_IMPORTED_MODULE_7__/* .Typography */ .o5.Title,
  Text = antd__WEBPACK_IMPORTED_MODULE_7__/* .Typography */ .o5.Text;
var IntegratedContainer = styled_components__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  height: 100vh;\n  background: #f5f5f5;\n  overflow: hidden;\n"])));
var HeaderBar = styled_components__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  background: #fff;\n  padding: 16px 24px;\n  border-bottom: 1px solid #f0f0f0;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.06);\n"])));
var ContentArea = styled_components__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  height: calc(100vh - 80px);\n  overflow: auto;\n  padding: 24px;\n"])));
var FeatureGrid = styled_components__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay.div(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  display: grid;\n  grid-template-columns: 1fr 1fr 1fr;\n  grid-template-rows: 1fr 1fr 1fr;\n  gap: 16px;\n  height: 100%;\n  min-height: 800px;\n"])));
var FeatureCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp)(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  height: 100%;\n  \n  .ant-card-body {\n    height: calc(100% - 57px);\n    overflow: auto;\n  }\n"])));
var DemoArea = (0,styled_components__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_7__/* .Card */ .Zp)(_templateObject6 || (_templateObject6 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  grid-column: 1 / -1;\n  margin-top: 24px;\n  \n  .demo-canvas {\n    min-height: 300px;\n    border: 2px dashed #d9d9d9;\n    border-radius: 8px;\n    padding: 20px;\n    background: #fafafa;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n  }\n"])));

/**
 * AppBuilderIntegrated - Comprehensive integrated app builder with all core features
 */
var AppBuilderIntegrated = function AppBuilderIntegrated() {
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)('overview'),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState, 2),
    activeFeature = _useState2[0],
    setActiveFeature = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)({
      components: [],
      layout: {
        type: 'grid',
        columns: 3
      },
      theme: {
        primaryColor: '#1890ff',
        fontFamily: 'Inter'
      },
      websocket: {
        connected: false,
        collaborators: []
      }
    }),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState3, 2),
    projectData = _useState4[0],
    setProjectData = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)({
      components: [],
      layout: null,
      theme: null,
      isBuilding: false
    }),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState5, 2),
    demoApp = _useState6[0],
    setDemoApp = _useState6[1];

  // Sample app creation workflow
  var createSampleApp = (0,react__WEBPACK_IMPORTED_MODULE_6__.useCallback)(/*#__PURE__*/(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default().mark(function _callee() {
    var sampleComponents, sampleLayout, sampleTheme;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default().wrap(function (_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          setDemoApp(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              isBuilding: true
            });
          });

          // Step 1: Add components
          sampleComponents = [{
            id: 'btn1',
            type: 'button',
            props: {
              text: 'Get Started',
              type: 'primary'
            },
            position: {
              x: 50,
              y: 50
            }
          }, {
            id: 'txt1',
            type: 'text',
            props: {
              text: 'Welcome to App Builder',
              size: 'large'
            },
            position: {
              x: 50,
              y: 100
            }
          }, {
            id: 'card1',
            type: 'card',
            props: {
              title: 'Feature Card',
              content: 'This is a sample card'
            },
            position: {
              x: 200,
              y: 50
            }
          }, {
            id: 'input1',
            type: 'input',
            props: {
              placeholder: 'Enter your name'
            },
            position: {
              x: 50,
              y: 200
            }
          }]; // Step 2: Apply layout
          sampleLayout = {
            type: 'grid',
            columns: 2,
            gap: '16px',
            responsive: true
          }; // Step 3: Apply theme
          sampleTheme = {
            primaryColor: '#52c41a',
            fontFamily: 'Inter, sans-serif',
            borderRadius: '8px',
            spacing: '16px'
          }; // Simulate building process
          _context.next = 1;
          return new Promise(function (resolve) {
            return setTimeout(resolve, 1000);
          });
        case 1:
          setDemoApp({
            components: sampleComponents,
            layout: sampleLayout,
            theme: sampleTheme,
            isBuilding: false
          });
          setProjectData(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              components: sampleComponents,
              layout: sampleLayout,
              theme: sampleTheme
            });
          });
        case 2:
        case "end":
          return _context.stop();
      }
    }, _callee);
  })), []);
  var tabItems = (0,react__WEBPACK_IMPORTED_MODULE_6__.useMemo)(function () {
    var _demoApp$layout, _demoApp$layout2;
    return [{
      key: 'overview',
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .RocketOutlined */ .PKb, null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("span", null, "Integrated Builder")),
      children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Alert */ .Fc, {
        message: "App Builder Enhanced - All Features Integrated",
        description: "This integrated view demonstrates all four core features working together: Component Builder, Layout Designer, Theme Manager, and WebSocket Manager.",
        type: "success",
        showIcon: true,
        style: {
          marginBottom: '24px'
        }
      }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(FeatureGrid, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(FeatureCard, {
        title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .AppstoreOutlined */ .rS9, null), "Component Builder"),
        extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
          size: "small",
          onClick: function onClick() {
            return setActiveFeature('components');
          }
        }, "Open")
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(ComponentBuilder, {
        onComponentAdd: function onComponentAdd(component) {
          setProjectData(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              components: [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(prev.components), [component])
            });
          });
        }
      })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(FeatureCard, {
        title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .LayoutOutlined */ .hy2, null), "Layout Designer"),
        extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
          size: "small",
          onClick: function onClick() {
            return setActiveFeature('layouts');
          }
        }, "Open")
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(LayoutDesigner, {
        components: projectData.components,
        onLayoutChange: function onLayoutChange(layout) {
          setProjectData(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              layout: layout
            });
          });
        }
      })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(FeatureCard, {
        title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .BgColorsOutlined */ .Ebl, null), "Theme Manager"),
        extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
          size: "small",
          onClick: function onClick() {
            return setActiveFeature('themes');
          }
        }, "Open")
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(ThemeManager, {
        currentTheme: projectData.theme,
        onThemeChange: function onThemeChange(theme) {
          setProjectData(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              theme: theme
            });
          });
        }
      })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(FeatureCard, {
        title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .ApiOutlined */ .bfv, null), "WebSocket Manager"),
        extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
          size: "small",
          onClick: function onClick() {
            return setActiveFeature('websocket');
          }
        }, "Open")
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(WebSocketManager, {
        onConnectionChange: function onConnectionChange(status) {
          setProjectData(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              websocket: _objectSpread(_objectSpread({}, prev.websocket), {}, {
                connected: status
              })
            });
          });
        }
      })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(FeatureCard, {
        title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .QuestionCircleOutlined */ .faO, null), "Tutorial Assistant"),
        extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
          size: "small",
          onClick: function onClick() {
            return setActiveFeature('tutorial');
          }
        }, "Open")
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
        style: {
          padding: '16px',
          textAlign: 'center'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Interactive tutorials and context-aware help system"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
        type: "primary",
        size: "small"
      }, "Start Tutorial"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(FeatureCard, {
        title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .BugOutlined */ .NhG, null), "Testing Tools"),
        extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
          size: "small",
          onClick: function onClick() {
            return setActiveFeature('testing');
          }
        }, "Open")
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
        style: {
          padding: '16px',
          textAlign: 'center'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Component testing, validation, and accessibility checks"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
        type: "primary",
        size: "small"
      }, "Run Tests"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(FeatureCard, {
        title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .DatabaseOutlined */ .ose, null), "Data Management"),
        extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
          size: "small",
          onClick: function onClick() {
            return setActiveFeature('data');
          }
        }, "Open")
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
        style: {
          padding: '16px',
          textAlign: 'center'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Data binding, state management, and flow visualization"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
        type: "primary",
        size: "small"
      }, "Manage Data"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(FeatureCard, {
        title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .DashboardOutlined */ .zpd, null), "Performance Monitor"),
        extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
          size: "small",
          onClick: function onClick() {
            return setActiveFeature('performance');
          }
        }, "Open")
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
        style: {
          padding: '16px',
          textAlign: 'center'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Bundle size tracking and optimization suggestions"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
        type: "primary",
        size: "small"
      }, "Analyze"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(FeatureCard, {
        title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .ExportOutlined */ .PZg, null), "Enhanced Export"),
        extra: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
          size: "small",
          onClick: function onClick() {
            return setActiveFeature('export');
          }
        }, "Open")
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
        style: {
          padding: '16px',
          textAlign: 'center'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("p", null, "Multi-framework export with TypeScript generation"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
        type: "primary",
        size: "small"
      }, "Export Code")))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(DemoArea, {
        title: "Sample App Demonstration"
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Row */ .fI, {
        gutter: [24, 24]
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Col */ .fv, {
        xs: 24,
        md: 12
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, {
        direction: "vertical",
        style: {
          width: '100%'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Title, {
        level: 4
      }, "Create Sample App"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, {
        type: "secondary"
      }, "Demonstrate the complete workflow from component creation to styled, collaborative application."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
        type: "primary",
        size: "large",
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .PlayCircleOutlined */ .VgC, null),
        onClick: createSampleApp,
        loading: demoApp.isBuilding
      }, "Build Sample App"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Col */ .fv, {
        xs: 24,
        md: 12
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
        className: "demo-canvas"
      }, demoApp.isBuilding ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
        style: {
          textAlign: 'center'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Title, {
        level: 4
      }, "Building Sample App..."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, {
        type: "secondary"
      }, "Adding components, applying layout, styling theme...")) : demoApp.components.length > 0 ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
        style: {
          width: '100%'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Title, {
        level: 4
      }, "Sample App Preview"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
        style: {
          display: 'grid',
          gridTemplateColumns: "repeat(".concat(((_demoApp$layout = demoApp.layout) === null || _demoApp$layout === void 0 ? void 0 : _demoApp$layout.columns) || 2, ", 1fr)"),
          gap: ((_demoApp$layout2 = demoApp.layout) === null || _demoApp$layout2 === void 0 ? void 0 : _demoApp$layout2.gap) || '16px',
          marginTop: '16px'
        }
      }, demoApp.components.map(function (component) {
        var _demoApp$theme;
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
          key: component.id,
          style: {
            padding: '12px',
            border: '1px solid #d9d9d9',
            borderRadius: ((_demoApp$theme = demoApp.theme) === null || _demoApp$theme === void 0 ? void 0 : _demoApp$theme.borderRadius) || '4px',
            background: '#fff'
          }
        }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, {
          strong: true
        }, component.type, ": "), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, null, component.props.text || component.props.title || component.props.placeholder));
      }))) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
        style: {
          textAlign: 'center'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Title, {
        level: 4,
        type: "secondary"
      }, "Ready to Build"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, {
        type: "secondary"
      }, "Click \"Build Sample App\" to see all features in action")))))))
    }, {
      key: 'components',
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .AppstoreOutlined */ .rS9, null), "Component Builder"),
      children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(ComponentBuilder, null)
    }, {
      key: 'layouts',
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .LayoutOutlined */ .hy2, null), "Layout Designer"),
      children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(LayoutDesigner, null)
    }, {
      key: 'themes',
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .BgColorsOutlined */ .Ebl, null), "Theme Manager"),
      children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(ThemeManager, null)
    }, {
      key: 'websocket',
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .ApiOutlined */ .bfv, null), "WebSocket Manager"),
      children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(WebSocketManager, null)
    }, {
      key: 'tutorial',
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .QuestionCircleOutlined */ .faO, null), "Tutorial Assistant"),
      children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(IntegratedTutorialAssistant, {
        enableAutoStart: false,
        showContextualHelp: true,
        onTutorialComplete: function onTutorialComplete(tutorialId) {
          console.log('Tutorial completed:', tutorialId);
        },
        onTutorialSkip: function onTutorialSkip(tutorialId) {
          console.log('Tutorial skipped:', tutorialId);
        },
        features: ['components', 'layouts', 'themes', 'websocket', 'testing', 'data', 'performance', 'export']
      })
    }, {
      key: 'testing',
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .BugOutlined */ .NhG, null), "Testing Tools"),
      children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(TestingTools, {
        components: projectData.components || [],
        onTestComplete: function onTestComplete(testType, results) {
          console.log('Test completed:', testType, results);
        },
        onTestStart: function onTestStart(testType) {
          console.log('Test started:', testType);
        },
        enabledTests: ['component', 'accessibility', 'performance', 'responsive'],
        autoRun: false,
        showMetrics: true,
        compact: false
      })
    }, {
      key: 'data',
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .DatabaseOutlined */ .ose, null), "Data Management"),
      children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(DataManagementDemo, null)
    }, {
      key: 'performance',
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .DashboardOutlined */ .zpd, null), "Performance Monitor"),
      children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(EnhancedPerformanceMonitor, {
        enabled: true,
        initiallyMinimized: false,
        refreshInterval: 5000,
        wsUrl: "ws://localhost:8000/ws",
        showNetworkStatus: true,
        showMemoryUsage: true,
        showRenderCounts: true,
        showWebSocketStatus: true
      })
    }, {
      key: 'export',
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .ExportOutlined */ .PZg, null), "Enhanced Export"),
      children: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(EnhancedCodeExporter, {
        components: projectData.components || [],
        layouts: projectData.layout ? [projectData.layout] : [],
        theme: projectData.theme || {},
        onExport: function onExport(exportData) {
          console.log('Code exported:', exportData);
        },
        onPreview: function onPreview(previewData) {
          console.log('Code preview:', previewData);
        },
        compact: false
      })
    }];
  }, [projectData, demoApp, createSampleApp]);
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(IntegratedContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(HeaderBar, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Title, {
    level: 3,
    style: {
      margin: 0,
      color: '#1890ff'
    }
  }, "App Builder Enhanced"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, {
    type: "secondary"
  }, "Integrated Development Environment")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .SaveOutlined */ .ylI, null)
  }, "Save Project"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .ShareAltOutlined */ .f5H, null)
  }, "Share"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Button */ .$n, {
    type: "primary",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_8__/* .PlayCircleOutlined */ .VgC, null)
  }, "Preview"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(ContentArea, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_7__/* .Tabs */ .tU, {
    activeKey: activeFeature,
    onChange: setActiveFeature,
    type: "card",
    size: "large",
    items: tabItems
  })));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AppBuilderIntegrated);

/***/ })

}]);