"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[64],{

/***/ 12237:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (/* binding */ EnhancedKeyboardShortcuts)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(33966);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(36031);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(71606);
/* harmony import */ var _design_system__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(79146);
/* harmony import */ var _hooks_useKeyboardShortcuts__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(31960);



var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5;
/**
 * Enhanced Keyboard Shortcuts System
 * 
 * Comprehensive keyboard shortcuts with visual feedback, customization,
 * and quick action toolbars for improved productivity.
 */







var Text = antd__WEBPACK_IMPORTED_MODULE_4__/* .Typography */ .o5.Text,
  Title = antd__WEBPACK_IMPORTED_MODULE_4__/* .Typography */ .o5.Title;
var TabPane = antd__WEBPACK_IMPORTED_MODULE_4__/* .Tabs */ .tU.TabPane;

// Enhanced styled components
var ShortcutContainer = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 9999;\n  backdrop-filter: blur(4px);\n"])));
var ShortcutPanel = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  background: ", ";\n  border-radius: ", ";\n  box-shadow: ", ";\n  max-width: 800px;\n  max-height: 80vh;\n  overflow-y: auto;\n  padding: ", ";\n  margin: ", ";\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.background.paper, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.borderRadius.lg, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.shadows.xl, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[6], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[4]);
var QuickActionBar = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  position: fixed;\n  top: 20px;\n  right: 20px;\n  background: ", ";\n  border-radius: ", ";\n  box-shadow: ", ";\n  padding: ", ";\n  display: flex;\n  gap: ", ";\n  z-index: 1000;\n  border: 1px solid ", ";\n  backdrop-filter: blur(8px);\n  \n  &.hidden {\n    transform: translateY(-100%);\n    opacity: 0;\n    pointer-events: none;\n  }\n  \n  transition: all 0.3s ease;\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.background.paper, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.borderRadius.lg, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.shadows.lg, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[1], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.border.light);
var ShortcutKey = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.span(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  background: ", ";\n  border: 1px solid ", ";\n  border-radius: ", ";\n  padding: 2px 6px;\n  font-family: ", ";\n  font-size: ", ";\n  font-weight: ", ";\n  color: ", ";\n  margin: 0 2px;\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.neutral[100], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.border.medium, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.borderRadius.sm, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.typography.fontFamily.mono, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.typography.fontSize.xs, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.typography.fontWeight.medium, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.text.primary);
var ShortcutFeedback = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  position: fixed;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  background: ", ";\n  color: white;\n  padding: ", " ", ";\n  border-radius: ", ";\n  box-shadow: ", ";\n  z-index: 10000;\n  display: flex;\n  align-items: center;\n  gap: ", ";\n  font-weight: ", ";\n  animation: shortcutFeedback 0.8s ease-out;\n  \n  @keyframes shortcutFeedback {\n    0% {\n      opacity: 0;\n      transform: translate(-50%, -50%) scale(0.8);\n    }\n    20% {\n      opacity: 1;\n      transform: translate(-50%, -50%) scale(1.05);\n    }\n    100% {\n      opacity: 0;\n      transform: translate(-50%, -50%) scale(1);\n    }\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.main, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[3], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[4], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.borderRadius.lg, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.shadows.xl, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.typography.fontWeight.semibold);

// Default keyboard shortcuts configuration
var DEFAULT_SHORTCUTS = {
  // File operations
  'ctrl+s': {
    action: 'save',
    label: 'Save Project',
    category: 'File',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .SaveOutlined */ .ylI, null)
  },
  'ctrl+n': {
    action: 'new',
    label: 'New Component',
    category: 'File',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .FileAddOutlined */ .iUk, null)
  },
  // Edit operations
  'ctrl+c': {
    action: 'copy',
    label: 'Copy Component',
    category: 'Edit',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .CopyOutlined */ .wq3, null)
  },
  'ctrl+v': {
    action: 'paste',
    label: 'Paste Component',
    category: 'Edit',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ScissorOutlined */ .p9w, null)
  },
  'ctrl+x': {
    action: 'cut',
    label: 'Cut Component',
    category: 'Edit',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ScissorOutlined */ .p9w, null)
  },
  'ctrl+z': {
    action: 'undo',
    label: 'Undo',
    category: 'Edit',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .UndoOutlined */ .Xrf, null)
  },
  'ctrl+y': {
    action: 'redo',
    label: 'Redo',
    category: 'Edit',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .RedoOutlined */ .zYO, null)
  },
  'delete': {
    action: 'delete',
    label: 'Delete Component',
    category: 'Edit',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .DeleteOutlined */ .SUY, null)
  },
  // View operations
  'f11': {
    action: 'fullscreen',
    label: 'Toggle Fullscreen',
    category: 'View',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .FullscreenOutlined */ .KrH, null)
  },
  'ctrl+shift+p': {
    action: 'preview',
    label: 'Toggle Preview',
    category: 'View',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .EyeOutlined */ .Om2, null)
  },
  'ctrl+f': {
    action: 'search',
    label: 'Search Components',
    category: 'View',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .SearchOutlined */ .VrN, null)
  },
  // Navigation
  'alt+1': {
    action: 'focusPalette',
    label: 'Focus Component Palette',
    category: 'Navigation',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ThunderboltOutlined */ .CwG, null)
  },
  'alt+2': {
    action: 'focusCanvas',
    label: 'Focus Canvas',
    category: 'Navigation',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ThunderboltOutlined */ .CwG, null)
  },
  'alt+3': {
    action: 'focusProperties',
    label: 'Focus Properties',
    category: 'Navigation',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ThunderboltOutlined */ .CwG, null)
  },
  // Help
  'f1': {
    action: 'help',
    label: 'Show Help',
    category: 'Help',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .QuestionCircleOutlined */ .faO, null)
  },
  'ctrl+shift+k': {
    action: 'shortcuts',
    label: 'Show Shortcuts',
    category: 'Help',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ControlOutlined */ .aM7, null)
  },
  // Quick actions
  'ctrl+space': {
    action: 'quickActions',
    label: 'Quick Actions',
    category: 'Quick',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ThunderboltOutlined */ .CwG, null)
  },
  'escape': {
    action: 'escape',
    label: 'Cancel/Close',
    category: 'Quick',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .CloseOutlined */ .r$3, null)
  }
};
function EnhancedKeyboardShortcuts(_ref) {
  var onAction = _ref.onAction,
    _ref$showQuickActions = _ref.showQuickActions,
    showQuickActions = _ref$showQuickActions === void 0 ? true : _ref$showQuickActions,
    _ref$enableCustomizat = _ref.enableCustomization,
    enableCustomization = _ref$enableCustomizat === void 0 ? true : _ref$enableCustomizat,
    _ref$enableFeedback = _ref.enableFeedback,
    enableFeedback = _ref$enableFeedback === void 0 ? true : _ref$enableFeedback;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    showShortcutsPanel = _useState2[0],
    setShowShortcutsPanel = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(showQuickActions),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState3, 2),
    showQuickActionBar = _useState4[0],
    setShowQuickActionBar = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(DEFAULT_SHORTCUTS),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState5, 2),
    shortcuts = _useState6[0],
    setShortcuts = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(''),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState7, 2),
    feedbackMessage = _useState8[0],
    setFeedbackMessage = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(''),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState9, 2),
    searchTerm = _useState0[0],
    setSearchTerm = _useState0[1];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('All'),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState1, 2),
    activeCategory = _useState10[0],
    setActiveCategory = _useState10[1];
  var feedbackTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);

  // Get categories for filtering
  var categories = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function () {
    var cats = ['All'].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(new Set(Object.values(shortcuts).map(function (s) {
      return s.category;
    }))));
    return cats;
  }, [shortcuts]);

  // Filter shortcuts based on search and category
  var filteredShortcuts = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function () {
    return Object.entries(shortcuts).filter(function (_ref2) {
      var _ref3 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_ref2, 2),
        key = _ref3[0],
        shortcut = _ref3[1];
      var matchesSearch = !searchTerm || shortcut.label.toLowerCase().includes(searchTerm.toLowerCase()) || key.toLowerCase().includes(searchTerm.toLowerCase());
      var matchesCategory = activeCategory === 'All' || shortcut.category === activeCategory;
      return matchesSearch && matchesCategory;
    });
  }, [shortcuts, searchTerm, activeCategory]);

  // Show feedback message
  var showFeedback = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (message, icon) {
    if (!enableFeedback) return;
    setFeedbackMessage({
      text: message,
      icon: icon
    });
    if (feedbackTimeoutRef.current) {
      clearTimeout(feedbackTimeoutRef.current);
    }
    feedbackTimeoutRef.current = setTimeout(function () {
      setFeedbackMessage('');
    }, 800);
  }, [enableFeedback]);

  // Handle shortcut action
  var handleShortcutAction = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (action, shortcutKey) {
    var shortcut = shortcuts[shortcutKey];
    if (onAction) {
      onAction(action, shortcutKey);
    }

    // Show feedback
    if (shortcut) {
      showFeedback(shortcut.label, shortcut.icon);
    }

    // Handle built-in actions
    switch (action) {
      case 'shortcuts':
        setShowShortcutsPanel(true);
        break;
      case 'escape':
        setShowShortcutsPanel(false);
        break;
      default:
        break;
    }
  }, [shortcuts, onAction, showFeedback]);

  // Create shortcuts object for the hook
  var shortcutHandlers = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function () {
    var handlers = {};
    Object.entries(shortcuts).forEach(function (_ref4) {
      var _ref5 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_ref4, 2),
        key = _ref5[0],
        shortcut = _ref5[1];
      handlers[key] = function () {
        return handleShortcutAction(shortcut.action, key);
      };
    });
    return handlers;
  }, [shortcuts, handleShortcutAction]);

  // Register keyboard shortcuts
  (0,_hooks_useKeyboardShortcuts__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A)(shortcutHandlers);

  // Quick action buttons
  var quickActions = [{
    action: 'save',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .SaveOutlined */ .ylI, null),
    tooltip: 'Save (Ctrl+S)',
    color: '#52c41a'
  }, {
    action: 'undo',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .UndoOutlined */ .Xrf, null),
    tooltip: 'Undo (Ctrl+Z)',
    color: '#1890ff'
  }, {
    action: 'redo',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .RedoOutlined */ .zYO, null),
    tooltip: 'Redo (Ctrl+Y)',
    color: '#1890ff'
  }, {
    action: 'copy',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .CopyOutlined */ .wq3, null),
    tooltip: 'Copy (Ctrl+C)',
    color: '#722ed1'
  }, {
    action: 'paste',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ScissorOutlined */ .p9w, null),
    tooltip: 'Paste (Ctrl+V)',
    color: '#722ed1'
  }, {
    action: 'preview',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .EyeOutlined */ .Om2, null),
    tooltip: 'Preview (Ctrl+Shift+P)',
    color: '#fa8c16'
  }];
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(react__WEBPACK_IMPORTED_MODULE_3__.Fragment, null, showQuickActionBar && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(QuickActionBar, {
    className: showQuickActionBar ? '' : 'hidden'
  }, quickActions.map(function (action, index) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Tooltip */ .m_, {
      key: index,
      title: action.tooltip,
      placement: "bottom"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
      type: "text",
      icon: action.icon,
      size: "small",
      onClick: function onClick() {
        return handleShortcutAction(action.action);
      },
      style: {
        color: action.color,
        border: "1px solid ".concat(action.color, "20"),
        background: "".concat(action.color, "10")
      }
    }));
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Divider */ .cG, {
    type: "vertical",
    style: {
      margin: '0 4px'
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Tooltip */ .m_, {
    title: "Keyboard Shortcuts (Ctrl+Shift+K)",
    placement: "bottom"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
    type: "text",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ControlOutlined */ .aM7, null),
    size: "small",
    onClick: function onClick() {
      return setShowShortcutsPanel(true);
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Tooltip */ .m_, {
    title: "Hide Quick Actions",
    placement: "bottom"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
    type: "text",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .CloseOutlined */ .r$3, null),
    size: "small",
    onClick: function onClick() {
      return setShowQuickActionBar(false);
    }
  }))), !showQuickActionBar && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .FloatButton */ .ff, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ThunderboltOutlined */ .CwG, null),
    tooltip: "Show Quick Actions",
    onClick: function onClick() {
      return setShowQuickActionBar(true);
    },
    style: {
      right: 24,
      top: 24
    }
  }), showShortcutsPanel && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ShortcutContainer, {
    onClick: function onClick() {
      return setShowShortcutsPanel(false);
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ShortcutPanel, {
    onClick: function onClick(e) {
      return e.stopPropagation();
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
    style: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[4]
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Title, {
    level: 3,
    style: {
      margin: 0
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ControlOutlined */ .aM7, null), " Keyboard Shortcuts"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
    type: "text",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .CloseOutlined */ .r$3, null),
    onClick: function onClick() {
      return setShowShortcutsPanel(false);
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, {
    direction: "vertical",
    size: "large",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
    style: {
      display: 'flex',
      gap: _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2]
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Input */ .pd, {
    placeholder: "Search shortcuts...",
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .SearchOutlined */ .VrN, null),
    value: searchTerm,
    onChange: function onChange(e) {
      return setSearchTerm(e.target.value);
    },
    allowClear: true,
    style: {
      flex: 1
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Tabs */ .tU, {
    activeKey: activeCategory,
    onChange: setActiveCategory
  }, categories.map(function (category) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(TabPane, {
      tab: category,
      key: category
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
      style: {
        maxHeight: '400px',
        overflowY: 'auto'
      }
    }, filteredShortcuts.filter(function (_ref6) {
      var _ref7 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_ref6, 2),
        shortcut = _ref7[1];
      return activeCategory === 'All' || shortcut.category === activeCategory;
    }).map(function (_ref8) {
      var _ref9 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_ref8, 2),
        key = _ref9[0],
        shortcut = _ref9[1];
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Card */ .Zp, {
        key: key,
        size: "small",
        style: {
          marginBottom: _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2]
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
        style: {
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, null, shortcut.icon, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
        strong: true
      }, shortcut.label)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", null, key.split('+').map(function (k, i) {
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ShortcutKey, {
          key: i
        }, k.toUpperCase());
      }))));
    })));
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Alert */ .Fc, {
    message: "Pro Tip",
    description: "Press Ctrl+Shift+K anytime to view this shortcuts panel. Most shortcuts work globally throughout the App Builder.",
    type: "info",
    showIcon: true
  })))), feedbackMessage && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ShortcutFeedback, null, feedbackMessage.icon, feedbackMessage.text));
}

/***/ }),

/***/ 37812:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (/* binding */ UXEnhancedPropertyEditor)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(58168);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(33966);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(36031);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(71606);
/* harmony import */ var _design_system__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(79146);




var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6, _templateObject7, _templateObject8;
function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t["return"] || t["return"](); } finally { if (u) throw o; } } }; }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * UX Enhanced Property Editor
 * 
 * A comprehensive property editor with enhanced UI/UX features:
 * - Intuitive form controls with better validation
 * - Real-time preview integration
 * - Property grouping and organization
 * - Contextual help and tooltips
 * - Improved visual hierarchy
 * - Accessibility enhancements
 */






var Title = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Title,
  Text = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Text,
  Paragraph = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Paragraph;
var Option = antd__WEBPACK_IMPORTED_MODULE_5__/* .Select */ .l6.Option;
var Panel = antd__WEBPACK_IMPORTED_MODULE_5__/* .Collapse */ .SD.Panel;
var TabPane = antd__WEBPACK_IMPORTED_MODULE_5__/* .Tabs */ .tU.TabPane;
var TextArea = antd__WEBPACK_IMPORTED_MODULE_5__/* .Input */ .pd.TextArea;

// Enhanced styled components
var PropertyEditorContainer = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  background: ", ";\n  border-radius: ", ";\n  box-shadow: ", ";\n  overflow: hidden;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  border: 1px solid ", ";\n"])), _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.background.paper, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.borderRadius.lg, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.shadows.md, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.border.light);
var PropertyHeader = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  padding: ", ";\n  background: linear-gradient(135deg, ", " 0%, ", " 100%);\n  color: ", ";\n  border-bottom: 1px solid ", ";\n  \n  .ant-typography {\n    color: ", " !important;\n    margin-bottom: ", ";\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[4], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.secondary.main, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.primary.main, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.background.paper, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.border.light, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.background.paper, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[1]);
var PropertyContent = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  flex: 1;\n  overflow-y: auto;\n  \n  /* Custom scrollbar */\n  &::-webkit-scrollbar {\n    width: 6px;\n  }\n  \n  &::-webkit-scrollbar-track {\n    background: ", ";\n  }\n  \n  &::-webkit-scrollbar-thumb {\n    background: ", ";\n    border-radius: ", ";\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.background.secondary, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.border.medium, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.borderRadius.full);
var PropertyGroup = (0,styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_5__/* .Card */ .Zp)(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  margin: ", ";\n  border-radius: ", ";\n  \n  .ant-card-head {\n    background: ", ";\n    border-bottom: 1px solid ", ";\n    padding: ", " ", ";\n    min-height: auto;\n  }\n  \n  .ant-card-body {\n    padding: ", ";\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.borderRadius.md, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.background.tertiary, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.border.light, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[3], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[3]);
var PropertyField = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  margin-bottom: ", ";\n  \n  .ant-form-item {\n    margin-bottom: ", ";\n  }\n  \n  .ant-form-item-label {\n    padding-bottom: ", ";\n    \n    label {\n      font-weight: ", ";\n      color: ", ";\n      font-size: ", ";\n    }\n  }\n  \n  .property-help {\n    margin-top: ", ";\n    font-size: ", ";\n    color: ", ";\n    line-height: ", ";\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[3], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[1], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.typography.fontWeight.medium, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.text.primary, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.typography.fontSize.sm, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[1], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.typography.fontSize.xs, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.text.secondary, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.typography.lineHeight.normal);
var ValidationMessage = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject6 || (_templateObject6 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  margin-top: ", ";\n  padding: ", " ", ";\n  border-radius: ", ";\n  font-size: ", ";\n  \n  &.error {\n    background: ", ";\n    color: ", ";\n    border: 1px solid ", ";\n  }\n  \n  &.warning {\n    background: ", ";\n    color: ", ";\n    border: 1px solid ", ";\n  }\n  \n  &.success {\n    background: ", ";\n    color: ", ";\n    border: 1px solid ", ";\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[1], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[1], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.borderRadius.sm, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.typography.fontSize.xs, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.error.light, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.error.dark, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.error.main, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.warning.light, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.warning.dark, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.warning.main, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.success.light, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.success.dark, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.success.main);
var PreviewToggle = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject7 || (_templateObject7 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  position: absolute;\n  top: ", ";\n  right: ", ";\n  z-index: 10;\n"])), _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2]);
var QuickActions = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject8 || (_templateObject8 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  padding: ", " ", ";\n  background: ", ";\n  border-top: 1px solid ", ";\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n"])), _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[4], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.background.secondary, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.border.light);

// Property validation rules
var VALIDATION_RULES = {
  required: function required(value) {
    return value !== undefined && value !== null && value !== '';
  },
  minLength: function minLength(min) {
    return function (value) {
      return !value || value.length >= min;
    };
  },
  maxLength: function maxLength(max) {
    return function (value) {
      return !value || value.length <= max;
    };
  },
  pattern: function pattern(regex) {
    return function (value) {
      return !value || regex.test(value);
    };
  },
  range: function range(min, max) {
    return function (value) {
      return !value || value >= min && value <= max;
    };
  },
  url: function url(value) {
    return !value || /^https?:\/\/.+/.test(value);
  },
  color: function color(value) {
    return !value || /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(value);
  }
};

// Enhanced property schemas with usage frequency and better organization
var PROPERTY_SCHEMAS = {
  text: {
    content: {
      type: 'textarea',
      label: 'Text Content',
      placeholder: 'Enter your text here...',
      help: 'The main text content to display. Supports basic formatting.',
      validation: [VALIDATION_RULES.required],
      group: 'essential',
      priority: 1,
      usage: 'high'
    },
    type: {
      type: 'select',
      label: 'Text Style',
      options: [{
        value: 'paragraph',
        label: 'Paragraph',
        description: 'Regular body text'
      }, {
        value: 'title',
        label: 'Title',
        description: 'Large heading text'
      }, {
        value: 'secondary',
        label: 'Secondary',
        description: 'Muted text'
      }, {
        value: 'warning',
        label: 'Warning',
        description: 'Warning text'
      }, {
        value: 'danger',
        label: 'Danger',
        description: 'Error or danger text'
      }],
      defaultValue: 'paragraph',
      help: 'Choose the visual style and semantic meaning of the text',
      group: 'essential',
      priority: 2,
      usage: 'high'
    },
    strong: {
      type: 'switch',
      label: 'Bold Text',
      help: 'Make text bold and emphasize importance',
      group: 'typography',
      priority: 3,
      usage: 'medium'
    },
    italic: {
      type: 'switch',
      label: 'Italic',
      help: 'Make text italic/emphasized',
      group: 'typography'
    },
    underline: {
      type: 'switch',
      label: 'Underline',
      help: 'Add underline decoration',
      group: 'typography'
    }
  },
  button: {
    text: {
      type: 'input',
      label: 'Button Text',
      placeholder: 'Enter button text...',
      help: 'The text displayed on the button',
      validation: [VALIDATION_RULES.required, VALIDATION_RULES.maxLength(50)],
      group: 'content'
    },
    type: {
      type: 'select',
      label: 'Button Type',
      options: [{
        value: 'default',
        label: 'Default'
      }, {
        value: 'primary',
        label: 'Primary'
      }, {
        value: 'dashed',
        label: 'Dashed'
      }, {
        value: 'text',
        label: 'Text'
      }, {
        value: 'link',
        label: 'Link'
      }],
      defaultValue: 'default',
      help: 'Visual style of the button',
      group: 'appearance'
    },
    size: {
      type: 'select',
      label: 'Size',
      options: [{
        value: 'small',
        label: 'Small'
      }, {
        value: 'middle',
        label: 'Medium'
      }, {
        value: 'large',
        label: 'Large'
      }],
      defaultValue: 'middle',
      help: 'Size of the button',
      group: 'layout'
    },
    disabled: {
      type: 'switch',
      label: 'Disabled',
      help: 'Disable button interaction',
      group: 'behavior'
    },
    action: {
      type: 'input',
      label: 'Action',
      placeholder: 'Enter action name...',
      help: 'Action to perform when clicked',
      group: 'behavior'
    }
  },
  header: {
    title: {
      type: 'input',
      label: 'Title',
      placeholder: 'Enter header title...',
      help: 'Main title text',
      validation: [VALIDATION_RULES.required],
      group: 'content'
    },
    subtitle: {
      type: 'input',
      label: 'Subtitle',
      placeholder: 'Enter subtitle...',
      help: 'Optional subtitle text',
      group: 'content'
    },
    alignment: {
      type: 'select',
      label: 'Alignment',
      options: [{
        value: 'left',
        label: 'Left'
      }, {
        value: 'center',
        label: 'Center'
      }, {
        value: 'right',
        label: 'Right'
      }],
      defaultValue: 'center',
      help: 'Text alignment',
      group: 'layout'
    },
    background: {
      type: 'color',
      label: 'Background Color',
      help: 'Background color of the header',
      group: 'appearance'
    },
    textColor: {
      type: 'color',
      label: 'Text Color',
      help: 'Color of the text',
      group: 'appearance'
    }
  },
  input: {
    placeholder: {
      type: 'input',
      label: 'Placeholder Text',
      placeholder: 'Enter placeholder text...',
      help: 'Text shown when input is empty',
      group: 'content',
      priority: 1,
      usage: 'high'
    },
    value: {
      type: 'input',
      label: 'Default Value',
      placeholder: 'Enter default value...',
      help: 'Initial value of the input',
      group: 'content',
      priority: 2,
      usage: 'medium'
    },
    type: {
      type: 'select',
      label: 'Input Type',
      options: [{
        value: 'text',
        label: 'Text'
      }, {
        value: 'email',
        label: 'Email'
      }, {
        value: 'password',
        label: 'Password'
      }, {
        value: 'number',
        label: 'Number'
      }, {
        value: 'tel',
        label: 'Telephone'
      }, {
        value: 'url',
        label: 'URL'
      }, {
        value: 'search',
        label: 'Search'
      }],
      defaultValue: 'text',
      help: 'Type of input field',
      group: 'behavior',
      priority: 3,
      usage: 'high'
    },
    required: {
      type: 'switch',
      label: 'Required',
      help: 'Whether the input is required',
      group: 'behavior',
      priority: 4,
      usage: 'medium'
    },
    disabled: {
      type: 'switch',
      label: 'Disabled',
      help: 'Whether the input is disabled',
      group: 'behavior',
      priority: 5,
      usage: 'low'
    },
    maxLength: {
      type: 'number',
      label: 'Max Length',
      placeholder: 'Maximum characters...',
      help: 'Maximum number of characters allowed',
      validation: [VALIDATION_RULES.positive],
      group: 'behavior',
      priority: 6,
      usage: 'low'
    }
  },
  container: {
    padding: {
      type: 'input',
      label: 'Padding',
      placeholder: 'e.g., 16px, 1rem, 10px 20px',
      help: 'Internal spacing of the container',
      group: 'layout',
      priority: 1,
      usage: 'high'
    },
    margin: {
      type: 'input',
      label: 'Margin',
      placeholder: 'e.g., 0px, 1rem, 10px 20px',
      help: 'External spacing around the container',
      group: 'layout',
      priority: 2,
      usage: 'medium'
    },
    backgroundColor: {
      type: 'color',
      label: 'Background Color',
      help: 'Background color of the container',
      group: 'appearance',
      priority: 3,
      usage: 'high'
    },
    borderRadius: {
      type: 'number',
      label: 'Border Radius (px)',
      placeholder: 'Border radius in pixels...',
      help: 'Roundness of the container corners',
      validation: [VALIDATION_RULES.nonNegative],
      group: 'appearance',
      priority: 4,
      usage: 'medium'
    },
    border: {
      type: 'input',
      label: 'Border',
      placeholder: 'e.g., 1px solid #ccc',
      help: 'Border style of the container',
      group: 'appearance',
      priority: 5,
      usage: 'medium'
    },
    width: {
      type: 'input',
      label: 'Width',
      placeholder: 'e.g., 100%, 300px, auto',
      help: 'Width of the container',
      group: 'layout',
      priority: 6,
      usage: 'medium'
    },
    height: {
      type: 'input',
      label: 'Height',
      placeholder: 'e.g., auto, 200px, 100vh',
      help: 'Height of the container',
      group: 'layout',
      priority: 7,
      usage: 'medium'
    },
    display: {
      type: 'select',
      label: 'Display',
      options: [{
        value: 'block',
        label: 'Block'
      }, {
        value: 'inline',
        label: 'Inline'
      }, {
        value: 'inline-block',
        label: 'Inline Block'
      }, {
        value: 'flex',
        label: 'Flex'
      }, {
        value: 'grid',
        label: 'Grid'
      }, {
        value: 'none',
        label: 'Hidden'
      }],
      defaultValue: 'block',
      help: 'Display type of the container',
      group: 'layout',
      priority: 8,
      usage: 'low'
    }
  },
  image: {
    src: {
      type: 'input',
      label: 'Image URL',
      placeholder: 'Enter image URL...',
      help: 'URL of the image to display',
      validation: [VALIDATION_RULES.required, VALIDATION_RULES.url],
      group: 'content',
      priority: 1,
      usage: 'high'
    },
    alt: {
      type: 'input',
      label: 'Alt Text',
      placeholder: 'Describe the image...',
      help: 'Alternative text for screen readers',
      validation: [VALIDATION_RULES.required],
      group: 'content',
      priority: 2,
      usage: 'high'
    },
    width: {
      type: 'input',
      label: 'Width',
      placeholder: 'e.g., 100%, 300px, auto',
      help: 'Width of the image',
      group: 'layout',
      priority: 3,
      usage: 'medium'
    },
    height: {
      type: 'input',
      label: 'Height',
      placeholder: 'e.g., auto, 200px',
      help: 'Height of the image',
      group: 'layout',
      priority: 4,
      usage: 'medium'
    },
    objectFit: {
      type: 'select',
      label: 'Object Fit',
      options: [{
        value: 'fill',
        label: 'Fill'
      }, {
        value: 'contain',
        label: 'Contain'
      }, {
        value: 'cover',
        label: 'Cover'
      }, {
        value: 'none',
        label: 'None'
      }, {
        value: 'scale-down',
        label: 'Scale Down'
      }],
      defaultValue: 'cover',
      help: 'How the image should be resized',
      group: 'appearance',
      priority: 5,
      usage: 'low'
    }
  }
};

// Enhanced property groups configuration with priority and usage frequency
var PROPERTY_GROUPS = {
  essential: {
    title: 'Essential',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .StarOutlined */ .L0Y, null),
    description: 'Most commonly used properties',
    priority: 1,
    defaultExpanded: true,
    color: '#1890ff'
  },
  content: {
    title: 'Content',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .FileTextOutlined */ .y9H, null),
    description: 'Text, images, and other content properties',
    priority: 2,
    defaultExpanded: true,
    color: '#52c41a'
  },
  appearance: {
    title: 'Appearance',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .BgColorsOutlined */ .Ebl, null),
    description: 'Visual styling and colors',
    priority: 3,
    defaultExpanded: false,
    color: '#722ed1'
  },
  layout: {
    title: 'Layout',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .ExpandOutlined */ .V9b, null),
    description: 'Size, spacing, and positioning',
    priority: 4,
    defaultExpanded: false,
    color: '#fa8c16'
  },
  typography: {
    title: 'Typography',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .FontSizeOutlined */ .ld1, null),
    description: 'Font styles and text formatting',
    priority: 5,
    defaultExpanded: false,
    color: '#eb2f96'
  },
  behavior: {
    title: 'Behavior',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .SettingOutlined */ .JO7, null),
    description: 'Interactions and functionality',
    priority: 6,
    defaultExpanded: false,
    color: '#13c2c2'
  },
  advanced: {
    title: 'Advanced',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .SettingOutlined */ .JO7, null),
    description: 'Custom CSS and advanced options',
    priority: 7,
    defaultExpanded: false,
    color: '#8c8c8c'
  }
};
function UXEnhancedPropertyEditor(_ref) {
  var component = _ref.component,
    onUpdateComponent = _ref.onUpdateComponent,
    onPreviewChange = _ref.onPreviewChange,
    _ref$dataSources = _ref.dataSources,
    dataSources = _ref$dataSources === void 0 ? [] : _ref$dataSources,
    _ref$showPreview = _ref.showPreview,
    showPreview = _ref$showPreview === void 0 ? true : _ref$showPreview,
    _ref$enableRealTimePr = _ref.enableRealTimePreview,
    enableRealTimePreview = _ref$enableRealTimePr === void 0 ? true : _ref$enableRealTimePr;
  var _Form$useForm = antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.useForm(),
    _Form$useForm2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_Form$useForm, 1),
    form = _Form$useForm2[0];
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)('properties'),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState, 2),
    activeTab = _useState2[0],
    setActiveTab = _useState2[1];

  // Initialize expanded groups based on default configuration
  var defaultExpandedGroups = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(function () {
    return Object.entries(PROPERTY_GROUPS).filter(function (_ref2) {
      var _ref3 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_ref2, 2),
        _ = _ref3[0],
        config = _ref3[1];
      return config.defaultExpanded;
    }).map(function (_ref4) {
      var _ref5 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_ref4, 2),
        key = _ref5[0],
        _ = _ref5[1];
      return key;
    });
  }, []);
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(defaultExpandedGroups),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState3, 2),
    expandedGroups = _useState4[0],
    setExpandedGroups = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({}),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState5, 2),
    validationErrors = _useState6[0],
    setValidationErrors = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState7, 2),
    hasUnsavedChanges = _useState8[0],
    setHasUnsavedChanges = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(enableRealTimePreview),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState9, 2),
    previewEnabled = _useState0[0],
    setPreviewEnabled = _useState0[1];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(''),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState1, 2),
    searchTerm = _useState10[0],
    setSearchTerm = _useState10[1];
  var _useState11 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),
    _useState12 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState11, 2),
    showOnlyPopular = _useState12[0],
    setShowOnlyPopular = _useState12[1];

  // Get property schema for current component type
  var propertySchema = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(function () {
    return PROPERTY_SCHEMAS[component === null || component === void 0 ? void 0 : component.type] || {};
  }, [component === null || component === void 0 ? void 0 : component.type]);

  // Enhanced property grouping with search and filtering
  var groupedProperties = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(function () {
    var groups = {};
    Object.entries(propertySchema).forEach(function (_ref6) {
      var _ref7 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_ref6, 2),
        key = _ref7[0],
        schema = _ref7[1];
      // Filter by search term
      if (searchTerm) {
        var _schema$label, _schema$help;
        var searchLower = searchTerm.toLowerCase();
        var matchesSearch = ((_schema$label = schema.label) === null || _schema$label === void 0 ? void 0 : _schema$label.toLowerCase().includes(searchLower)) || ((_schema$help = schema.help) === null || _schema$help === void 0 ? void 0 : _schema$help.toLowerCase().includes(searchLower)) || key.toLowerCase().includes(searchLower);
        if (!matchesSearch) return;
      }

      // Filter by popularity if enabled
      if (showOnlyPopular && schema.usage !== 'high') {
        return;
      }
      var groupKey = schema.group || 'advanced';
      if (!groups[groupKey]) {
        groups[groupKey] = [];
      }
      groups[groupKey].push(_objectSpread({
        key: key
      }, schema));
    });

    // Sort properties within each group by priority
    Object.keys(groups).forEach(function (groupKey) {
      groups[groupKey].sort(function (a, b) {
        return (a.priority || 999) - (b.priority || 999);
      });
    });

    // Sort groups by priority
    var sortedGroups = {};
    Object.entries(PROPERTY_GROUPS).sort(function (_ref8, _ref9) {
      var _ref0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_ref8, 2),
        a = _ref0[1];
      var _ref1 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_ref9, 2),
        b = _ref1[1];
      return (a.priority || 999) - (b.priority || 999);
    }).forEach(function (_ref10) {
      var _ref11 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_ref10, 1),
        groupKey = _ref11[0];
      if (groups[groupKey]) {
        sortedGroups[groupKey] = groups[groupKey];
      }
    });
    return sortedGroups;
  }, [propertySchema, searchTerm, showOnlyPopular]);

  // Validate a single property
  var validateProperty = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (key, value, schema) {
    if (!schema.validation) return null;
    var _iterator = _createForOfIteratorHelper(schema.validation),
      _step;
    try {
      for (_iterator.s(); !(_step = _iterator.n()).done;) {
        var rule = _step.value;
        if (!rule(value)) {
          return "Invalid ".concat(schema.label.toLowerCase());
        }
      }
    } catch (err) {
      _iterator.e(err);
    } finally {
      _iterator.f();
    }
    return null;
  }, []);

  // Validate all properties
  var validateAllProperties = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function () {
    var errors = {};
    var values = form.getFieldsValue();
    Object.entries(propertySchema).forEach(function (_ref12) {
      var _ref13 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_ref12, 2),
        key = _ref13[0],
        schema = _ref13[1];
      var error = validateProperty(key, values[key], schema);
      if (error) {
        errors[key] = error;
      }
    });
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  }, [form, propertySchema, validateProperty]);

  // Handle form value changes
  var handleValuesChange = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (changedValues, allValues) {
    setHasUnsavedChanges(true);

    // Validate changed values
    var errors = _objectSpread({}, validationErrors);
    Object.entries(changedValues).forEach(function (_ref14) {
      var _ref15 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_ref14, 2),
        key = _ref15[0],
        value = _ref15[1];
      var schema = propertySchema[key];
      if (schema) {
        var error = validateProperty(key, value, schema);
        if (error) {
          errors[key] = error;
        } else {
          delete errors[key];
        }
      }
    });
    setValidationErrors(errors);

    // Real-time preview update
    if (previewEnabled && onUpdateComponent) {
      onUpdateComponent(component.id, changedValues);
    }

    // Notify preview change
    if (onPreviewChange) {
      onPreviewChange(allValues);
    }
  }, [component === null || component === void 0 ? void 0 : component.id, onUpdateComponent, onPreviewChange, previewEnabled, validationErrors, propertySchema, validateProperty]);

  // Save changes
  var handleSave = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function () {
    if (validateAllProperties()) {
      var values = form.getFieldsValue();
      onUpdateComponent(component.id, values);
      setHasUnsavedChanges(false);
      antd__WEBPACK_IMPORTED_MODULE_5__/* .message */ .iU.success('Properties saved successfully');
    } else {
      antd__WEBPACK_IMPORTED_MODULE_5__/* .message */ .iU.error('Please fix validation errors before saving');
    }
  }, [component === null || component === void 0 ? void 0 : component.id, form, onUpdateComponent, validateAllProperties]);

  // Reset form
  var handleReset = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function () {
    if (!component) {
      antd__WEBPACK_IMPORTED_MODULE_5__/* .message */ .iU.warning('No component selected to reset');
      return;
    }
    try {
      // Get the original component properties
      var originalProps = component.props || {};

      // Reset form to original values
      form.setFieldsValue(originalProps);

      // Clear validation errors
      setValidationErrors({});

      // Mark as no unsaved changes
      setHasUnsavedChanges(false);

      // Notify parent component of reset if needed
      if (onUpdateComponent && previewEnabled) {
        onUpdateComponent(component.id, originalProps);
      }
      antd__WEBPACK_IMPORTED_MODULE_5__/* .message */ .iU.success('Properties reset to original values');
    } catch (error) {
      console.error('Error resetting properties:', error);
      antd__WEBPACK_IMPORTED_MODULE_5__/* .message */ .iU.error('Failed to reset properties');
    }
  }, [component, form, onUpdateComponent, previewEnabled]);

  // Set initial form values
  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {
    if (component !== null && component !== void 0 && component.props) {
      form.setFieldsValue(component.props);
      setHasUnsavedChanges(false);
      setValidationErrors({});
    }
  }, [component, form]);

  // Render property field based on type
  var renderPropertyField = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (property) {
    var key = property.key,
      type = property.type,
      label = property.label,
      placeholder = property.placeholder,
      help = property.help,
      options = property.options,
      defaultValue = property.defaultValue,
      validation = property.validation,
      usage = property.usage,
      priority = property.priority;
    var hasError = validationErrors[key];
    var isRequired = validation === null || validation === void 0 ? void 0 : validation.some(function (rule) {
      return rule === VALIDATION_RULES.required;
    });
    var fieldProps = {
      name: key,
      label: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
        style: {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, null, usage === 'high' && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .StarOutlined */ .L0Y, {
        style: {
          color: '#faad14',
          fontSize: '10px'
        }
      }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("span", {
        style: {
          fontWeight: usage === 'high' ? _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.typography.fontWeight.semibold : _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.typography.fontWeight.medium
        }
      }, label), isRequired && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
        type: "danger"
      }, "*"), help && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Tooltip */ .m_, {
        title: help,
        placement: "topLeft"
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .QuestionCircleOutlined */ .faO, {
        style: {
          color: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.text.tertiary,
          cursor: 'help'
        }
      }))), priority <= 3 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Tooltip */ .m_, {
        title: "Essential property"
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
        style: {
          width: 6,
          height: 6,
          borderRadius: '50%',
          backgroundColor: '#1890ff',
          opacity: 0.6
        }
      }))),
      validateStatus: hasError ? 'error' : '',
      help: hasError ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(ValidationMessage, {
        className: "error"
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .ExclamationCircleOutlined */ .G2i, null), " ", hasError) : help ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
        className: "property-help"
      }, help) : null
    };
    switch (type) {
      case 'input':
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, fieldProps, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Input */ .pd, {
          placeholder: placeholder,
          style: {
            borderRadius: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.borderRadius.md
          }
        }));
      case 'textarea':
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, fieldProps, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(TextArea, {
          rows: 4,
          placeholder: placeholder,
          style: {
            borderRadius: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.borderRadius.md
          }
        }));
      case 'select':
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, fieldProps, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Select */ .l6, {
          placeholder: placeholder,
          style: {
            borderRadius: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.borderRadius.md
          }
        }, options === null || options === void 0 ? void 0 : options.map(function (option) {
          return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Option, {
            key: option.value,
            value: option.value
          }, option.label);
        })));
      case 'switch':
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, (0,_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)({}, fieldProps, {
          valuePropName: "checked"
        }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Switch */ .dO, null));
      case 'number':
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, fieldProps, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .InputNumber */ .YI, {
          placeholder: placeholder,
          style: {
            width: '100%',
            borderRadius: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.borderRadius.md
          }
        }));
      case 'slider':
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, fieldProps, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Slider */ .Ap, null));
      case 'color':
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, fieldProps, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .ColorPicker */ .sk, null));
      default:
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV.Item, fieldProps, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Input */ .pd, {
          placeholder: placeholder
        }));
    }
  }, [validationErrors]);
  if (!component) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(PropertyEditorContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
      style: {
        padding: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[8],
        textAlign: 'center',
        color: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.text.secondary
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .SettingOutlined */ .JO7, {
      style: {
        fontSize: 48,
        marginBottom: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[4]
      }
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Title, {
      level: 4,
      style: {
        color: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.text.secondary
      }
    }, "No Component Selected"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Paragraph, null, "Select a component from the canvas to edit its properties")));
  }
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(PropertyEditorContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(PropertyHeader, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Title, {
    level: 5,
    style: {
      margin: 0,
      color: 'white'
    }
  }, "Properties"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    style: {
      color: 'rgba(255, 255, 255, 0.8)'
    }
  }, component.type, " component")), showPreview && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(PreviewToggle, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Tooltip */ .m_, {
    title: previewEnabled ? 'Disable real-time preview' : 'Enable real-time preview'
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Switch */ .dO, {
    checked: previewEnabled,
    onChange: setPreviewEnabled,
    checkedChildren: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .EyeOutlined */ .Om2, null),
    unCheckedChildren: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .EyeOutlined */ .Om2, null),
    size: "small"
  })))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      display: 'flex',
      gap: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2],
      alignItems: 'center',
      marginTop: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[3],
      padding: "".concat(_design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2], " 0"),
      borderTop: '1px solid rgba(255, 255, 255, 0.1)'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Input */ .pd, {
    placeholder: "Search properties...",
    value: searchTerm,
    onChange: function onChange(e) {
      return setSearchTerm(e.target.value);
    },
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .InfoCircleOutlined */ .rUN, {
      style: {
        color: 'rgba(255, 255, 255, 0.6)'
      }
    }),
    allowClear: true,
    size: "small",
    style: {
      flex: 1,
      background: 'rgba(255, 255, 255, 0.1)',
      border: '1px solid rgba(255, 255, 255, 0.2)',
      color: 'white'
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Tooltip */ .m_, {
    title: "Show only frequently used properties"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Switch */ .dO, {
    checked: showOnlyPopular,
    onChange: setShowOnlyPopular,
    size: "small",
    checkedChildren: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .StarOutlined */ .L0Y, null),
    unCheckedChildren: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .StarOutlined */ .L0Y, null)
  }))), hasUnsavedChanges && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Alert */ .Fc, {
    message: "You have unsaved changes",
    type: "warning",
    showIcon: true,
    style: {
      marginTop: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2],
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      border: '1px solid rgba(255, 255, 255, 0.2)',
      color: 'white'
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(PropertyContent, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Tabs */ .tU, {
    activeKey: activeTab,
    onChange: setActiveTab,
    style: {
      padding: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2]
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(TabPane, {
    tab: "Properties",
    key: "properties"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Form */ .lV, {
    form: form,
    layout: "vertical",
    onValuesChange: handleValuesChange,
    initialValues: component.props
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Collapse */ .SD, {
    activeKey: expandedGroups,
    onChange: setExpandedGroups,
    ghost: true
  }, Object.entries(groupedProperties).map(function (_ref16) {
    var _ref17 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_ref16, 2),
      groupKey = _ref17[0],
      properties = _ref17[1];
    var groupConfig = PROPERTY_GROUPS[groupKey];
    if (!groupConfig || properties.length === 0) return null;
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Panel, {
      header: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
        style: {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
        style: {
          width: 12,
          height: 12,
          borderRadius: '50%',
          backgroundColor: groupConfig.color,
          marginRight: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[1]
        }
      }), groupConfig.icon, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("span", {
        style: {
          fontWeight: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.typography.fontWeight.semibold
        }
      }, groupConfig.title), groupKey === 'essential' && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .StarOutlined */ .L0Y, {
        style: {
          color: '#faad14',
          fontSize: '12px'
        }
      })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Badge */ .Ex, {
        count: properties.length,
        size: "small",
        style: {
          backgroundColor: groupConfig.color
        }
      }), properties.some(function (p) {
        return p.usage === 'high';
      }) && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Tooltip */ .m_, {
        title: "Contains frequently used properties"
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .StarOutlined */ .L0Y, {
        style: {
          color: '#faad14',
          fontSize: '10px'
        }
      })))),
      key: groupKey
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
      style: {
        padding: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2]
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
      type: "secondary",
      style: {
        fontSize: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.typography.fontSize.xs
      }
    }, groupConfig.description), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Divider */ .cG, {
      style: {
        margin: "".concat(_design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2], " 0")
      }
    }), properties.map(function (property) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(PropertyField, {
        key: property.key
      }, renderPropertyField(property));
    })));
  })))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(TabPane, {
    tab: "Style",
    key: "style"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      padding: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2]
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Alert */ .Fc, {
    message: "Style Editor",
    description: "Advanced styling options will be available in the next update.",
    type: "info",
    showIcon: true
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(TabPane, {
    tab: "Advanced",
    key: "advanced"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
    style: {
      padding: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2]
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Alert */ .Fc, {
    message: "Advanced Options",
    description: "Custom CSS and JavaScript options will be available in the next update.",
    type: "info",
    showIcon: true
  }))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(QuickActions, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .UndoOutlined */ .Xrf, null),
    onClick: handleReset,
    disabled: !hasUnsavedChanges,
    size: "small"
  }, "Reset"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .ReloadOutlined */ .KF4, null),
    onClick: function onClick() {
      return form.resetFields();
    },
    size: "small"
  }, "Refresh")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    style: {
      fontSize: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.typography.fontSize.xs,
      color: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.text.secondary
    }
  }, Object.keys(validationErrors).length > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("span", {
    style: {
      color: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.error.main
    }
  }, Object.keys(validationErrors).length, " error(s)"), Object.keys(validationErrors).length === 0 && hasUnsavedChanges && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("span", {
    style: {
      color: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.success.main
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .CheckCircleOutlined */ .hWy, null), " Ready to save")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    type: "primary",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .SaveOutlined */ .ylI, null),
    onClick: handleSave,
    disabled: Object.keys(validationErrors).length > 0 || !hasUnsavedChanges,
    size: "small"
  }, "Save"))));
}

/***/ }),

/***/ 60064:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ IntegratedAppBuilder)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(57528);
/* harmony import */ var _babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(53986);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(33966);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(71468);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(71606);






var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5;
var _excluded = ["children"],
  _excluded2 = ["children"];
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }

/**
 * Integrated App Builder
 * 
 * Main App Builder component that integrates all UI/UX improvements
 * with existing features including WebSocket collaboration, template system,
 * code export, tutorial assistant, and AI design suggestions.
 */






// Enhanced UI/UX Components - with fallbacks
var ResponsiveAppLayout, AccessibleComponent, EnhancedComponentPaletteFixed, UXEnhancedPropertyEditor, UXEnhancedPreviewArea, EnhancedKeyboardShortcuts, DragDropProvider;

// New Feature Components
var TestingTools, DataManagementTools, PerformanceTools, EnhancedCodeExporter, IntegratedTutorialAssistant;

// Initialize all components to prevent undefined errors
ResponsiveAppLayout = ResponsiveAppLayout || function () {
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, "Layout Loading...");
};
AccessibleComponent = AccessibleComponent || function (_ref) {
  var children = _ref.children;
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, children);
};
EnhancedComponentPaletteFixed = EnhancedComponentPaletteFixed || function () {
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, "Component Palette Loading...");
};
UXEnhancedPropertyEditor = UXEnhancedPropertyEditor || function () {
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, "Property Editor Loading...");
};
UXEnhancedPreviewArea = UXEnhancedPreviewArea || function () {
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, "Preview Area Loading...");
};
EnhancedKeyboardShortcuts = EnhancedKeyboardShortcuts || function () {
  return null;
};
DragDropProvider = DragDropProvider || function (_ref2) {
  var children = _ref2.children;
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, children);
};
TestingTools = TestingTools || function () {
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, "Testing Tools Loading...");
};
DataManagementTools = DataManagementTools || function () {
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, "Data Management Loading...");
};
PerformanceTools = PerformanceTools || function () {
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, "Performance Tools Loading...");
};
EnhancedCodeExporter = EnhancedCodeExporter || function () {
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, "Code Exporter Loading...");
};
IntegratedTutorialAssistant = IntegratedTutorialAssistant || function () {
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, "Tutorial Assistant Loading...");
};
try {
  ResponsiveAppLayout = (__webpack_require__(32361)/* ["default"] */ .A);
} catch (error) {
  console.warn('ResponsiveAppLayout not available, using fallback');
  ResponsiveAppLayout = function ResponsiveAppLayout(_ref3) {
    var children = _ref3.children,
      headerContent = _ref3.headerContent,
      leftPanel = _ref3.leftPanel,
      rightPanel = _ref3.rightPanel;
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Layout */ .PE, {
      style: {
        height: '100vh'
      }
    }, headerContent && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Layout */ .PE.Header, {
      style: {
        background: '#fff',
        padding: '0 24px'
      }
    }, headerContent), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Layout */ .PE, null, leftPanel && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Layout */ .PE.Sider, {
      width: 250,
      style: {
        background: '#fff'
      }
    }, leftPanel), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Layout */ .PE.Content, {
      style: {
        padding: '24px'
      }
    }, children), rightPanel && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Layout */ .PE.Sider, {
      width: 250,
      style: {
        background: '#fff'
      }
    }, rightPanel)));
  };
}
try {
  AccessibleComponent = (__webpack_require__(12576)/* ["default"] */ .A);
} catch (error) {
  console.warn('AccessibleComponent not available, using fallback');
  AccessibleComponent = function AccessibleComponent(_ref4) {
    var children = _ref4.children,
      props = (0,_babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A)(_ref4, _excluded);
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", props, children);
  };
}
try {
  EnhancedComponentPaletteFixed = (__webpack_require__(90985)/* ["default"] */ .A);
} catch (error) {
  console.warn('EnhancedComponentPaletteFixed not available, using fallback');
  EnhancedComponentPaletteFixed = function EnhancedComponentPaletteFixed(_ref5) {
    var onAddComponent = _ref5.onAddComponent;
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
      style: {
        padding: '16px'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("h3", null, "Components"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
      onClick: function onClick() {
        return onAddComponent('button');
      },
      block: true,
      style: {
        marginBottom: '8px'
      }
    }, "Add Button"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
      onClick: function onClick() {
        return onAddComponent('text');
      },
      block: true,
      style: {
        marginBottom: '8px'
      }
    }, "Add Text"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
      onClick: function onClick() {
        return onAddComponent('input');
      },
      block: true
    }, "Add Input"));
  };
}
try {
  UXEnhancedPropertyEditor = (__webpack_require__(37812)/* ["default"] */ .A);
} catch (error) {
  console.warn('UXEnhancedPropertyEditor not available, using fallback');
  UXEnhancedPropertyEditor = function UXEnhancedPropertyEditor(_ref6) {
    var component = _ref6.component;
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
      style: {
        padding: '16px'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("h3", null, "Properties"), component ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("p", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("strong", null, "Type:"), " ", component.type), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("p", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("strong", null, "ID:"), " ", component.id)) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("p", null, "Select a component to edit properties"));
  };
}
try {
  UXEnhancedPreviewArea = (__webpack_require__(79647)/* ["default"] */ .A);
} catch (error) {
  console.warn('UXEnhancedPreviewArea not available, using fallback');
  UXEnhancedPreviewArea = function UXEnhancedPreviewArea(_ref7) {
    var components = _ref7.components,
      onSelectComponent = _ref7.onSelectComponent,
      onDrop = _ref7.onDrop;
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
      style: {
        minHeight: '400px',
        border: '2px dashed #ccc',
        borderRadius: '8px',
        padding: '20px',
        textAlign: 'center',
        background: '#fafafa'
      }
    }, components.length === 0 ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("h3", null, "Canvas Area"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("p", null, "Drag components here to start building")) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("h3", null, "Preview"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("p", null, components.length, " component(s) added")));
  };
}
try {
  EnhancedKeyboardShortcuts = (__webpack_require__(12237)/* ["default"] */ .A);
} catch (error) {
  console.warn('EnhancedKeyboardShortcuts not available, using fallback');
  EnhancedKeyboardShortcuts = function EnhancedKeyboardShortcuts() {
    return null;
  };
}
try {
  var dragDropModule = __webpack_require__(51311);
  DragDropProvider = dragDropModule.DragDropProvider;
} catch (error) {
  console.warn('DragDropProvider not available, using fallback');
  DragDropProvider = function DragDropProvider(_ref8) {
    var children = _ref8.children;
    return children;
  };
}

// Progressive Loading - with fallback
var useProgressiveLoading, ProgressiveWrapper;
try {
  var progressiveModule = __webpack_require__(92939);
  useProgressiveLoading = progressiveModule.useProgressiveLoading;
  ProgressiveWrapper = progressiveModule.ProgressiveWrapper;
} catch (error) {
  console.warn('Progressive loading not available, using fallback');
  useProgressiveLoading = function useProgressiveLoading() {
    return {
      isLoaded: true,
      loadComponent: function loadComponent() {}
    };
  };
  ProgressiveWrapper = function ProgressiveWrapper(_ref9) {
    var children = _ref9.children;
    return children;
  };
}

// Lazy-loaded Feature Components - with fallbacks
var TutorialAssistant, AIDesignSuggestions, TemplateManager, CodeExporter, CollaborationIndicator;

// Define fallback component first
var FallbackComponent = function FallbackComponent(_ref0) {
  var children = _ref0.children,
    props = (0,_babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_5__/* ["default"] */ .A)(_ref0, _excluded2);
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    style: {
      padding: '10px',
      border: '1px dashed #ccc',
      borderRadius: '4px'
    }
  }, "Feature not available");
};

// Initialize with fallbacks first
TutorialAssistant = FallbackComponent;
AIDesignSuggestions = FallbackComponent;
TemplateManager = FallbackComponent;
CodeExporter = FallbackComponent;
CollaborationIndicator = FallbackComponent;
try {
  var lazyComponents = __webpack_require__(38787);
  TutorialAssistant = lazyComponents.TutorialAssistant || FallbackComponent;
  AIDesignSuggestions = lazyComponents.AIDesignSuggestions || FallbackComponent;
  TemplateManager = lazyComponents.TemplateManager || FallbackComponent;
  CodeExporter = lazyComponents.CodeExporter || FallbackComponent;
  CollaborationIndicator = lazyComponents.CollaborationIndicator || FallbackComponent;
} catch (error) {
  console.warn('Lazy components not available, using fallbacks');
  // Fallbacks already set above
}

// Import new components - with fallbacks
var AppBuilderExamples, IntegratedTutorialSystem;

// Initialize with fallbacks first
AppBuilderExamples = function AppBuilderExamples() {
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, "Examples not available");
};
IntegratedTutorialSystem = function IntegratedTutorialSystem() {
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, "Tutorial system not available");
};
try {
  AppBuilderExamples = (__webpack_require__(32150)/* ["default"] */ .A) || AppBuilderExamples;
} catch (error) {
  console.warn('AppBuilderExamples not available');
  // Fallback already set above
}
try {
  IntegratedTutorialSystem = (__webpack_require__(39446)/* ["default"] */ .A) || IntegratedTutorialSystem;
} catch (error) {
  console.warn('IntegratedTutorialSystem not available');
  // Fallback already set above
}

// Design System - with fallbacks
var theme, visualHierarchy;
try {
  var designSystem = __webpack_require__(79146);
  theme = designSystem.theme;
  visualHierarchy = designSystem.visualHierarchy;
} catch (error) {
  console.warn('Design system not available, using fallback theme');
  theme = {
    colors: {
      primary: '#1890ff',
      secondary: '#666'
    },
    spacing: {
      sm: '8px',
      md: '16px',
      lg: '24px'
    },
    borderRadius: {
      sm: '4px',
      md: '8px'
    }
  };
  visualHierarchy = {
    headings: {
      h1: {
        fontSize: '24px'
      },
      h2: {
        fontSize: '20px'
      }
    }
  };
}

// Load new feature components with fallbacks
try {
  TestingTools = (__webpack_require__(54605)["default"]);
} catch (error) {
  console.warn('TestingTools not available, using fallback');
  TestingTools = function TestingTools() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, "Testing Tools not available");
  };
}
try {
  DataManagementTools = (__webpack_require__(27954)/* ["default"] */ .A);
} catch (error) {
  console.warn('DataManagementTools not available, using fallback');
  DataManagementTools = function DataManagementTools() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, "Data Management Tools not available");
  };
}
try {
  PerformanceTools = (__webpack_require__(40672)["default"]);
} catch (error) {
  console.warn('PerformanceTools not available, using fallback');
  PerformanceTools = function PerformanceTools() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, "Performance Tools not available");
  };
}
try {
  EnhancedCodeExporter = (__webpack_require__(71142)["default"]);
} catch (error) {
  console.warn('EnhancedCodeExporter not available, using fallback');
  EnhancedCodeExporter = function EnhancedCodeExporter() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, "Enhanced Code Exporter not available");
  };
}
try {
  IntegratedTutorialAssistant = (__webpack_require__(9771)/* ["default"] */ .A);
} catch (error) {
  console.warn('IntegratedTutorialAssistant not available, using fallback');
  IntegratedTutorialAssistant = function IntegratedTutorialAssistant() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, "Tutorial Assistant not available");
  };
}

// Load WebSocket Manager component
var WebSocketManagerComponent;
try {
  WebSocketManagerComponent = (__webpack_require__(18270)/* ["default"] */ .A);
} catch (error) {
  console.warn('WebSocketManager not available, using fallback');
  WebSocketManagerComponent = function WebSocketManagerComponent() {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, "WebSocket Manager not available");
  };
}

// Hooks and Services - with fallbacks
var useWebSocket, useAppBuilder, useTutorial, useAIDesignSuggestions, useTemplates, useCodeExport, useCollaboration;
try {
  useWebSocket = (__webpack_require__(97787)/* ["default"] */ .A);
} catch (error) {
  console.warn('useWebSocket not available, using fallback');
  useWebSocket = function useWebSocket() {
    return {
      isConnected: false,
      collaborators: [],
      sendUpdate: function sendUpdate() {},
      sendCursor: function sendCursor() {}
    };
  };
}
try {
  useAppBuilder = (__webpack_require__(20364)/* ["default"] */ .A);
} catch (error) {
  console.warn('useAppBuilder not available, using fallback');
  useAppBuilder = function useAppBuilder() {
    return {
      components: [],
      addComponent: function addComponent() {},
      updateComponent: function updateComponent() {},
      deleteComponent: function deleteComponent() {},
      moveComponent: function moveComponent() {},
      duplicateComponent: function duplicateComponent() {},
      undoAction: function undoAction() {},
      redoAction: function redoAction() {},
      canUndo: false,
      canRedo: false,
      saveProject: function saveProject() {},
      loadProject: function loadProject() {},
      isModified: false
    };
  };
}
try {
  useTutorial = (__webpack_require__(52648)/* ["default"] */ .A);
} catch (error) {
  console.warn('useTutorial not available, using fallback');
  useTutorial = function useTutorial() {
    return {
      isActive: false,
      currentStep: 0,
      totalSteps: 0,
      nextStep: function nextStep() {},
      previousStep: function previousStep() {},
      skipTutorial: function skipTutorial() {},
      startTutorial: function startTutorial() {}
    };
  };
}
try {
  useAIDesignSuggestions = (__webpack_require__(87169)/* ["default"] */ .A);
} catch (error) {
  console.warn('useAIDesignSuggestions not available, using fallback');
  useAIDesignSuggestions = function useAIDesignSuggestions() {
    return {
      suggestions: [],
      loading: false,
      generateSuggestions: function generateSuggestions() {},
      applySuggestion: function applySuggestion() {},
      dismissSuggestion: function dismissSuggestion() {}
    };
  };
}
try {
  useTemplates = (__webpack_require__(2643)/* ["default"] */ .A);
} catch (error) {
  console.warn('useTemplates not available, using fallback');
  useTemplates = function useTemplates() {
    return {
      templates: [],
      loading: false,
      saveAsTemplate: function saveAsTemplate() {},
      loadTemplate: function loadTemplate() {},
      deleteTemplate: function deleteTemplate() {}
    };
  };
}
try {
  useCodeExport = (__webpack_require__(66337)/* ["default"] */ .A);
} catch (error) {
  console.warn('useCodeExport not available, using fallback');
  useCodeExport = function useCodeExport() {
    return {
      exportFormats: ['React', 'Vue', 'Angular'],
      loading: false,
      exportCode: function exportCode() {},
      downloadCode: function downloadCode() {}
    };
  };
}
try {
  useCollaboration = (__webpack_require__(7805)/* ["default"] */ .A);
} catch (error) {
  console.warn('useCollaboration not available, using fallback');
  useCollaboration = function useCollaboration() {
    return {
      activeUsers: [],
      comments: [],
      addComment: function addComment() {},
      resolveComment: function resolveComment() {}
    };
  };
}
var IntegratedContainer = styled_components__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  height: 100vh;\n  background: ", ";\n  position: relative;\n  overflow: hidden;\n\n  /* Ensure proper stacking context */\n  z-index: 0;\n"])), function (props) {
  var _props$theme;
  return ((_props$theme = props.theme) === null || _props$theme === void 0 || (_props$theme = _props$theme.colors) === null || _props$theme === void 0 || (_props$theme = _props$theme.background) === null || _props$theme === void 0 ? void 0 : _props$theme["default"]) || '#f5f5f5';
});
var HeaderContent = styled_components__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Ay.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  gap: ", ";\n  flex: 1;\n\n  .app-title {\n    font-size: ", ";\n    font-weight: ", ";\n    margin: 0;\n    color: ", ";\n  }\n\n  .project-name {\n    font-size: ", ";\n    color: ", ";\n    margin-left: ", ";\n  }\n"])), function (props) {
  var _props$theme2;
  return ((_props$theme2 = props.theme) === null || _props$theme2 === void 0 || (_props$theme2 = _props$theme2.spacing) === null || _props$theme2 === void 0 ? void 0 : _props$theme2[4]) || '16px';
}, function (props) {
  var _props$theme3;
  return ((_props$theme3 = props.theme) === null || _props$theme3 === void 0 || (_props$theme3 = _props$theme3.visualHierarchy) === null || _props$theme3 === void 0 || (_props$theme3 = _props$theme3.heading) === null || _props$theme3 === void 0 || (_props$theme3 = _props$theme3.h4) === null || _props$theme3 === void 0 ? void 0 : _props$theme3.fontSize) || '18px';
}, function (props) {
  var _props$theme4;
  return ((_props$theme4 = props.theme) === null || _props$theme4 === void 0 || (_props$theme4 = _props$theme4.visualHierarchy) === null || _props$theme4 === void 0 || (_props$theme4 = _props$theme4.heading) === null || _props$theme4 === void 0 || (_props$theme4 = _props$theme4.h4) === null || _props$theme4 === void 0 ? void 0 : _props$theme4.fontWeight) || '600';
}, function (props) {
  var _props$theme5;
  return ((_props$theme5 = props.theme) === null || _props$theme5 === void 0 || (_props$theme5 = _props$theme5.colors) === null || _props$theme5 === void 0 || (_props$theme5 = _props$theme5.text) === null || _props$theme5 === void 0 ? void 0 : _props$theme5.primary) || '#333';
}, function (props) {
  var _props$theme6;
  return ((_props$theme6 = props.theme) === null || _props$theme6 === void 0 || (_props$theme6 = _props$theme6.visualHierarchy) === null || _props$theme6 === void 0 || (_props$theme6 = _props$theme6.body) === null || _props$theme6 === void 0 || (_props$theme6 = _props$theme6.small) === null || _props$theme6 === void 0 ? void 0 : _props$theme6.fontSize) || '12px';
}, function (props) {
  var _props$theme7;
  return ((_props$theme7 = props.theme) === null || _props$theme7 === void 0 || (_props$theme7 = _props$theme7.colors) === null || _props$theme7 === void 0 || (_props$theme7 = _props$theme7.text) === null || _props$theme7 === void 0 ? void 0 : _props$theme7.secondary) || '#666';
}, function (props) {
  var _props$theme8;
  return ((_props$theme8 = props.theme) === null || _props$theme8 === void 0 || (_props$theme8 = _props$theme8.spacing) === null || _props$theme8 === void 0 ? void 0 : _props$theme8[2]) || '8px';
});
var FeatureToggles = styled_components__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  gap: ", ";\n\n  @media (max-width: 768px) {\n    gap: ", ";\n  }\n"])), function (props) {
  var _props$theme9;
  return ((_props$theme9 = props.theme) === null || _props$theme9 === void 0 || (_props$theme9 = _props$theme9.spacing) === null || _props$theme9 === void 0 ? void 0 : _props$theme9[2]) || '8px';
}, function (props) {
  var _props$theme0;
  return ((_props$theme0 = props.theme) === null || _props$theme0 === void 0 || (_props$theme0 = _props$theme0.spacing) === null || _props$theme0 === void 0 ? void 0 : _props$theme0[1]) || '4px';
});
var LoadingOverlay = styled_components__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Ay.div(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(255, 255, 255, 0.8);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  z-index: ", ";\n\n  .loading-text {\n    margin-top: ", ";\n    font-size: ", ";\n    color: ", ";\n  }\n"])), function (props) {
  var _props$theme1;
  return ((_props$theme1 = props.theme) === null || _props$theme1 === void 0 || (_props$theme1 = _props$theme1.zIndex) === null || _props$theme1 === void 0 ? void 0 : _props$theme1.modal) || 1000;
}, function (props) {
  var _props$theme10;
  return ((_props$theme10 = props.theme) === null || _props$theme10 === void 0 || (_props$theme10 = _props$theme10.spacing) === null || _props$theme10 === void 0 ? void 0 : _props$theme10[4]) || '16px';
}, function (props) {
  var _props$theme11;
  return ((_props$theme11 = props.theme) === null || _props$theme11 === void 0 || (_props$theme11 = _props$theme11.visualHierarchy) === null || _props$theme11 === void 0 || (_props$theme11 = _props$theme11.body) === null || _props$theme11 === void 0 || (_props$theme11 = _props$theme11.medium) === null || _props$theme11 === void 0 ? void 0 : _props$theme11.fontSize) || '14px';
}, function (props) {
  var _props$theme12;
  return ((_props$theme12 = props.theme) === null || _props$theme12 === void 0 || (_props$theme12 = _props$theme12.colors) === null || _props$theme12 === void 0 || (_props$theme12 = _props$theme12.text) === null || _props$theme12 === void 0 ? void 0 : _props$theme12.secondary) || '#666';
});
var ErrorBoundary = styled_components__WEBPACK_IMPORTED_MODULE_10__/* ["default"] */ .Ay.div(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  padding: ", ";\n  text-align: center;\n\n  .error-title {\n    font-size: ", ";\n    font-weight: ", ";\n    color: ", ";\n    margin-bottom: ", ";\n  }\n\n  .error-message {\n    font-size: ", ";\n    color: ", ";\n    margin-bottom: ", ";\n  }\n"])), function (props) {
  var _props$theme13;
  return ((_props$theme13 = props.theme) === null || _props$theme13 === void 0 || (_props$theme13 = _props$theme13.spacing) === null || _props$theme13 === void 0 ? void 0 : _props$theme13[8]) || '32px';
}, function (props) {
  var _props$theme14;
  return ((_props$theme14 = props.theme) === null || _props$theme14 === void 0 || (_props$theme14 = _props$theme14.visualHierarchy) === null || _props$theme14 === void 0 || (_props$theme14 = _props$theme14.heading) === null || _props$theme14 === void 0 || (_props$theme14 = _props$theme14.h3) === null || _props$theme14 === void 0 ? void 0 : _props$theme14.fontSize) || '20px';
}, function (props) {
  var _props$theme15;
  return ((_props$theme15 = props.theme) === null || _props$theme15 === void 0 || (_props$theme15 = _props$theme15.visualHierarchy) === null || _props$theme15 === void 0 || (_props$theme15 = _props$theme15.heading) === null || _props$theme15 === void 0 || (_props$theme15 = _props$theme15.h3) === null || _props$theme15 === void 0 ? void 0 : _props$theme15.fontWeight) || '600';
}, function (props) {
  var _props$theme16;
  return ((_props$theme16 = props.theme) === null || _props$theme16 === void 0 || (_props$theme16 = _props$theme16.colors) === null || _props$theme16 === void 0 || (_props$theme16 = _props$theme16.error) === null || _props$theme16 === void 0 ? void 0 : _props$theme16.main) || '#ff4d4f';
}, function (props) {
  var _props$theme17;
  return ((_props$theme17 = props.theme) === null || _props$theme17 === void 0 || (_props$theme17 = _props$theme17.spacing) === null || _props$theme17 === void 0 ? void 0 : _props$theme17[4]) || '16px';
}, function (props) {
  var _props$theme18;
  return ((_props$theme18 = props.theme) === null || _props$theme18 === void 0 || (_props$theme18 = _props$theme18.visualHierarchy) === null || _props$theme18 === void 0 || (_props$theme18 = _props$theme18.body) === null || _props$theme18 === void 0 || (_props$theme18 = _props$theme18.medium) === null || _props$theme18 === void 0 ? void 0 : _props$theme18.fontSize) || '14px';
}, function (props) {
  var _props$theme19;
  return ((_props$theme19 = props.theme) === null || _props$theme19 === void 0 || (_props$theme19 = _props$theme19.colors) === null || _props$theme19 === void 0 || (_props$theme19 = _props$theme19.text) === null || _props$theme19 === void 0 ? void 0 : _props$theme19.secondary) || '#666';
}, function (props) {
  var _props$theme20;
  return ((_props$theme20 = props.theme) === null || _props$theme20 === void 0 || (_props$theme20 = _props$theme20.spacing) === null || _props$theme20 === void 0 ? void 0 : _props$theme20[6]) || '24px';
});
function IntegratedAppBuilder(_ref1) {
  var projectId = _ref1.projectId,
    _ref1$initialComponen = _ref1.initialComponents,
    initialComponents = _ref1$initialComponen === void 0 ? [] : _ref1$initialComponen,
    _ref1$enableFeatures = _ref1.enableFeatures,
    enableFeatures = _ref1$enableFeatures === void 0 ? {
      websocket: true,
      tutorial: true,
      aiSuggestions: true,
      templates: true,
      codeExport: true,
      collaboration: true,
      testing: true,
      dataManagement: true,
      performanceMonitoring: true,
      enhancedExport: true,
      tutorialAssistant: true
    } : _ref1$enableFeatures,
    onSave = _ref1.onSave,
    onLoad = _ref1.onLoad,
    _onError = _ref1.onError;
  // Redux state
  var dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_9__/* .useDispatch */ .wA)();
  var user = (0,react_redux__WEBPACK_IMPORTED_MODULE_9__/* .useSelector */ .d4)(function (state) {
    var _state$auth;
    return (_state$auth = state.auth) === null || _state$auth === void 0 ? void 0 : _state$auth.user;
  });
  var project = (0,react_redux__WEBPACK_IMPORTED_MODULE_9__/* .useSelector */ .d4)(function (state) {
    var _state$projects;
    return (_state$projects = state.projects) === null || _state$projects === void 0 ? void 0 : _state$projects.current;
  });

  // Local state
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(true),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(null),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState3, 2),
    error = _useState4[0],
    setError = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(null),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState5, 2),
    selectedComponent = _useState6[0],
    setSelectedComponent = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState7, 2),
    previewMode = _useState8[0],
    setPreviewMode = _useState8[1];

  // Clipboard for copy/paste operations
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(null),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState9, 2),
    clipboard = _useState0[0],
    setClipboard = _useState0[1];

  // New feature states
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(null),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState1, 2),
    activeFeaturePanel = _useState10[0],
    setActiveFeaturePanel = _useState10[1];
  var _useState11 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)({}),
    _useState12 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState11, 2),
    testResults = _useState12[0],
    setTestResults = _useState12[1];
  var _useState13 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)({
      coreWebVitals: {},
      bundleSize: {
        total: 0
      },
      renderMetrics: {},
      memoryUsage: {
        used: 0,
        total: 100
      }
    }),
    _useState14 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState13, 2),
    performanceData = _useState14[0],
    setPerformanceData = _useState14[1];
  var _useState15 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)([]),
    _useState16 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState15, 2),
    dataBindings = _useState16[0],
    setDataBindings = _useState16[1];
  var _useState17 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)({
      framework: 'react',
      typescript: true,
      includeTests: false,
      includeStyles: true,
      codeQuality: {
        prettier: true,
        eslint: true,
        components: true
      }
    }),
    _useState18 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState17, 2),
    exportSettings = _useState18[0],
    setExportSettings = _useState18[1];
  var _useState19 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)({
      currentTutorial: null,
      completedTutorials: new Set(),
      isActive: false
    }),
    _useState20 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState19, 2),
    tutorialProgress = _useState20[0],
    setTutorialProgress = _useState20[1];

  // Feature states
  var _useState21 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false),
    _useState22 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState21, 2),
    showTutorial = _useState22[0],
    setShowTutorial = _useState22[1];
  var _useState23 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false),
    _useState24 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState23, 2),
    showAISuggestions = _useState24[0],
    setShowAISuggestions = _useState24[1];
  var _useState25 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false),
    _useState26 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState25, 2),
    showTemplates = _useState26[0],
    setShowTemplates = _useState26[1];
  var _useState27 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false),
    _useState28 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState27, 2),
    showCodeExport = _useState28[0],
    setShowCodeExport = _useState28[1];
  var _useState29 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false),
    _useState30 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState29, 2),
    showExamples = _useState30[0],
    setShowExamples = _useState30[1];
  var _useState31 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false),
    _useState32 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState31, 2),
    showTestingTools = _useState32[0],
    setShowTestingTools = _useState32[1];
  var _useState33 = (0,react__WEBPACK_IMPORTED_MODULE_7__.useState)(false),
    _useState34 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState33, 2),
    showPerformanceMonitor = _useState34[0],
    setShowPerformanceMonitor = _useState34[1];

  // Progressive loading for feature components
  var progressiveLoading = useProgressiveLoading({
    strategy: 'priority',
    features: Object.keys(enableFeatures).filter(function (key) {
      return enableFeatures[key];
    }),
    autoStart: true,
    delay: 1000 // Start loading after initial render
  });

  // App Builder hook with enhanced features
  var _useAppBuilder = useAppBuilder({
      projectId: projectId,
      initialComponents: initialComponents,
      autoSave: true,
      onSave: onSave,
      onLoad: onLoad,
      onError: function onError(err) {
        setError(err);
        if (_onError) _onError(err);
      }
    }),
    components = _useAppBuilder.components,
    addComponent = _useAppBuilder.addComponent,
    updateComponent = _useAppBuilder.updateComponent,
    deleteComponent = _useAppBuilder.deleteComponent,
    moveComponent = _useAppBuilder.moveComponent,
    duplicateComponent = _useAppBuilder.duplicateComponent,
    undoAction = _useAppBuilder.undoAction,
    redoAction = _useAppBuilder.redoAction,
    canUndo = _useAppBuilder.canUndo,
    canRedo = _useAppBuilder.canRedo,
    saveProject = _useAppBuilder.saveProject,
    loadProject = _useAppBuilder.loadProject,
    isModified = _useAppBuilder.isModified;

  // WebSocket collaboration
  var _useWebSocket = useWebSocket({
      enabled: enableFeatures.websocket,
      projectId: projectId,
      userId: user === null || user === void 0 ? void 0 : user.id,
      onComponentUpdate: updateComponent,
      onComponentAdd: addComponent,
      onComponentDelete: deleteComponent,
      onComponentMove: moveComponent
    }),
    websocketConnected = _useWebSocket.isConnected,
    collaborators = _useWebSocket.collaborators,
    sendUpdate = _useWebSocket.sendUpdate,
    sendCursor = _useWebSocket.sendCursor;

  // Tutorial system
  var _useTutorial = useTutorial({
      enabled: enableFeatures.tutorial,
      autoStart: !(user !== null && user !== void 0 && user.hasCompletedTutorial)
    }),
    tutorialActive = _useTutorial.isActive,
    currentStep = _useTutorial.currentStep,
    totalSteps = _useTutorial.totalSteps,
    nextStep = _useTutorial.nextStep,
    previousStep = _useTutorial.previousStep,
    skipTutorial = _useTutorial.skipTutorial,
    startTutorial = _useTutorial.startTutorial;

  // AI Design Suggestions
  var _useAIDesignSuggestio = useAIDesignSuggestions({
      enabled: enableFeatures.aiSuggestions,
      components: components,
      selectedComponent: selectedComponent,
      autoRefresh: true
    }),
    suggestions = _useAIDesignSuggestio.suggestions,
    aiLoading = _useAIDesignSuggestio.loading,
    generateSuggestions = _useAIDesignSuggestio.generateSuggestions,
    applySuggestion = _useAIDesignSuggestio.applySuggestion,
    dismissSuggestion = _useAIDesignSuggestio.dismissSuggestion;

  // Template system
  var _useTemplates = useTemplates({
      enabled: enableFeatures.templates,
      projectId: projectId
    }),
    templates = _useTemplates.templates,
    templatesLoading = _useTemplates.loading,
    saveAsTemplate = _useTemplates.saveAsTemplate,
    loadTemplate = _useTemplates.loadTemplate,
    deleteTemplate = _useTemplates.deleteTemplate;

  // Code export
  var _useCodeExport = useCodeExport({
      enabled: enableFeatures.codeExport,
      components: components,
      projectSettings: project === null || project === void 0 ? void 0 : project.settings
    }),
    exportCode = _useCodeExport.exportCode,
    exportFormats = _useCodeExport.exportFormats,
    exportLoading = _useCodeExport.loading,
    downloadCode = _useCodeExport.downloadCode;

  // Collaboration features
  var _useCollaboration = useCollaboration({
      enabled: enableFeatures.collaboration,
      projectId: projectId,
      websocketConnected: websocketConnected
    }),
    activeUsers = _useCollaboration.activeUsers,
    comments = _useCollaboration.comments,
    addComment = _useCollaboration.addComment,
    resolveComment = _useCollaboration.resolveComment,
    shareProject = _useCollaboration.shareProject;

  // Initialize app
  (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(function () {
    var initializeApp = /*#__PURE__*/function () {
      var _ref10 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_6___default().mark(function _callee() {
        var _t;
        return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_6___default().wrap(function (_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.prev = 0;
              setLoading(true);
              if (!projectId) {
                _context.next = 1;
                break;
              }
              _context.next = 1;
              return loadProject(projectId);
            case 1:
              if (!enableFeatures.aiSuggestions) {
                _context.next = 2;
                break;
              }
              _context.next = 2;
              return generateSuggestions();
            case 2:
              setLoading(false);
              _context.next = 4;
              break;
            case 3:
              _context.prev = 3;
              _t = _context["catch"](0);
              setError(_t);
              setLoading(false);
            case 4:
            case "end":
              return _context.stop();
          }
        }, _callee, null, [[0, 3]]);
      }));
      return function initializeApp() {
        return _ref10.apply(this, arguments);
      };
    }();
    initializeApp();
  }, [projectId, enableFeatures.aiSuggestions]); // Removed function dependencies to prevent infinite re-renders

  // Handle incoming WebSocket events for new features
  (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(function () {
    if (!websocketConnected) return;
    var handleWebSocketMessage = function handleWebSocketMessage(event) {
      try {
        var data = JSON.parse(event.data);
        switch (data.type) {
          case 'test_results':
            if (data.userId !== (user === null || user === void 0 ? void 0 : user.id)) {
              antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.info("".concat(data.userId, " completed ").concat(data.testType, " tests: ").concat(data.results.passed, "/").concat(data.results.total, " passed"));
            }
            break;
          case 'optimization_applied':
            if (data.userId !== (user === null || user === void 0 ? void 0 : user.id)) {
              antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.info("".concat(data.userId, " applied optimization: ").concat(data.suggestion));
            }
            break;
          case 'data_binding_created':
            if (data.userId !== (user === null || user === void 0 ? void 0 : user.id)) {
              setDataBindings(function (prev) {
                return [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(prev), [data.binding]);
              });
              antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.info("".concat(data.userId, " created a data binding"));
            }
            break;
          case 'data_binding_updated':
            if (data.userId !== (user === null || user === void 0 ? void 0 : user.id)) {
              setDataBindings(function (prev) {
                return prev.map(function (b) {
                  return b.id === data.binding.id ? data.binding : b;
                });
              });
              antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.info("".concat(data.userId, " updated a data binding"));
            }
            break;
          case 'data_binding_deleted':
            if (data.userId !== (user === null || user === void 0 ? void 0 : user.id)) {
              setDataBindings(function (prev) {
                return prev.filter(function (b) {
                  return b.id !== data.bindingId;
                });
              });
              antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.info("".concat(data.userId, " deleted a data binding"));
            }
            break;
          case 'code_exported':
            if (data.userId !== (user === null || user === void 0 ? void 0 : user.id)) {
              antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.info("".concat(data.userId, " exported code for ").concat(data.framework));
            }
            break;
          case 'tutorial_completed':
            if (data.userId !== (user === null || user === void 0 ? void 0 : user.id)) {
              antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.info("".concat(data.userId, " completed a tutorial"));
            }
            break;
          default:
            // Handle other WebSocket events
            break;
        }
      } catch (error) {
        console.warn('Failed to parse WebSocket message:', error);
      }
    };

    // Note: WebSocket message handling is integrated through the useWebSocket hook
    console.log('WebSocket event handling for new features is ready');
    return function () {
      // Cleanup if needed
    };
  }, [websocketConnected, user === null || user === void 0 ? void 0 : user.id]);

  // Persist feature panel state and settings
  (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(function () {
    var savedSettings = localStorage.getItem("app-builder-settings-".concat(projectId));
    if (savedSettings) {
      try {
        var settings = JSON.parse(savedSettings);
        if (settings.exportSettings) {
          setExportSettings(function (prev) {
            return _objectSpread(_objectSpread({}, prev), settings.exportSettings);
          });
        }
        if (settings.dataBindings) {
          setDataBindings(settings.dataBindings);
        }
        if (settings.tutorialProgress) {
          setTutorialProgress(function (prev) {
            return _objectSpread(_objectSpread({}, prev), settings.tutorialProgress);
          });
        }
      } catch (error) {
        console.warn('Failed to load saved settings:', error);
      }
    }
  }, [projectId]);

  // Save settings when they change
  (0,react__WEBPACK_IMPORTED_MODULE_7__.useEffect)(function () {
    var settings = {
      exportSettings: exportSettings,
      dataBindings: dataBindings,
      tutorialProgress: tutorialProgress,
      lastUpdated: Date.now()
    };
    localStorage.setItem("app-builder-settings-".concat(projectId), JSON.stringify(settings));
  }, [projectId, exportSettings, dataBindings, tutorialProgress]);

  // Handle component selection with collaboration
  var handleSelectComponent = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (component) {
    setSelectedComponent(component);

    // Send cursor position for collaboration
    if (websocketConnected && component) {
      sendCursor({
        componentId: component.id,
        action: 'select',
        timestamp: Date.now()
      });
    }
  }, [websocketConnected, sendCursor]);

  // Handle component updates with real-time sync
  var handleUpdateComponent = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (componentId, updates) {
    updateComponent(componentId, updates);

    // Send update to collaborators
    if (websocketConnected) {
      sendUpdate({
        type: 'component_update',
        componentId: componentId,
        updates: updates,
        userId: user === null || user === void 0 ? void 0 : user.id,
        timestamp: Date.now()
      });
    }
  }, [updateComponent, websocketConnected, sendUpdate, user === null || user === void 0 ? void 0 : user.id]);

  // Handle component addition with AI suggestions
  var handleAddComponent = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(/*#__PURE__*/function () {
    var _ref11 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_6___default().mark(function _callee2(componentType, position) {
      var newComponent;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_6___default().wrap(function (_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            _context2.next = 1;
            return addComponent(componentType, position);
          case 1:
            newComponent = _context2.sent;
            // Generate AI suggestions for the new component
            if (enableFeatures.aiSuggestions && newComponent) {
              setTimeout(function () {
                return generateSuggestions();
              }, 500);
            }

            // Send update to collaborators
            if (websocketConnected) {
              sendUpdate({
                type: 'component_add',
                component: newComponent,
                userId: user === null || user === void 0 ? void 0 : user.id,
                timestamp: Date.now()
              });
            }
            return _context2.abrupt("return", newComponent);
          case 2:
          case "end":
            return _context2.stop();
        }
      }, _callee2);
    }));
    return function (_x, _x2) {
      return _ref11.apply(this, arguments);
    };
  }(), [addComponent, enableFeatures.aiSuggestions, generateSuggestions, websocketConnected, sendUpdate, user === null || user === void 0 ? void 0 : user.id]);

  // Handle component deletion with confirmation
  var handleDeleteComponent = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (componentId) {
    var component = components.find(function (c) {
      return c.id === componentId;
    });
    if (component) {
      deleteComponent(componentId);

      // Clear selection if deleted component was selected
      if ((selectedComponent === null || selectedComponent === void 0 ? void 0 : selectedComponent.id) === componentId) {
        setSelectedComponent(null);
      }

      // Send update to collaborators
      if (websocketConnected) {
        sendUpdate({
          type: 'component_delete',
          componentId: componentId,
          userId: user === null || user === void 0 ? void 0 : user.id,
          timestamp: Date.now()
        });
      }
      antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success("Deleted ".concat(component.type, " component"));
    }
  }, [components, deleteComponent, selectedComponent, websocketConnected, sendUpdate, user === null || user === void 0 ? void 0 : user.id]);

  // Enhanced keyboard shortcut handlers
  var handleKeyboardAction = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (action, shortcutKey) {
    switch (action) {
      case 'save':
        if (isModified) {
          saveProject();
          antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success('Project saved successfully');
        }
        break;
      case 'copy':
        if (selectedComponent) {
          setClipboard(_objectSpread(_objectSpread({}, selectedComponent), {}, {
            id: undefined
          }));
          antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success('Component copied to clipboard');
        }
        break;
      case 'paste':
        if (clipboard) {
          var _clipboard$position, _clipboard$position2;
          var newComponent = _objectSpread(_objectSpread({}, clipboard), {}, {
            id: "component_".concat(Date.now()),
            position: {
              x: (((_clipboard$position = clipboard.position) === null || _clipboard$position === void 0 ? void 0 : _clipboard$position.x) || 0) + 20,
              y: (((_clipboard$position2 = clipboard.position) === null || _clipboard$position2 === void 0 ? void 0 : _clipboard$position2.y) || 0) + 20
            }
          });
          handleAddComponent(newComponent.type, newComponent.position);
          antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success('Component pasted from clipboard');
        }
        break;
      case 'cut':
        if (selectedComponent) {
          setClipboard(_objectSpread(_objectSpread({}, selectedComponent), {}, {
            id: undefined
          }));
          handleDeleteComponent(selectedComponent.id);
          antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success('Component cut to clipboard');
        }
        break;
      case 'undo':
        if (canUndo) {
          undoAction();
          antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success('Action undone');
        }
        break;
      case 'redo':
        if (canRedo) {
          redoAction();
          antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success('Action redone');
        }
        break;
      case 'delete':
        if (selectedComponent) {
          handleDeleteComponent(selectedComponent.id);
        }
        break;
      case 'preview':
        setPreviewMode(function (prev) {
          return !prev;
        });
        antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success("Preview mode ".concat(!previewMode ? 'enabled' : 'disabled'));
        break;
      case 'new':
        handleAddComponent('text', {
          x: 100,
          y: 100
        });
        break;
      case 'fullscreen':
        if (document.fullscreenElement) {
          document.exitFullscreen();
        } else {
          document.documentElement.requestFullscreen();
        }
        break;

      // New feature shortcuts
      case 'toggle_testing':
        if (enableFeatures.testing) {
          handleFeaturePanelToggle('testing');
          antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.info('Testing panel toggled');
        }
        break;
      case 'toggle_performance':
        if (enableFeatures.performanceMonitoring) {
          handleFeaturePanelToggle('performance');
          antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.info('Performance panel toggled');
        }
        break;
      case 'toggle_data':
        if (enableFeatures.dataManagement) {
          handleFeaturePanelToggle('data');
          antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.info('Data management panel toggled');
        }
        break;
      case 'toggle_export':
        if (enableFeatures.enhancedExport) {
          handleFeaturePanelToggle('export');
          antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.info('Export panel toggled');
        }
        break;
      case 'close_panels':
        if (activeFeaturePanel) {
          setActiveFeaturePanel(null);
          antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.info('All panels closed');
        }
        break;
      default:
        console.log("Unhandled keyboard action: ".concat(action));
    }
  }, [selectedComponent, clipboard, isModified, canUndo, canRedo, previewMode, saveProject, undoAction, redoAction, handleAddComponent, handleDeleteComponent, enableFeatures, handleFeaturePanelToggle, activeFeaturePanel, setActiveFeaturePanel]);

  // New Feature Handlers

  // Testing Tools Handlers
  var handleTestComplete = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (testType, results) {
    setTestResults(function (prev) {
      return _objectSpread(_objectSpread({}, prev), {}, (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)({}, testType, results));
    });
    var passedTests = results.filter(function (r) {
      return r.status === 'passed';
    }).length;
    var totalTests = results.length;
    antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success("".concat(testType, " tests completed: ").concat(passedTests, "/").concat(totalTests, " passed"));

    // Send test results via WebSocket for collaboration
    if (websocketConnected && sendUpdate) {
      sendUpdate({
        type: 'test_results',
        testType: testType,
        results: {
          passed: passedTests,
          total: totalTests
        },
        userId: user === null || user === void 0 ? void 0 : user.id,
        timestamp: Date.now()
      });
    }
  }, [websocketConnected, sendUpdate, user === null || user === void 0 ? void 0 : user.id]);
  var handleTestStart = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (testType) {
    antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.info("Starting ".concat(testType, " tests..."));
    setTestResults(function (prev) {
      return _objectSpread(_objectSpread({}, prev), {}, (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)({}, testType, []));
    });
  }, []);

  // Performance Monitoring Handlers
  var handleOptimizationApply = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (suggestion) {
    antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success("Applied optimization: ".concat(suggestion.title));
    setPerformanceData(function (prev) {
      return _objectSpread(_objectSpread({}, prev), {}, {
        optimizations: [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(prev.optimizations || []), [_objectSpread(_objectSpread({}, suggestion), {}, {
          appliedAt: Date.now()
        })])
      });
    });
    if (websocketConnected && sendUpdate) {
      sendUpdate({
        type: 'optimization_applied',
        suggestion: suggestion.title,
        userId: user === null || user === void 0 ? void 0 : user.id,
        timestamp: Date.now()
      });
    }
  }, [websocketConnected, sendUpdate, user === null || user === void 0 ? void 0 : user.id]);

  // Data Management Handlers
  var handleDataChange = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (data) {
    antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success('Data updated successfully');
    if (selectedComponent && data.componentId === selectedComponent.id) {
      handleUpdateComponent(selectedComponent.id, {
        data: data
      });
    }
  }, [selectedComponent, handleUpdateComponent]);
  var handleBindingCreate = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (binding) {
    setDataBindings(function (prev) {
      return [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(prev), [binding]);
    });
    antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success('Data binding created');
    if (websocketConnected && sendUpdate) {
      sendUpdate({
        type: 'data_binding_created',
        binding: binding,
        userId: user === null || user === void 0 ? void 0 : user.id,
        timestamp: Date.now()
      });
    }
  }, [websocketConnected, sendUpdate, user === null || user === void 0 ? void 0 : user.id]);
  var handleBindingUpdate = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (binding) {
    setDataBindings(function (prev) {
      return prev.map(function (b) {
        return b.id === binding.id ? binding : b;
      });
    });
    antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success('Data binding updated');
    if (websocketConnected && sendUpdate) {
      sendUpdate({
        type: 'data_binding_updated',
        binding: binding,
        userId: user === null || user === void 0 ? void 0 : user.id,
        timestamp: Date.now()
      });
    }
  }, [websocketConnected, sendUpdate, user === null || user === void 0 ? void 0 : user.id]);
  var handleBindingDelete = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (bindingId) {
    setDataBindings(function (prev) {
      return prev.filter(function (b) {
        return b.id !== bindingId;
      });
    });
    antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success('Data binding deleted');
    if (websocketConnected && sendUpdate) {
      sendUpdate({
        type: 'data_binding_deleted',
        bindingId: bindingId,
        userId: user === null || user === void 0 ? void 0 : user.id,
        timestamp: Date.now()
      });
    }
  }, [websocketConnected, sendUpdate, user === null || user === void 0 ? void 0 : user.id]);

  // Enhanced Export Handlers
  var handleEnhancedExport = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (exportData) {
    antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success("Code exported for ".concat(exportData.framework));
    setExportSettings(function (prev) {
      return _objectSpread(_objectSpread({}, prev), {}, {
        lastExport: {
          framework: exportData.framework,
          timestamp: Date.now(),
          settings: exportData.settings
        }
      });
    });
    if (websocketConnected && sendUpdate) {
      sendUpdate({
        type: 'code_exported',
        framework: exportData.framework,
        userId: user === null || user === void 0 ? void 0 : user.id,
        timestamp: Date.now()
      });
    }
  }, [websocketConnected, sendUpdate, user === null || user === void 0 ? void 0 : user.id]);
  var handleCodePreview = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (code) {
    antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.info('Code preview generated');
  }, []);

  // Tutorial Assistant Handlers
  var handleTutorialComplete = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (tutorial) {
    setTutorialProgress(function (prev) {
      return _objectSpread(_objectSpread({}, prev), {}, {
        completedTutorials: new Set([].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(prev.completedTutorials), [tutorial.id])),
        currentTutorial: null,
        isActive: false
      });
    });
    antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success("Tutorial \"".concat(tutorial.title, "\" completed! \uD83C\uDF89"));
    if (websocketConnected && sendUpdate) {
      sendUpdate({
        type: 'tutorial_completed',
        tutorialId: tutorial.id,
        userId: user === null || user === void 0 ? void 0 : user.id,
        timestamp: Date.now()
      });
    }
  }, [websocketConnected, sendUpdate, user === null || user === void 0 ? void 0 : user.id]);
  var handleTutorialSkip = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (tutorial) {
    setTutorialProgress(function (prev) {
      return _objectSpread(_objectSpread({}, prev), {}, {
        currentTutorial: null,
        isActive: false
      });
    });
    antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.info("Tutorial \"".concat(tutorial.title, "\" skipped."));
  }, []);

  // Feature Panel Management
  var handleFeaturePanelToggle = (0,react__WEBPACK_IMPORTED_MODULE_7__.useCallback)(function (panelType) {
    setActiveFeaturePanel(function (prev) {
      return prev === panelType ? null : panelType;
    });
  }, []);

  // Memoized header content
  var headerContent = (0,react__WEBPACK_IMPORTED_MODULE_7__.useMemo)(function () {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(HeaderContent, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("h1", {
      className: "app-title"
    }, "App Builder"), (project === null || project === void 0 ? void 0 : project.name) && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("span", {
      className: "project-name"
    }, project.name), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
      style: {
        display: 'flex',
        alignItems: 'center',
        gap: '8px',
        marginTop: '4px',
        fontSize: '12px'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
      style: {
        display: 'flex',
        alignItems: 'center',
        gap: '4px',
        padding: '2px 8px',
        borderRadius: '12px',
        background: websocketConnected ? '#f6ffed' : '#fff2f0',
        border: "1px solid ".concat(websocketConnected ? '#b7eb8f' : '#ffccc7'),
        color: websocketConnected ? '#52c41a' : '#ff4d4f'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
      style: {
        width: '6px',
        height: '6px',
        borderRadius: '50%',
        background: websocketConnected ? '#52c41a' : '#ff4d4f'
      }
    }), websocketConnected ? 'Connected' : 'Offline'), isModified && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
      style: {
        display: 'flex',
        alignItems: 'center',
        gap: '4px',
        padding: '2px 8px',
        borderRadius: '12px',
        background: '#fff7e6',
        border: '1px solid #ffd591',
        color: '#fa8c16'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
      style: {
        width: '6px',
        height: '6px',
        borderRadius: '50%',
        background: '#fa8c16'
      }
    }), "Unsaved Changes"), activeFeaturePanel && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
      style: {
        display: 'flex',
        alignItems: 'center',
        gap: '4px',
        padding: '2px 8px',
        borderRadius: '12px',
        background: '#e6f7ff',
        border: '1px solid #91d5ff',
        color: '#1890ff'
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
      style: {
        width: '6px',
        height: '6px',
        borderRadius: '50%',
        background: '#1890ff'
      }
    }), activeFeaturePanel.charAt(0).toUpperCase() + activeFeaturePanel.slice(1), " Panel"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
      style: {
        display: 'flex',
        alignItems: 'center',
        gap: '4px',
        padding: '2px 8px',
        borderRadius: '12px',
        background: '#f0f5ff',
        border: '1px solid #adc6ff',
        color: '#1890ff'
      }
    }, components.length, " Component", components.length !== 1 ? 's' : ''))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(FeatureToggles, null, enableFeatures.collaboration && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(ProgressiveWrapper, {
      componentName: "CollaborationIndicator",
      strategy: "viewport",
      fallback: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
        style: {
          width: 32,
          height: 32
        }
      })
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(CollaborationIndicator, {
      connected: websocketConnected,
      collaborators: collaborators,
      activeUsers: activeUsers
    })), enableFeatures.aiSuggestions && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(ProgressiveWrapper, {
      componentName: "AIDesignSuggestions",
      strategy: "interaction",
      fallback: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
        style: {
          width: 120,
          height: 32
        }
      })
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(AIDesignSuggestions, {
      suggestions: suggestions,
      loading: aiLoading,
      onApply: applySuggestion,
      onDismiss: dismissSuggestion,
      compact: true
    })), enableFeatures.templates && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
      "data-tutorial": "theme-manager"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(ProgressiveWrapper, {
      componentName: "TemplateManager",
      strategy: "interaction",
      fallback: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
        style: {
          width: 100,
          height: 32
        }
      })
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(TemplateManager, {
      templates: templates,
      loading: templatesLoading,
      onSave: saveAsTemplate,
      onLoad: loadTemplate,
      onDelete: deleteTemplate,
      compact: true
    }))), enableFeatures.codeExport && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(ProgressiveWrapper, {
      componentName: "CodeExporter",
      strategy: "interaction",
      fallback: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
        style: {
          width: 100,
          height: 32
        }
      })
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(CodeExporter, {
      formats: exportFormats,
      loading: exportLoading,
      onExport: exportCode,
      onDownload: downloadCode,
      compact: true
    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("button", {
      "data-tutorial": "preview-mode",
      onClick: function onClick() {
        return setPreviewMode(function (prev) {
          return !prev;
        });
      },
      style: {
        background: previewMode ? '#52c41a' : '#f0f0f0',
        color: previewMode ? 'white' : '#333',
        border: 'none',
        borderRadius: '6px',
        padding: '8px 16px',
        cursor: 'pointer',
        fontSize: '14px',
        fontWeight: '500',
        display: 'flex',
        alignItems: 'center',
        gap: '6px',
        transition: 'all 0.2s ease',
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
      },
      title: previewMode ? 'Exit preview mode' : 'Enter preview mode'
    }, "\uD83D\uDC41\uFE0F ", previewMode ? 'Exit Preview' : 'Preview'), enableFeatures.tutorial && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("button", {
      onClick: function onClick() {
        return startTutorial();
      },
      style: {
        background: tutorialActive ? 'linear-gradient(135deg, #52c41a 0%, #389e0d 100%)' : 'linear-gradient(135deg, #1890ff 0%, #096dd9 100%)',
        color: 'white',
        border: 'none',
        borderRadius: '6px',
        padding: '8px 16px',
        cursor: 'pointer',
        fontSize: '14px',
        fontWeight: '500',
        display: 'flex',
        alignItems: 'center',
        gap: '6px',
        transition: 'all 0.2s ease',
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
      },
      onMouseOver: function onMouseOver(e) {
        e.target.style.transform = 'translateY(-1px)';
        e.target.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.15)';
      },
      onMouseOut: function onMouseOut(e) {
        e.target.style.transform = 'translateY(0)';
        e.target.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
      },
      title: tutorialActive ? 'Tutorial is active' : 'Start interactive tutorial'
    }, "\uD83C\uDF93 ", tutorialActive ? 'Tutorial Active' : 'Start Tutorial'), enableFeatures.testing && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("button", {
      "data-tutorial": "testing-tools",
      onClick: function onClick() {
        return handleFeaturePanelToggle('testing');
      },
      style: {
        background: activeFeaturePanel === 'testing' ? 'linear-gradient(135deg, #52c41a 0%, #389e0d 100%)' : 'linear-gradient(135deg, #fa8c16 0%, #d46b08 100%)',
        color: 'white',
        border: 'none',
        borderRadius: '6px',
        padding: '8px 16px',
        cursor: 'pointer',
        fontSize: '14px',
        fontWeight: '500',
        display: 'flex',
        alignItems: 'center',
        gap: '6px',
        transition: 'all 0.2s ease',
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
      },
      onMouseOver: function onMouseOver(e) {
        e.target.style.transform = 'translateY(-1px)';
        e.target.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.15)';
      },
      onMouseOut: function onMouseOut(e) {
        e.target.style.transform = 'translateY(0)';
        e.target.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
      },
      title: "Open testing and debugging tools"
    }, "\uD83E\uDDEA ", activeFeaturePanel === 'testing' ? 'Close Testing' : 'Testing'), enableFeatures.performanceMonitoring && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("button", {
      "data-tutorial": "performance-monitor",
      onClick: function onClick() {
        return handleFeaturePanelToggle('performance');
      },
      style: {
        background: activeFeaturePanel === 'performance' ? 'linear-gradient(135deg, #52c41a 0%, #389e0d 100%)' : 'linear-gradient(135deg, #722ed1 0%, #531dab 100%)',
        color: 'white',
        border: 'none',
        borderRadius: '6px',
        padding: '8px 16px',
        cursor: 'pointer',
        fontSize: '14px',
        fontWeight: '500',
        display: 'flex',
        alignItems: 'center',
        gap: '6px',
        transition: 'all 0.2s ease',
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
      },
      onMouseOver: function onMouseOver(e) {
        e.target.style.transform = 'translateY(-1px)';
        e.target.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.15)';
      },
      onMouseOut: function onMouseOut(e) {
        e.target.style.transform = 'translateY(0)';
        e.target.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
      },
      title: "Open performance monitoring dashboard"
    }, "\u26A1 ", activeFeaturePanel === 'performance' ? 'Close Performance' : 'Performance'), enableFeatures.dataManagement && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("button", {
      "data-tutorial": "data-management",
      onClick: function onClick() {
        return handleFeaturePanelToggle('data');
      },
      style: {
        background: activeFeaturePanel === 'data' ? 'linear-gradient(135deg, #52c41a 0%, #389e0d 100%)' : 'linear-gradient(135deg, #13c2c2 0%, #08979c 100%)',
        color: 'white',
        border: 'none',
        borderRadius: '6px',
        padding: '8px 16px',
        cursor: 'pointer',
        fontSize: '14px',
        fontWeight: '500',
        display: 'flex',
        alignItems: 'center',
        gap: '6px',
        transition: 'all 0.2s ease',
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
      },
      onMouseOver: function onMouseOver(e) {
        e.target.style.transform = 'translateY(-1px)';
        e.target.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.15)';
      },
      onMouseOut: function onMouseOut(e) {
        e.target.style.transform = 'translateY(0)';
        e.target.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
      },
      title: "Open data management tools"
    }, "\uD83D\uDCCA ", activeFeaturePanel === 'data' ? 'Close Data' : 'Data'), enableFeatures.enhancedExport && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("button", {
      "data-tutorial": "code-export",
      onClick: function onClick() {
        return handleFeaturePanelToggle('export');
      },
      style: {
        background: activeFeaturePanel === 'export' ? 'linear-gradient(135deg, #52c41a 0%, #389e0d 100%)' : 'linear-gradient(135deg, #eb2f96 0%, #c41d7f 100%)',
        color: 'white',
        border: 'none',
        borderRadius: '6px',
        padding: '8px 16px',
        cursor: 'pointer',
        fontSize: '14px',
        fontWeight: '500',
        display: 'flex',
        alignItems: 'center',
        gap: '6px',
        transition: 'all 0.2s ease',
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
      },
      onMouseOver: function onMouseOver(e) {
        e.target.style.transform = 'translateY(-1px)';
        e.target.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.15)';
      },
      onMouseOut: function onMouseOut(e) {
        e.target.style.transform = 'translateY(0)';
        e.target.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
      },
      title: "Enhanced code export with multiple frameworks"
    }, "\uD83D\uDCBB ", activeFeaturePanel === 'export' ? 'Close Export' : 'Export'), enableFeatures.websocket && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("button", {
      "data-tutorial": "websocket-manager",
      onClick: function onClick() {
        return handleFeaturePanelToggle('websocket');
      },
      style: {
        background: activeFeaturePanel === 'websocket' ? 'linear-gradient(135deg, #52c41a 0%, #389e0d 100%)' : 'linear-gradient(135deg, #1890ff 0%, #096dd9 100%)',
        color: 'white',
        border: 'none',
        borderRadius: '6px',
        padding: '8px 16px',
        cursor: 'pointer',
        fontSize: '14px',
        fontWeight: '500',
        display: 'flex',
        alignItems: 'center',
        gap: '6px',
        transition: 'all 0.2s ease',
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
      },
      onMouseOver: function onMouseOver(e) {
        e.target.style.transform = 'translateY(-1px)';
        e.target.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.15)';
      },
      onMouseOut: function onMouseOut(e) {
        e.target.style.transform = 'translateY(0)';
        e.target.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
      },
      title: "Manage WebSocket connections and real-time collaboration"
    }, "\uD83D\uDD17 ", activeFeaturePanel === 'websocket' ? 'Close WebSocket' : 'WebSocket'), enableFeatures.tutorialAssistant && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("button", {
      "data-tutorial": "tutorial-assistant",
      onClick: function onClick() {
        return handleFeaturePanelToggle('tutorialAssistant');
      },
      style: {
        background: activeFeaturePanel === 'tutorialAssistant' ? 'linear-gradient(135deg, #52c41a 0%, #389e0d 100%)' : 'linear-gradient(135deg, #f759ab 0%, #eb2f96 100%)',
        color: 'white',
        border: 'none',
        borderRadius: '6px',
        padding: '8px 16px',
        cursor: 'pointer',
        fontSize: '14px',
        fontWeight: '500',
        display: 'flex',
        alignItems: 'center',
        gap: '6px',
        transition: 'all 0.2s ease',
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
      },
      onMouseOver: function onMouseOver(e) {
        e.target.style.transform = 'translateY(-1px)';
        e.target.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.15)';
      },
      onMouseOut: function onMouseOut(e) {
        e.target.style.transform = 'translateY(0)';
        e.target.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
      },
      title: "Interactive tutorial assistant with contextual help"
    }, "\uD83C\uDFAF ", activeFeaturePanel === 'tutorialAssistant' ? 'Close Assistant' : 'Tutorial Assistant'), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("button", {
      onClick: function onClick() {
        return setShowExamples(true);
      },
      style: {
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white',
        border: 'none',
        borderRadius: '6px',
        padding: '8px 16px',
        cursor: 'pointer',
        fontSize: '14px',
        fontWeight: '500',
        display: 'flex',
        alignItems: 'center',
        gap: '6px',
        transition: 'all 0.2s ease',
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'
      },
      onMouseOver: function onMouseOver(e) {
        e.target.style.transform = 'translateY(-1px)';
        e.target.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.15)';
      },
      onMouseOut: function onMouseOut(e) {
        e.target.style.transform = 'translateY(0)';
        e.target.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
      },
      title: "View examples and tutorials"
    }, "\uD83D\uDCDA Examples")));
  }, [project === null || project === void 0 ? void 0 : project.name, enableFeatures, websocketConnected, collaborators, activeUsers, suggestions, aiLoading, applySuggestion, dismissSuggestion, templates, templatesLoading, saveAsTemplate, loadTemplate, deleteTemplate, exportFormats, exportLoading, exportCode, downloadCode, setShowExamples, previewMode, setPreviewMode, components.length, isModified, tutorialActive, startTutorial, activeFeaturePanel, handleFeaturePanelToggle]);

  // Error boundary
  if (error) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(IntegratedContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(ErrorBoundary, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
      className: "error-title"
    }, "Something went wrong"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
      className: "error-message"
    }, error.message), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("button", {
      onClick: function onClick() {
        return window.location.reload();
      }
    }, "Reload Application")));
  }
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(IntegratedContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(DragDropProvider, {
    showOverlay: true
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(AccessibleComponent, {
    role: "application",
    ariaLabel: "App Builder application for creating user interfaces"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(ResponsiveAppLayout, {
    headerContent: headerContent,
    leftPanel: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
      "data-tutorial": "component-palette"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(EnhancedComponentPaletteFixed, {
      onAddComponent: handleAddComponent,
      selectedComponent: selectedComponent,
      showAISuggestions: enableFeatures.aiSuggestions,
      loading: loading
    })),
    rightPanel: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
      "data-tutorial": "property-editor"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(UXEnhancedPropertyEditor, {
      component: selectedComponent,
      onUpdateComponent: handleUpdateComponent,
      loading: loading
    })),
    showBreakpointIndicator: "production" === 'development',
    enablePanelResize: true,
    persistLayout: true
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    "data-tutorial": "canvas-area"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(UXEnhancedPreviewArea, {
    components: components,
    selectedComponentId: selectedComponent === null || selectedComponent === void 0 ? void 0 : selectedComponent.id,
    onSelectComponent: handleSelectComponent,
    onDeleteComponent: handleDeleteComponent,
    onUpdateComponent: handleUpdateComponent,
    onMoveComponent: moveComponent,
    previewMode: previewMode,
    websocketConnected: websocketConnected,
    loading: loading,
    onDrop: handleAddComponent
  }))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(EnhancedKeyboardShortcuts, {
    onAction: handleKeyboardAction,
    showQuickActions: true,
    enableCustomization: true,
    showFeedback: true
  }), enableFeatures.tutorial && tutorialActive && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(TutorialAssistant, {
    currentStep: currentStep,
    totalSteps: totalSteps,
    onNext: nextStep,
    onPrevious: previousStep,
    onSkip: skipTutorial,
    onComplete: skipTutorial
  }), enableFeatures.tutorial && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(IntegratedTutorialSystem, {
    onTutorialComplete: function onTutorialComplete() {
      antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success('Tutorial completed! You\'re ready to build amazing apps.');
    }
  }), enableFeatures.tutorialAssistant && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(IntegratedTutorialAssistant, {
    enableAutoStart: !(user !== null && user !== void 0 && user.hasCompletedTutorial),
    showContextualHelp: true,
    onTutorialComplete: handleTutorialComplete,
    onTutorialSkip: handleTutorialSkip,
    features: Object.keys(enableFeatures).filter(function (key) {
      return enableFeatures[key];
    })
  }), showExamples && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Modal */ .aF, {
    title: "App Builder Examples & Tutorials",
    visible: showExamples,
    onCancel: function onCancel() {
      return setShowExamples(false);
    },
    footer: null,
    width: "90%",
    style: {
      top: 20
    },
    bodyStyle: {
      padding: 0,
      height: '80vh',
      overflow: 'auto'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(AppBuilderExamples, null)), activeFeaturePanel === 'testing' && enableFeatures.testing && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Modal */ .aF, {
    title: "Testing Tools",
    open: true,
    onCancel: function onCancel() {
      return setActiveFeaturePanel(null);
    },
    footer: null,
    width: "90%",
    style: {
      top: 20
    },
    bodyStyle: {
      padding: 0,
      height: '80vh',
      overflow: 'auto'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    style: {
      padding: '16px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(TestingTools, {
    components: components,
    onTestComplete: handleTestComplete,
    onTestStart: handleTestStart,
    enabledTests: ['component', 'layout', 'accessibility', 'performance'],
    autoRun: false,
    showMetrics: true
  }))), activeFeaturePanel === 'performance' && enableFeatures.performanceMonitoring && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Modal */ .aF, {
    title: "Performance Monitor",
    open: true,
    onCancel: function onCancel() {
      return setActiveFeaturePanel(null);
    },
    footer: null,
    width: "90%",
    style: {
      top: 20
    },
    bodyStyle: {
      padding: 0,
      height: '80vh',
      overflow: 'auto'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    style: {
      padding: '16px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(PerformanceTools, {
    components: components,
    onOptimizationApply: handleOptimizationApply,
    realTimeMonitoring: true,
    showSuggestions: true
  }))), activeFeaturePanel === 'data' && enableFeatures.dataManagement && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Modal */ .aF, {
    title: "Data Management",
    open: true,
    onCancel: function onCancel() {
      return setActiveFeaturePanel(null);
    },
    footer: null,
    width: "90%",
    style: {
      top: 20
    },
    bodyStyle: {
      padding: 0,
      height: '80vh',
      overflow: 'auto'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    style: {
      padding: '16px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(DataManagementTools, {
    components: components,
    onDataChange: handleDataChange,
    onBindingCreate: handleBindingCreate,
    onBindingUpdate: handleBindingUpdate,
    onBindingDelete: handleBindingDelete,
    realTimeUpdates: true,
    showVisualization: true
  }))), activeFeaturePanel === 'export' && enableFeatures.enhancedExport && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Modal */ .aF, {
    title: "Enhanced Code Export",
    open: true,
    onCancel: function onCancel() {
      return setActiveFeaturePanel(null);
    },
    footer: null,
    width: "90%",
    style: {
      top: 20
    },
    bodyStyle: {
      padding: 0,
      height: '80vh',
      overflow: 'auto'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    style: {
      padding: '16px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(EnhancedCodeExporter, {
    components: components,
    layouts: [],
    theme: theme,
    onExport: handleEnhancedExport,
    onPreview: handleCodePreview
  }))), activeFeaturePanel === 'websocket' && enableFeatures.websocket && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Modal */ .aF, {
    title: "WebSocket Manager",
    open: true,
    onCancel: function onCancel() {
      return setActiveFeaturePanel(null);
    },
    footer: null,
    width: "90%",
    style: {
      top: 20
    },
    bodyStyle: {
      padding: 0,
      height: '80vh',
      overflow: 'auto'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    style: {
      padding: '16px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(WebSocketManagerComponent, {
    endpoint: "app_builder",
    autoConnect: false,
    showMetrics: true,
    showDebug: true
  }))), activeFeaturePanel === 'tutorialAssistant' && enableFeatures.tutorialAssistant && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Modal */ .aF, {
    title: "Tutorial Assistant",
    open: true,
    onCancel: function onCancel() {
      return setActiveFeaturePanel(null);
    },
    footer: null,
    width: "90%",
    style: {
      top: 20
    },
    bodyStyle: {
      padding: 0,
      height: '80vh',
      overflow: 'auto'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    style: {
      padding: '16px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(IntegratedTutorialAssistant, {
    enableAutoStart: false,
    showContextualHelp: true,
    onTutorialComplete: handleTutorialComplete,
    onTutorialSkip: handleTutorialSkip,
    features: Object.keys(enableFeatures).filter(function (key) {
      return enableFeatures[key];
    })
  }))), loading && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(LoadingOverlay, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Spin */ .tK, {
    size: "large"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_7__.createElement("div", {
    className: "loading-text"
  }, "Loading App Builder...")),  false && /*#__PURE__*/0);
}

/***/ }),

/***/ 79647:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (/* binding */ UXEnhancedPreviewArea)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(33966);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(36031);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(71606);
/* harmony import */ var _design_system__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(79146);
/* harmony import */ var _hooks_useRealTimePreview__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(79459);
/* harmony import */ var _hooks_usePreviewPerformance__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(48860);



var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6, _templateObject7, _templateObject8, _templateObject9, _templateObject0, _templateObject1, _templateObject10, _templateObject11, _templateObject12, _templateObject13, _templateObject14, _templateObject15, _templateObject16, _templateObject17;
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * UX Enhanced Preview Area
 * 
 * A comprehensive preview area with enhanced UI/UX features:
 * - Responsive breakpoint indicators and device frames
 * - Advanced zoom controls and canvas interactions
 * - Enhanced drag-and-drop visual feedback
 * - Real-time collaboration indicators
 * - Performance monitoring and optimization
 * - Accessibility improvements
 */








var Title = antd__WEBPACK_IMPORTED_MODULE_4__/* .Typography */ .o5.Title,
  Text = antd__WEBPACK_IMPORTED_MODULE_4__/* .Typography */ .o5.Text;
var Option = antd__WEBPACK_IMPORTED_MODULE_4__/* .Select */ .l6.Option;

// Device configurations with enhanced metadata
var DEVICE_CONFIGS = {
  mobile: {
    name: 'Mobile',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .MobileOutlined */ .jHj, null),
    width: 375,
    height: 812,
    scale: 0.8,
    frame: true,
    breakpoint: 'sm',
    description: 'iPhone 12 Pro (375×812)',
    userAgent: 'mobile'
  },
  tablet: {
    name: 'Tablet',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .TabletOutlined */ .pLH, null),
    width: 768,
    height: 1024,
    scale: 0.7,
    frame: true,
    breakpoint: 'md',
    description: 'iPad (768×1024)',
    userAgent: 'tablet'
  },
  desktop: {
    name: 'Desktop',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .DesktopOutlined */ .zlw, null),
    width: 1440,
    height: 900,
    scale: 1,
    frame: false,
    breakpoint: 'lg',
    description: 'Desktop (1440×900)',
    userAgent: 'desktop'
  },
  wide: {
    name: 'Wide Screen',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ExpandOutlined */ .V9b, null),
    width: 1920,
    height: 1080,
    scale: 0.8,
    frame: false,
    breakpoint: 'xl',
    description: 'Wide Screen (1920×1080)',
    userAgent: 'desktop'
  }
};

// Enhanced styled components
var PreviewContainer = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  position: relative;\n  height: 100%;\n  background: ", ";\n  border-radius: ", ";\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n  border: 1px solid ", ";\n  \n  /* High contrast mode support */\n  @media (prefers-contrast: high) {\n    border: 2px solid ", ";\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.background.secondary, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.borderRadius.lg, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.border.light, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.border.dark);
var PreviewToolbar = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: ", " ", ";\n  background: ", ";\n  border-bottom: 1px solid ", ";\n  box-shadow: ", ";\n  z-index: ", ";\n  flex-wrap: wrap;\n  gap: ", ";\n  min-height: 60px;\n\n  ", " {\n    padding: ", " ", ";\n    gap: ", ";\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[4], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.background.paper, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.border.light, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.shadows.sm, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.zIndex.sticky, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.mediaQueries.maxMd, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[3], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[1]);
var ToolbarSection = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  gap: ", ";\n  \n  ", " {\n    gap: ", ";\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.mediaQueries.maxMd, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[1]);
var DeviceSelector = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  gap: ", ";\n  padding: ", ";\n  background: ", ";\n  border-radius: ", ";\n  border: 1px solid ", ";\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[1], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[1], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.background.tertiary, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.borderRadius.md, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.border.light);
var DeviceButton = (0,styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n)(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  gap: ", ";\n  border: none;\n  background: ", ";\n  color: ", ";\n  box-shadow: none;\n  border-radius: ", ";\n  transition: ", ";\n  min-width: auto;\n  padding: ", " ", ";\n\n  &:hover {\n    background: ", ";\n    color: ", ";\n  }\n  \n  &:focus {\n    ", ";\n  }\n  \n  ", " {\n    padding: ", ";\n    \n    .device-label {\n      display: none;\n    }\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[1], function (props) {
  return props.active ? _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.main : 'transparent';
}, function (props) {
  return props.active ? _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.contrastText : _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.text.secondary;
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.borderRadius.sm, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.transitions["default"], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[1], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], function (props) {
  return props.active ? _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.dark : _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.interactive.hover;
}, function (props) {
  return props.active ? _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.contrastText : _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.main;
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.a11yUtils.focusRing(), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.mediaQueries.maxMd, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[1]);
var ZoomControls = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject6 || (_templateObject6 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  gap: ", ";\n  padding: ", " ", ";\n  background: ", ";\n  border-radius: ", ";\n  border: 1px solid ", ";\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[1], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.background.tertiary, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.borderRadius.md, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.border.light);
var ZoomSlider = (0,styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_4__/* .Slider */ .Ap)(_templateObject7 || (_templateObject7 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  width: 100px;\n  margin: 0 ", ";\n  \n  .ant-slider-rail {\n    background: ", ";\n  }\n  \n  .ant-slider-track {\n    background: ", ";\n  }\n  \n  .ant-slider-handle {\n    border-color: ", ";\n    \n    &:focus {\n      box-shadow: ", ";\n    }\n  }\n  \n  ", " {\n    width: 60px;\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.border.medium, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.main, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.main, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.shadows.focus, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.mediaQueries.maxMd);
var StatusIndicator = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject8 || (_templateObject8 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  gap: ", ";\n  padding: ", " ", ";\n  background: ", ";\n  color: ", ";\n  border-radius: ", ";\n  font-size: ", ";\n  font-weight: ", ";\n  border: 1px solid ", ";\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[1], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[1], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], function (props) {
  return props.connected ? _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.success.light : _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.error.light;
}, function (props) {
  return props.connected ? _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.success.dark : _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.error.dark;
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.borderRadius.sm, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.typography.fontSize.xs, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.typography.fontWeight.medium, function (props) {
  return props.connected ? _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.success.main : _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.error.main;
});
var CanvasContainer = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject9 || (_templateObject9 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  flex: 1;\n  position: relative;\n  overflow: auto;\n  background: ", ";\n  background-size: ", "px ", "px;\n  background-position: ", "px ", "px;\n  \n  /* Custom scrollbar */\n  &::-webkit-scrollbar {\n    width: 8px;\n    height: 8px;\n  }\n  \n  &::-webkit-scrollbar-track {\n    background: ", ";\n  }\n  \n  &::-webkit-scrollbar-thumb {\n    background: ", ";\n    border-radius: ", ";\n  }\n  \n  &::-webkit-scrollbar-thumb:hover {\n    background: ", ";\n  }\n"])), function (props) {
  return props.showGrid ? "radial-gradient(circle, ".concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.border.light, " 1px, transparent 1px)") : _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.background.secondary;
}, function (props) {
  return props.gridSize || 20;
}, function (props) {
  return props.gridSize || 20;
}, function (props) {
  var _props$gridOffset;
  return ((_props$gridOffset = props.gridOffset) === null || _props$gridOffset === void 0 ? void 0 : _props$gridOffset.x) || 0;
}, function (props) {
  var _props$gridOffset2;
  return ((_props$gridOffset2 = props.gridOffset) === null || _props$gridOffset2 === void 0 ? void 0 : _props$gridOffset2.y) || 0;
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.background.tertiary, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.border.medium, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.borderRadius.full, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.border.dark);
var DeviceFrame = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject0 || (_templateObject0 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  position: relative;\n  margin: ", " auto;\n  background: ", ";\n  border-radius: ", ";\n  padding: ", ";\n  box-shadow: ", ";\n  transition: ", ";\n  \n  /* Mobile device frame details */\n  ", "\n\n  /* Tablet device frame details */\n  ", "\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[4], function (props) {
  switch (props.deviceType) {
    case 'mobile':
      return _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.neutral[800];
    case 'tablet':
      return _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.neutral[700];
    default:
      return 'transparent';
  }
}, function (props) {
  switch (props.deviceType) {
    case 'mobile':
      return '30px';
    case 'tablet':
      return '20px';
    default:
      return '0';
  }
}, function (props) {
  switch (props.deviceType) {
    case 'mobile':
      return "".concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[6], " ").concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[3]);
    case 'tablet':
      return "".concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[4], " ").concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2]);
    default:
      return '0';
  }
}, function (props) {
  return props.frame ? _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.shadows.xl : 'none';
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.transitions["default"], function (props) {
  return props.deviceType === 'mobile' && "\n    &::before {\n      content: '';\n      position: absolute;\n      top: ".concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], ";\n      left: 50%;\n      transform: translateX(-50%);\n      width: 60px;\n      height: 4px;\n      background: ").concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.neutral[600], ";\n      border-radius: ").concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.borderRadius.full, ";\n    }\n\n    &::after {\n      content: '';\n      position: absolute;\n      bottom: ").concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], ";\n      left: 50%;\n      transform: translateX(-50%);\n      width: 40px;\n      height: 40px;\n      border: 2px solid ").concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.neutral[600], ";\n      border-radius: 50%;\n    }\n  ");
}, function (props) {
  return props.deviceType === 'tablet' && "\n    &::before {\n      content: '';\n      position: absolute;\n      bottom: ".concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[1], ";\n      left: 50%;\n      transform: translateX(-50%);\n      width: 30px;\n      height: 30px;\n      border: 2px solid ").concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.neutral[600], ";\n      border-radius: 50%;\n    }\n  ");
});
var ResponsiveCanvas = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject1 || (_templateObject1 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  width: ", "px;\n  height: ", "px;\n  max-width: 100%;\n  max-height: 100%;\n  background: ", ";\n  border-radius: ", ";\n  overflow: auto;\n  position: relative;\n  transform: scale(", ");\n  transform-origin: top center;\n  transition: ", ";\n  box-shadow: ", ";\n  \n  /* Responsive scaling */\n  ", " {\n    transform: scale(", ");\n  }\n  \n  ", " {\n    transform: scale(", ");\n  }\n  \n  ", " {\n    transform: scale(", ");\n  }\n  \n  /* Reduced motion support */\n  @media (prefers-reduced-motion: reduce) {\n    transition: none;\n  }\n"])), function (props) {
  return props.deviceWidth;
}, function (props) {
  return props.deviceHeight;
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.background.paper, function (props) {
  switch (props.deviceType) {
    case 'mobile':
      return _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.borderRadius.lg;
    case 'tablet':
      return _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.borderRadius.md;
    default:
      return '0';
  }
}, function (props) {
  return props.scale;
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.transitions["default"], function (props) {
  return props.deviceType !== 'desktop' ? _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.shadows.inner : 'none';
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.mediaQueries.maxXl, function (props) {
  return Math.min(props.scale, 0.9);
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.mediaQueries.maxLg, function (props) {
  return Math.min(props.scale, 0.8);
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.mediaQueries.maxMd, function (props) {
  return Math.min(props.scale, 0.7);
});
var BreakpointIndicator = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject10 || (_templateObject10 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  position: absolute;\n  top: ", ";\n  left: ", ";\n  background: ", ";\n  color: ", ";\n  padding: ", " ", ";\n  border-radius: ", ";\n  font-size: ", ";\n  font-weight: ", ";\n  z-index: ", ";\n  box-shadow: ", ";\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.main, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.contrastText, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[1], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.borderRadius.sm, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.typography.fontSize.xs, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.typography.fontWeight.medium, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.zIndex.popover, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.shadows.sm);
var PerformanceIndicator = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject11 || (_templateObject11 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  position: absolute;\n  top: ", ";\n  right: ", ";\n  background: rgba(0, 0, 0, 0.8);\n  color: white;\n  padding: ", ";\n  border-radius: ", ";\n  font-size: ", ";\n  z-index: ", ";\n  min-width: 120px;\n  \n  .metric {\n    display: flex;\n    justify-content: space-between;\n    margin-bottom: ", ";\n    \n    &:last-child {\n      margin-bottom: 0;\n    }\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.borderRadius.md, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.typography.fontSize.xs, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.zIndex.popover, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[1]);
var DropZone = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div.withConfig({
  shouldForwardProp: function shouldForwardProp(prop) {
    return !['visible', 'isActive'].includes(prop);
  }
})(_templateObject12 || (_templateObject12 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  border: 2px dashed ", ";\n  border-radius: ", ";\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  background: rgba(255, 255, 255, 0.9);\n  opacity: ", ";\n  pointer-events: ", ";\n  transition: ", ";\n  z-index: ", ";\n\n  ", "\n  \n  .drop-message {\n    color: ", ";\n    font-weight: ", ";\n    font-size: ", ";\n    margin-top: ", ";\n  }\n  \n  .drop-hint {\n    color: ", ";\n    font-size: ", ";\n    margin-top: ", ";\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.border.medium, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.borderRadius.lg, function (props) {
  return props.visible ? 1 : 0;
}, function (props) {
  return props.visible ? 'auto' : 'none';
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.transitions["default"], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.zIndex.overlay, function (props) {
  return props.isActive && "\n    border-color: ".concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.success.main, ";\n    background: ").concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.success.light, ";\n\n    .drop-message {\n      color: ").concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.success.dark, ";\n    }\n  ");
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.text.secondary, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.typography.fontWeight.medium, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.typography.fontSize.lg, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.text.tertiary, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.typography.fontSize.sm, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[1]);
var LoadingOverlay = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject13 || (_templateObject13 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(255, 255, 255, 0.8);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  z-index: ", ";\n  \n  .loading-text {\n    margin-top: ", ";\n    color: ", ";\n    font-weight: ", ";\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.zIndex.modal, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.text.secondary, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.typography.fontWeight.medium);

// Component renderer function - moved to inside the main component

// Enhanced component wrapper with clear visual indicators
var ComponentWrapper = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div.withConfig({
  shouldForwardProp: function shouldForwardProp(prop) {
    return !['previewMode', 'isSelected', 'isHovered', 'isDragOver'].includes(prop);
  }
})(_templateObject14 || (_templateObject14 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n    position: relative;\n    margin: 4px 0;\n    border: ", ";\n    border-radius: ", ";\n    background: ", ";\n    transition: all 0.2s ease;\n    cursor: ", ";\n    min-height: ", ";\n    padding: ", ";\n\n    &:hover {\n      ", "\n    }\n\n    &::before {\n      content: '';\n      position: absolute;\n      top: -1px;\n      left: -1px;\n      right: -1px;\n      bottom: -1px;\n      border: 2px solid transparent;\n      border-radius: ", ";\n      pointer-events: none;\n      transition: border-color 0.2s ease;\n      ", "\n    }\n  "])), function (props) {
  if (props.previewMode) return '1px solid transparent';
  if (props.isSelected) return "2px solid ".concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.main);
  return '1px dashed rgba(0, 0, 0, 0.1)';
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.borderRadius.sm, function (props) {
  if (props.previewMode) return 'transparent';
  if (props.isSelected) return "".concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.main, "08");
  return 'rgba(255, 255, 255, 0.8)';
}, function (props) {
  return props.previewMode ? 'default' : 'pointer';
}, function (props) {
  return props.previewMode ? 'auto' : '32px';
}, function (props) {
  return props.previewMode ? '0' : '8px';
}, function (props) {
  return !props.previewMode && "\n        border-color: ".concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.main, ";\n        background: ").concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.main, "12;\n        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);\n        transform: translateY(-1px);\n      ");
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.borderRadius.sm, function (props) {
  return props.isSelected && "border-color: ".concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.main, ";");
});

// Component type badge for identification
var ComponentBadge = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div.withConfig({
  shouldForwardProp: function shouldForwardProp(prop) {
    return !['isSelected', 'previewMode'].includes(prop);
  }
})(_templateObject15 || (_templateObject15 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n    position: absolute;\n    top: -8px;\n    left: 8px;\n    background: ", ";\n    color: white;\n    padding: 2px 8px;\n    border-radius: 12px;\n    font-size: 10px;\n    font-weight: 500;\n    text-transform: uppercase;\n    letter-spacing: 0.5px;\n    z-index: 10;\n    opacity: ", ";\n    transform: ", ";\n    transition: all 0.2s ease;\n    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n  "])), function (props) {
  return props.isSelected ? _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.main : '#666';
}, function (props) {
  return props.previewMode ? 0 : 1;
}, function (props) {
  return props.previewMode ? 'scale(0.8)' : 'scale(1)';
});

// Selection handles for resize/move operations
var SelectionHandles = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject16 || (_templateObject16 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n    position: absolute;\n    top: -4px;\n    left: -4px;\n    right: -4px;\n    bottom: -4px;\n    pointer-events: none;\n    opacity: ", ";\n    transition: opacity 0.2s ease;\n\n    &::before, &::after {\n      content: '';\n      position: absolute;\n      width: 8px;\n      height: 8px;\n      background: ", ";\n      border: 2px solid white;\n      border-radius: 50%;\n      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);\n    }\n\n    &::before {\n      top: -4px;\n      left: -4px;\n    }\n\n    &::after {\n      bottom: -4px;\n      right: -4px;\n    }\n  "])), function (props) {
  return props.visible ? 1 : 0;
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.main);

// Edit indicator for text components
var EditIndicator = styled_components__WEBPACK_IMPORTED_MODULE_6__/* ["default"] */ .Ay.div(_templateObject17 || (_templateObject17 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(["\n    position: absolute;\n    top: 4px;\n    right: 4px;\n    width: 16px;\n    height: 16px;\n    background: ", ";\n    border-radius: 50%;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    opacity: ", ";\n    transition: all 0.2s ease;\n    cursor: pointer;\n    z-index: 10;\n\n    &:hover {\n      transform: scale(1.1);\n      background: ", ";\n    }\n\n    svg {\n      width: 10px;\n      height: 10px;\n      color: white;\n    }\n  "])), _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.main, function (props) {
  return props.visible ? 1 : 0;
}, _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.primary.dark);

// Component rendering logic moved inside main component

function UXEnhancedPreviewArea(_ref) {
  var _ref$components = _ref.components,
    components = _ref$components === void 0 ? [] : _ref$components,
    onSelectComponent = _ref.onSelectComponent,
    onDeleteComponent = _ref.onDeleteComponent,
    onUpdateComponent = _ref.onUpdateComponent,
    onMoveComponent = _ref.onMoveComponent,
    _ref$previewMode = _ref.previewMode,
    previewMode = _ref$previewMode === void 0 ? false : _ref$previewMode,
    selectedComponentId = _ref.selectedComponentId,
    onDrop = _ref.onDrop,
    onDragOver = _ref.onDragOver,
    onDragLeave = _ref.onDragLeave,
    _ref$realTimeUpdates = _ref.realTimeUpdates,
    realTimeUpdates = _ref$realTimeUpdates === void 0 ? true : _ref$realTimeUpdates,
    _ref$websocketConnect = _ref.websocketConnected,
    websocketConnected = _ref$websocketConnect === void 0 ? false : _ref$websocketConnect,
    _ref$showPerformanceM = _ref.showPerformanceMetrics,
    showPerformanceMetrics = _ref$showPerformanceM === void 0 ? false : _ref$showPerformanceM,
    _ref$enableDeviceFram = _ref.enableDeviceFrames,
    enableDeviceFrames = _ref$enableDeviceFram === void 0 ? true : _ref$enableDeviceFram,
    _ref$loading = _ref.loading,
    loading = _ref$loading === void 0 ? false : _ref$loading;
  // State management
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('desktop'),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    deviceType = _useState2[0],
    setDeviceType = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(1),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState3, 2),
    zoom = _useState4[0],
    setZoom = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState5, 2),
    showGrid = _useState6[0],
    setShowGrid = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(20),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState7, 2),
    gridSize = _useState8[0],
    setGridSize = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState9, 2),
    showBreakpoints = _useState0[0],
    setShowBreakpoints = _useState0[1];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState1, 2),
    isDragOver = _useState10[0],
    setIsDragOver = _useState10[1];
  var _useState11 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false),
    _useState12 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState11, 2),
    isFullscreen = _useState12[0],
    setIsFullscreen = _useState12[1];
  var _useState13 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null),
    _useState14 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState13, 2),
    lastUpdateTime = _useState14[0],
    setLastUpdateTime = _useState14[1];
  var _useState15 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null),
    _useState16 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState15, 2),
    hoveredComponent = _useState16[0],
    setHoveredComponent = _useState16[1];

  // Refs
  var canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);
  var containerRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);

  // Enhanced component rendering function with visual indicators
  var renderComponent = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (component, currentDeviceType, currentPreviewMode) {
    if (!component) return null;
    var isSelected = selectedComponentId === component.id;
    var isHovered = hoveredComponent === component.id;
    var showIndicators = !currentPreviewMode && (isSelected || isHovered);

    // Responsive styles based on device type
    var getResponsiveStyles = function getResponsiveStyles() {
      var baseStyles = {
        fontSize: currentDeviceType === 'mobile' ? '14px' : currentDeviceType === 'tablet' ? '16px' : '16px',
        padding: currentDeviceType === 'mobile' ? '8px' : '12px',
        margin: currentDeviceType === 'mobile' ? '4px 0' : '8px 0'
      };
      return _objectSpread(_objectSpread({}, baseStyles), component.style);
    };
    var responsiveStyles = getResponsiveStyles();

    // Component content renderer
    var renderComponentContent = function renderComponentContent() {
      var _component$props, _component$props2, _component$props3, _component$props4, _component$props5, _component$props6, _component$props7, _component$props8, _component$props9, _component$props0, _component$props1;
      switch (component.type) {
        case 'text':
          return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
            style: _objectSpread(_objectSpread({}, responsiveStyles), {}, {
              minHeight: currentPreviewMode ? 'auto' : '20px',
              display: 'block',
              padding: currentPreviewMode ? '0' : '4px',
              background: currentPreviewMode ? 'transparent' : 'rgba(255, 255, 255, 0.9)',
              borderRadius: '2px'
            })
          }, ((_component$props = component.props) === null || _component$props === void 0 ? void 0 : _component$props.content) || 'Click to edit text...');
        case 'button':
          return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
            type: ((_component$props2 = component.props) === null || _component$props2 === void 0 ? void 0 : _component$props2.type) || 'default',
            size: currentDeviceType === 'mobile' ? 'small' : 'middle',
            style: {
              fontSize: responsiveStyles.fontSize,
              minWidth: currentPreviewMode ? 'auto' : '80px'
            }
          }, ((_component$props3 = component.props) === null || _component$props3 === void 0 ? void 0 : _component$props3.text) || 'Button');
        case 'header':
          return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Title, {
            level: ((_component$props4 = component.props) === null || _component$props4 === void 0 ? void 0 : _component$props4.level) || (currentDeviceType === 'mobile' ? 4 : 2),
            style: responsiveStyles
          }, ((_component$props5 = component.props) === null || _component$props5 === void 0 ? void 0 : _component$props5.text) || 'Header');
        case 'card':
          return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Card */ .Zp, {
            title: ((_component$props6 = component.props) === null || _component$props6 === void 0 ? void 0 : _component$props6.title) || 'Card Title',
            size: currentDeviceType === 'mobile' ? 'small' : 'default',
            style: {
              fontSize: responsiveStyles.fontSize
            }
          }, ((_component$props7 = component.props) === null || _component$props7 === void 0 ? void 0 : _component$props7.content) || 'Card content');
        case 'image':
          return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("img", {
            src: ((_component$props8 = component.props) === null || _component$props8 === void 0 ? void 0 : _component$props8.src) || 'https://via.placeholder.com/150',
            alt: ((_component$props9 = component.props) === null || _component$props9 === void 0 ? void 0 : _component$props9.alt) || 'Image',
            style: {
              maxWidth: '100%',
              height: 'auto',
              borderRadius: currentDeviceType === 'mobile' ? '4px' : '6px'
            }
          });
        case 'divider':
          return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Divider */ .cG, {
            style: responsiveStyles
          }, (_component$props0 = component.props) === null || _component$props0 === void 0 ? void 0 : _component$props0.text);
        case 'input':
          return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Input */ .pd, {
            placeholder: ((_component$props1 = component.props) === null || _component$props1 === void 0 ? void 0 : _component$props1.placeholder) || 'Enter text',
            disabled: currentPreviewMode ? false : true,
            size: currentDeviceType === 'mobile' ? 'small' : 'middle',
            style: responsiveStyles
          });
        case 'form':
          return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV, {
            layout: "vertical",
            size: currentDeviceType === 'mobile' ? 'small' : 'middle'
          }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Form */ .lV.Item, {
            label: "Sample Field"
          }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Input */ .pd, {
            placeholder: "Sample input",
            disabled: !currentPreviewMode,
            style: responsiveStyles
          })));
        case 'table':
          var columns = [{
            title: 'Name',
            dataIndex: 'name',
            key: 'name'
          }, {
            title: 'Age',
            dataIndex: 'age',
            key: 'age'
          }];
          var data = [{
            key: '1',
            name: 'John',
            age: 32
          }, {
            key: '2',
            name: 'Jane',
            age: 28
          }];
          return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Table */ .XI, {
            columns: columns,
            dataSource: data,
            size: currentDeviceType === 'mobile' ? 'small' : 'middle',
            scroll: currentDeviceType === 'mobile' ? {
              x: true
            } : undefined
          });
        default:
          return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
            style: {
              padding: '8px',
              textAlign: 'center',
              color: '#666',
              border: '1px dashed #ccc',
              borderRadius: currentDeviceType === 'mobile' ? '4px' : '6px'
            }
          }, component.type, " Component");
      }
    };
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
      key: component.id,
      onMouseEnter: function onMouseEnter() {
        return setHoveredComponent(component.id);
      },
      onMouseLeave: function onMouseLeave() {
        return setHoveredComponent(null);
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ComponentWrapper, {
      isSelected: isSelected,
      previewMode: currentPreviewMode,
      style: responsiveStyles,
      onClick: function onClick(e) {
        e.stopPropagation();
        if (!currentPreviewMode && onSelectComponent) {
          onSelectComponent(component);
        }
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ComponentBadge, {
      isSelected: isSelected,
      previewMode: currentPreviewMode
    }, component.type), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(SelectionHandles, {
      visible: showIndicators
    }), component.type === 'text' && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(EditIndicator, {
      visible: showIndicators
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .EditOutlined */ .xjh, null)), renderComponentContent()));
  }, [selectedComponentId, hoveredComponent, onSelectComponent]);

  // Debug logging
  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(function () {
    console.log('UXEnhancedPreviewArea - Components:', components);
    console.log('UXEnhancedPreviewArea - Components length:', components.length);
    console.log('UXEnhancedPreviewArea - Device type:', deviceType);
    console.log('UXEnhancedPreviewArea - Preview mode:', previewMode);
    console.log('UXEnhancedPreviewArea - Component rendering fixed!');
  }, [components, deviceType, previewMode]);

  // Test component addition
  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(function () {
    if (components.length === 0) {
      console.log('No components found. You can test by clicking on components in the palette.');

      // Add a test component for debugging (only in development)
      if (false) {}
    }
  }, [components.length, onDrop]);

  // Get current device configuration
  var currentDevice = DEVICE_CONFIGS[deviceType];

  // Memoized device-specific styles
  var deviceStyles = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function () {
    return {
      width: currentDevice.width,
      height: currentDevice.height,
      scale: zoom
    };
  }, [currentDevice, zoom]);

  // Real-time preview hook (with fallback)
  var realTimePreviewHook = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function () {
    try {
      return (0,_hooks_useRealTimePreview__WEBPACK_IMPORTED_MODULE_8__/* ["default"] */ .A)({
        components: components,
        onUpdateComponent: onUpdateComponent,
        onDeleteComponent: onDeleteComponent,
        enableWebSocket: realTimeUpdates && websocketConnected
      });
    } catch (error) {
      console.warn('Real-time preview hook failed, using fallback:', error);
      return {
        isUpdating: false,
        websocketConnected: false,
        updateComponent: onUpdateComponent || function () {},
        getAllComponents: function getAllComponents() {
          return components;
        }
      };
    }
  }, [components, onUpdateComponent, onDeleteComponent, realTimeUpdates, websocketConnected]);
  var isUpdating = realTimePreviewHook.isUpdating,
    realtimeConnected = realTimePreviewHook.websocketConnected,
    updateComponent = realTimePreviewHook.updateComponent,
    getAllComponents = realTimePreviewHook.getAllComponents;

  // Performance monitoring hook (with fallback)
  var performanceHook = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(function () {
    try {
      return (0,_hooks_usePreviewPerformance__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .A)({
        components: getAllComponents(),
        enablePerformanceMonitoring: showPerformanceMetrics
      });
    } catch (error) {
      console.warn('Performance monitoring hook failed, using fallback:', error);
      return {
        renderTime: 0,
        frameRate: 60,
        memoryUsage: 0,
        componentCount: components.length
      };
    }
  }, [getAllComponents, showPerformanceMetrics, components.length]);
  var renderTime = performanceHook.renderTime,
    frameRate = performanceHook.frameRate,
    memoryUsage = performanceHook.memoryUsage,
    componentCount = performanceHook.componentCount;

  // Device change handler
  var handleDeviceChange = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (newDeviceType) {
    setDeviceType(newDeviceType);
    var device = DEVICE_CONFIGS[newDeviceType];
    setZoom(device.scale);
  }, []);

  // Zoom controls
  var handleZoomIn = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function () {
    setZoom(function (prev) {
      return Math.min(prev + 0.1, 2);
    });
  }, []);
  var handleZoomOut = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function () {
    setZoom(function (prev) {
      return Math.max(prev - 0.1, 0.3);
    });
  }, []);
  var handleZoomReset = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function () {
    setZoom(currentDevice.scale);
  }, [currentDevice.scale]);

  // Fullscreen toggle
  var handleFullscreenToggle = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function () {
    if (!document.fullscreenElement) {
      var _containerRef$current;
      (_containerRef$current = containerRef.current) === null || _containerRef$current === void 0 || _containerRef$current.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  }, []);

  // Drag and drop handlers
  var handleDragEnter = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (e) {
    e.preventDefault();
    setIsDragOver(true);
  }, []);
  var handleDragOverInternal = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (e) {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'copy';
    if (onDragOver) onDragOver(e);
  }, [onDragOver]);
  var handleDragLeave = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (e) {
    e.preventDefault();
    if (!e.currentTarget.contains(e.relatedTarget)) {
      setIsDragOver(false);
      if (onDragLeave) onDragLeave(e);
    }
  }, [onDragLeave]);
  var handleDrop = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(function (e) {
    e.preventDefault();
    setIsDragOver(false);
    if (onDrop) {
      var _canvasRef$current;
      var rect = (_canvasRef$current = canvasRef.current) === null || _canvasRef$current === void 0 ? void 0 : _canvasRef$current.getBoundingClientRect();
      if (rect) {
        var x = (e.clientX - rect.left) / zoom;
        var y = (e.clientY - rect.top) / zoom;
        onDrop(e, {
          x: x,
          y: y
        });
      }
    }
  }, [onDrop, zoom]);

  // Settings menu
  var settingsMenu = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Menu */ .W1, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Menu */ .W1.Item, {
    key: "grid"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Switch */ .dO, {
    size: "small",
    checked: showGrid,
    onChange: setShowGrid
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", null, "Show Grid"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Menu */ .W1.Item, {
    key: "breakpoints"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Switch */ .dO, {
    size: "small",
    checked: showBreakpoints,
    onChange: setShowBreakpoints
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", null, "Show Breakpoints"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Menu */ .W1.Item, {
    key: "performance"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Switch */ .dO, {
    size: "small",
    checked: showPerformanceMetrics,
    onChange: function onChange() {}
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", null, "Performance Metrics"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Menu */ .W1.Divider, null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Menu */ .W1.Item, {
    key: "gridSize"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(Text, {
    style: {
      fontSize: _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.typography.fontSize.xs
    }
  }, "Grid Size: ", gridSize, "px"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Slider */ .Ap, {
    min: 10,
    max: 50,
    value: gridSize,
    onChange: setGridSize,
    style: {
      width: 100,
      margin: "".concat(_design_system__WEBPACK_IMPORTED_MODULE_7__.theme.spacing[1], " 0")
    }
  }))));
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(PreviewContainer, {
    ref: containerRef
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(PreviewToolbar, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ToolbarSection, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(DeviceSelector, null, Object.entries(DEVICE_CONFIGS).map(function (_ref2) {
    var _ref3 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_ref2, 2),
      key = _ref3[0],
      device = _ref3[1];
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(DeviceButton, {
      key: key,
      size: "small",
      active: deviceType === key,
      onClick: function onClick() {
        return handleDeviceChange(key);
      },
      icon: device.icon,
      title: device.description
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", {
      className: "device-label"
    }, device.name));
  })), showBreakpoints && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(BreakpointIndicator, null, currentDevice.breakpoint.toUpperCase(), " \u2022 ", currentDevice.width, "\xD7", currentDevice.height)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ToolbarSection, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ZoomControls, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
    size: "small",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ZoomOutOutlined */ .uC4, null),
    onClick: handleZoomOut,
    disabled: zoom <= 0.3,
    title: "Zoom Out"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ZoomSlider, {
    min: 30,
    max: 200,
    value: Math.round(zoom * 100),
    onChange: function onChange(value) {
      return setZoom(value / 100);
    },
    tooltip: {
      formatter: function formatter(value) {
        return "".concat(value, "%");
      }
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
    size: "small",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .ZoomInOutlined */ .$gz, null),
    onClick: handleZoomIn,
    disabled: zoom >= 2,
    title: "Zoom In"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
    size: "small",
    onClick: handleZoomReset,
    title: "Reset Zoom"
  }, Math.round(zoom * 100), "%")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(StatusIndicator, {
    connected: realtimeConnected
  }, realtimeConnected ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .WifiOutlined */ ._bA, null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .DisconnectOutlined */ .Bu6, null), realtimeConnected ? 'Connected' : 'Offline', isUpdating && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .SyncOutlined */ .OmY, {
    spin: true
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Dropdown */ .ms, {
    overlay: settingsMenu,
    trigger: ['click']
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
    size: "small",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .SettingOutlined */ .JO7, null),
    title: "Settings"
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Button */ .$n, {
    size: "small",
    icon: isFullscreen ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .CompressOutlined */ .J5k, null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .FullscreenOutlined */ .KrH, null),
    onClick: handleFullscreenToggle,
    title: isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'
  })))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(CanvasContainer, {
    showGrid: showGrid,
    gridSize: gridSize,
    onDragEnter: handleDragEnter,
    onDragOver: handleDragOverInternal,
    onDragLeave: handleDragLeave,
    onDrop: handleDrop
  }, enableDeviceFrames && currentDevice.frame ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(DeviceFrame, {
    deviceType: deviceType,
    frame: currentDevice.frame
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ResponsiveCanvas, {
    ref: canvasRef,
    deviceWidth: deviceStyles.width,
    deviceHeight: deviceStyles.height,
    deviceType: deviceType,
    scale: zoom,
    onClick: function onClick() {
      return onSelectComponent && onSelectComponent(null);
    }
  }, components.length > 0 ? components.map(function (component) {
    return renderComponent(component, deviceType, previewMode);
  }) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Empty */ .Sv, {
    description: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", null, "No components added yet.", /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("br", null), previewMode ? 'Add components to see them here.' : 'Drag components from the palette to get started.'),
    style: {
      margin: deviceType === 'mobile' ? '50px 20px' : '100px 0',
      fontSize: deviceType === 'mobile' ? '14px' : '16px'
    }
  }))) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(ResponsiveCanvas, {
    ref: canvasRef,
    deviceWidth: deviceStyles.width,
    deviceHeight: deviceStyles.height,
    deviceType: deviceType,
    scale: zoom,
    onClick: function onClick() {
      return onSelectComponent && onSelectComponent(null);
    }
  }, components.length > 0 ? components.map(function (component) {
    return renderComponent(component, deviceType, previewMode);
  }) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Empty */ .Sv, {
    description: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("span", null, "No components added yet.", /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("br", null), previewMode ? 'Add components to see them here.' : 'Drag components from the palette to get started.'),
    style: {
      margin: deviceType === 'mobile' ? '50px 20px' : '100px 0',
      fontSize: deviceType === 'mobile' ? '14px' : '16px'
    }
  })), showPerformanceMetrics && "production" === 'development' && /*#__PURE__*/0, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(DropZone, {
    visible: isDragOver,
    isActive: isDragOver
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_5__/* .DragOutlined */ .duJ, {
    style: {
      fontSize: 48,
      color: _design_system__WEBPACK_IMPORTED_MODULE_7__.theme.colors.text.tertiary
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
    className: "drop-message"
  }, "Drop component here"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
    className: "drop-hint"
  }, "Release to add to canvas")), loading && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(LoadingOverlay, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement(antd__WEBPACK_IMPORTED_MODULE_4__/* .Spin */ .tK, {
    size: "large"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3__.createElement("div", {
    className: "loading-text"
  }, "Loading preview..."))));
}

/***/ }),

/***/ 90985:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (/* binding */ EnhancedComponentPaletteFixed)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(57528);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(96540);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(33966);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(36031);
/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(71606);
/* harmony import */ var _design_system__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(79146);
/* harmony import */ var _hooks_useAIDesignSuggestions__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(87169);




var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6, _templateObject7, _templateObject8, _templateObject9, _templateObject0, _templateObject1, _templateObject10, _templateObject11;
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * Enhanced Component Palette with improved visual hierarchy and categorization
 */







var Search = antd__WEBPACK_IMPORTED_MODULE_5__/* .Input */ .pd.Search;
var Text = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Text,
  Title = antd__WEBPACK_IMPORTED_MODULE_5__/* .Typography */ .o5.Title;
var Panel = antd__WEBPACK_IMPORTED_MODULE_5__/* .Collapse */ .SD.Panel;

// Enhanced styled components
var PaletteContainer = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  background: ", ";\n  border-radius: ", ";\n  box-shadow: ", ";\n  overflow: hidden;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  border: 1px solid ", ";\n"])), _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.background.paper, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.borderRadius.lg, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.shadows.lg, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.border.light);
var PaletteHeader = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  padding: ", ";\n  background: linear-gradient(135deg, ", " 0%, ", " 100%);\n  color: ", ";\n  border-bottom: 1px solid ", ";\n  \n  .ant-typography {\n    color: ", " !important;\n    margin-bottom: ", ";\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[4], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.primary.main, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.accent.main, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.primary.contrastText, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.border.light, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.primary.contrastText, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2]);
var SearchContainer = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  padding: ", " ", ";\n  background: ", ";\n  border-bottom: 1px solid ", ";\n"])), _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[3], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[4], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.background.secondary, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.border.light);
var FilterControls = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: ", ";\n  flex-wrap: wrap;\n  gap: ", ";\n"])), _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2]);
var ComponentGrid = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));\n  gap: ", ";\n  padding: ", ";\n  \n  ", " {\n    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));\n    gap: ", ";\n    padding: ", ";\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[3], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.mediaQueries.maxMd, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[1], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2]);
var ComponentCard = (0,styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay)(antd__WEBPACK_IMPORTED_MODULE_5__/* .Card */ .Zp)(_templateObject6 || (_templateObject6 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  cursor: grab;\n  transition: ", ";\n  border: 1px solid ", ";\n  border-radius: ", ";\n  position: relative;\n  min-height: 140px;\n  overflow: hidden;\n  background: ", ";\n  \n  /* Enhanced visual hierarchy */\n  box-shadow: ", ";\n  \n  &:hover {\n    transform: translateY(-3px);\n    box-shadow: ", ";\n    border-color: ", ";\n    background: ", "05;\n    \n    .component-icon {\n      transform: scale(1.15);\n      background: ", "20;\n    }\n    \n    .component-label {\n      color: ", ";\n      font-weight: ", ";\n    }\n  }\n  \n  &:active {\n    cursor: grabbing;\n    transform: scale(0.95) translateY(-1px);\n  }\n  \n  &:focus-visible {\n    outline: 2px solid ", ";\n    outline-offset: 2px;\n  }\n  \n  /* Complexity indicator */\n  &::before {\n    content: '';\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    height: 3px;\n    background: ", ";\n    opacity: 0.8;\n  }\n  \n  .ant-card-body {\n    padding: ", ";\n    text-align: center;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    height: 100%;\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.transitions["default"], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.border.light, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.borderRadius.md, function (props) {
  return props.isDragging ? _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.primary.light : _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.background.paper;
}, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.shadows.sm, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.shadows.xl, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.primary.main, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.primary.main, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.primary.main, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.primary.main, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.typography.fontWeight.semibold, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.primary.main, function (props) {
  switch (props.complexity) {
    case 'simple':
      return _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.success.main;
    case 'medium':
      return _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.warning.main;
    case 'complex':
      return _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.error.main;
    default:
      return _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.neutral[300];
  }
}, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[3]);
var ComponentIcon = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject7 || (_templateObject7 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  font-size: 28px;\n  color: ", ";\n  margin-bottom: ", ";\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  width: 48px;\n  height: 48px;\n  border-radius: ", ";\n  background: ", ";\n  margin: 0 auto ", ";\n  transition: ", ";\n  position: relative;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  \n  @media (prefers-contrast: high) {\n    border: 2px solid currentColor;\n    background: transparent;\n  }\n"])), function (props) {
  return props.color || _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.primary.main;
}, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.borderRadius.lg, function (props) {
  return props.gradient || "linear-gradient(135deg, ".concat(_design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.primary.main, "15 0%, ").concat(_design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.primary.main, "25 100%)");
}, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.transitions["default"]);
var ComponentLabel = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject8 || (_templateObject8 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  font-size: ", ";\n  font-weight: ", ";\n  color: ", ";\n  margin-bottom: ", ";\n  line-height: ", ";\n"])), _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.typography.fontSize.sm, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.typography.fontWeight.medium, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.text.primary, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[1], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.typography.lineHeight.tight);
var ComponentDescription = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject9 || (_templateObject9 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  font-size: ", ";\n  color: ", ";\n  line-height: ", ";\n  text-align: center;\n  margin-top: ", ";\n"])), _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.typography.fontSize.xs, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.text.secondary, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.typography.lineHeight.normal, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[1]);
var ComponentPreview = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject0 || (_templateObject0 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  position: absolute;\n  bottom: 8px;\n  right: 8px;\n  width: 24px;\n  height: 16px;\n  background: ", ";\n  border: 1px solid ", ";\n  border-radius: 2px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 10px;\n  font-weight: bold;\n  color: ", ";\n  opacity: 0.7;\n  transition: ", ";\n  \n  ", ":hover & {\n    opacity: 1;\n    transform: scale(1.1);\n    background: ", ";\n    box-shadow: ", ";\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.neutral[100], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.border.light, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.text.secondary, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.transitions["default"], ComponentCard, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.background.paper, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.shadows.sm);
var ComplexityBadge = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject1 || (_templateObject1 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  position: absolute;\n  top: 8px;\n  right: 8px;\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  background: ", ";\n  opacity: 0.8;\n  transition: ", ";\n  \n  ", ":hover & {\n    opacity: 1;\n    transform: scale(1.2);\n  }\n"])), function (props) {
  switch (props.complexity) {
    case 'simple':
      return _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.success.main;
    case 'medium':
      return _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.warning.main;
    case 'complex':
      return _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.error.main;
    default:
      return _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.neutral[300];
  }
}, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.transitions["default"], ComponentCard);
var CategoryHeader = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject10 || (_templateObject10 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: ", " ", ";\n  background: ", ";\n  border-bottom: 1px solid ", ";\n  font-weight: ", ";\n  color: ", ";\n  cursor: pointer;\n  transition: ", ";\n  \n  &:hover {\n    background: ", ";\n  }\n  \n  &:focus {\n    ", ";\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[3], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[4], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.background.tertiary, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.border.light, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.typography.fontWeight.semibold, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.text.primary, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.transitions["default"], _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.interactive.hover, _design_system__WEBPACK_IMPORTED_MODULE_8__.a11yUtils.focusRing());
var ScrollableContent = styled_components__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .Ay.div(_templateObject11 || (_templateObject11 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(["\n  flex: 1;\n  overflow-y: auto;\n  overflow-x: hidden;\n  \n  &::-webkit-scrollbar {\n    width: 6px;\n  }\n  \n  &::-webkit-scrollbar-track {\n    background: ", ";\n  }\n  \n  &::-webkit-scrollbar-thumb {\n    background: ", ";\n    border-radius: ", ";\n  }\n  \n  &::-webkit-scrollbar-thumb:hover {\n    background: ", ";\n  }\n"])), _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.background.secondary, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.border.medium, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.borderRadius.full, _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.border.dark);

// Component data with enhanced metadata
var COMPONENT_GROUPS = [{
  id: 'layout',
  title: 'Layout',
  description: 'Structural components for organizing content',
  color: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.success.main,
  icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .LayoutOutlined */ .hy2, null),
  components: [{
    type: 'header',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .FontSizeOutlined */ .ld1, null),
    label: 'Header',
    description: 'Page or section header with title and navigation',
    usage: 'Use for page titles, navigation bars, or section headers',
    tags: ['layout', 'navigation', 'title'],
    complexity: 'simple',
    preview: '═══'
  }, {
    type: 'section',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .LayoutOutlined */ .hy2, null),
    label: 'Section',
    description: 'Container for grouping related content',
    usage: 'Organize content into logical sections',
    tags: ['layout', 'container', 'organization'],
    complexity: 'simple',
    preview: '▢'
  }, {
    type: 'card',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .CreditCardOutlined */ .wN, null),
    label: 'Card',
    description: 'Flexible content container with optional header and footer',
    usage: 'Display content in a clean, contained format',
    tags: ['layout', 'container', 'content'],
    complexity: 'medium',
    preview: '▢'
  }]
}, {
  id: 'basic',
  title: 'Basic Components',
  description: 'Essential UI elements for content and interaction',
  color: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.primary.main,
  icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .AppstoreOutlined */ .rS9, null),
  components: [{
    type: 'text',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .FileTextOutlined */ .y9H, null),
    label: 'Text',
    description: 'Formatted text content with typography options',
    usage: 'Display paragraphs, headings, and formatted text',
    tags: ['content', 'text', 'typography'],
    complexity: 'simple',
    preview: 'Aa'
  }, {
    type: 'button',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .AppstoreOutlined */ .rS9, null),
    label: 'Button',
    description: 'Interactive button for user actions',
    usage: 'Trigger actions, submit forms, or navigate',
    tags: ['interaction', 'action', 'click'],
    complexity: 'simple',
    preview: '▢'
  }, {
    type: 'image',
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .PictureOutlined */ .JZT, null),
    label: 'Image',
    description: 'Display images with responsive sizing',
    usage: 'Show photos, illustrations, or graphics',
    tags: ['media', 'visual', 'content'],
    complexity: 'simple',
    preview: '🖼'
  }]
}];
function EnhancedComponentPaletteFixed(_ref) {
  var onAddComponent = _ref.onAddComponent,
    onDragStart = _ref.onDragStart,
    onDragEnd = _ref.onDragEnd,
    _ref$components = _ref.components,
    components = _ref$components === void 0 ? [] : _ref$components,
    _ref$selectedComponen = _ref.selectedComponent,
    selectedComponent = _ref$selectedComponen === void 0 ? null : _ref$selectedComponen,
    _ref$showAISuggestion = _ref.showAISuggestions,
    showAISuggestions = _ref$showAISuggestion === void 0 ? true : _ref$showAISuggestion,
    _ref$loading = _ref.loading,
    loading = _ref$loading === void 0 ? false : _ref$loading;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(''),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState, 2),
    searchTerm = _useState2[0],
    setSearchTerm = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(['layout', 'basic']),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState3, 2),
    expandedCategories = _useState4[0],
    setExpandedCategories = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(true),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState5, 2),
    showDescriptions = _useState6[0],
    setShowDescriptions = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState7, 2),
    showTags = _useState8[0],
    setShowTags = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)('all'),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState9, 2),
    filterBy = _useState0[0],
    setFilterBy = _useState0[1];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)('name'),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState1, 2),
    sortBy = _useState10[0],
    setSortBy = _useState10[1];
  var _useState11 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null),
    _useState12 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState11, 2),
    draggedComponent = _useState12[0],
    setDraggedComponent = _useState12[1];
  var dragPreviewRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);

  // AI suggestions hook
  var _useAIDesignSuggestio = (0,_hooks_useAIDesignSuggestions__WEBPACK_IMPORTED_MODULE_9__/* ["default"] */ .A)({
      autoRefresh: true,
      context: {
        selectedComponent: selectedComponent
      }
    }),
    suggestions = _useAIDesignSuggestio.suggestions,
    aiLoading = _useAIDesignSuggestio.loading,
    applyComponentCombination = _useAIDesignSuggestio.applyComponentCombination,
    hasLayoutSuggestions = _useAIDesignSuggestio.hasLayoutSuggestions,
    hasCombinationSuggestions = _useAIDesignSuggestio.hasCombinationSuggestions;

  // Filter and sort components
  var filteredGroups = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(function () {
    var groups = [].concat(COMPONENT_GROUPS);

    // Add AI suggestions group if enabled
    if (showAISuggestions && hasCombinationSuggestions) {
      groups.unshift({
        id: 'ai',
        title: 'AI Suggestions',
        description: 'Smart component recommendations based on your current app',
        color: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.colors.accent.main,
        icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .RobotOutlined */ .J_h, null),
        isAI: true,
        components: [] // Will be populated with AI suggestions
      });
    }

    // Filter by search term
    if (searchTerm) {
      groups = groups.map(function (group) {
        return _objectSpread(_objectSpread({}, group), {}, {
          components: group.components.filter(function (component) {
            return component.label.toLowerCase().includes(searchTerm.toLowerCase()) || component.description.toLowerCase().includes(searchTerm.toLowerCase()) || component.tags.some(function (tag) {
              return tag.toLowerCase().includes(searchTerm.toLowerCase());
            });
          })
        });
      }).filter(function (group) {
        return group.components.length > 0 || group.isAI;
      });
    }

    // Filter by complexity
    if (filterBy !== 'all') {
      groups = groups.map(function (group) {
        return _objectSpread(_objectSpread({}, group), {}, {
          components: group.components.filter(function (component) {
            return component.complexity === filterBy;
          })
        });
      }).filter(function (group) {
        return group.components.length > 0 || group.isAI;
      });
    }

    // Sort components within each group
    groups = groups.map(function (group) {
      return _objectSpread(_objectSpread({}, group), {}, {
        components: (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(group.components).sort(function (a, b) {
          switch (sortBy) {
            case 'name':
              return a.label.localeCompare(b.label);
            case 'complexity':
              var complexityOrder = {
                simple: 0,
                medium: 1,
                complex: 2
              };
              return complexityOrder[a.complexity] - complexityOrder[b.complexity];
            default:
              return 0;
          }
        })
      });
    });
    return groups;
  }, [searchTerm, filterBy, sortBy, showAISuggestions, hasCombinationSuggestions]);
  var handleDragStart = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (e, component) {
    setDraggedComponent(component);

    // Set drag data
    e.dataTransfer.setData('application/json', JSON.stringify({
      type: component.type,
      label: component.label,
      source: 'palette'
    }));
    e.dataTransfer.effectAllowed = 'copy';
    if (onDragStart) {
      onDragStart(component);
    }
  }, [onDragStart]);
  var handleDragEnd = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (e) {
    setDraggedComponent(null);
    if (onDragEnd) {
      onDragEnd();
    }
  }, [onDragEnd]);
  var handleCategoryToggle = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (categoryId) {
    setExpandedCategories(function (prev) {
      return prev.includes(categoryId) ? prev.filter(function (cat) {
        return cat !== categoryId;
      }) : [].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(prev), [categoryId]);
    });
  }, []);
  var handleComponentClick = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (component) {
    if (onAddComponent) {
      onAddComponent(component.type);
    }
  }, [onAddComponent]);

  // Filter menu for dropdown
  var filterMenu = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Menu */ .W1, {
    selectedKeys: [filterBy],
    onClick: function onClick(_ref2) {
      var key = _ref2.key;
      return setFilterBy(key);
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Menu */ .W1.Item, {
    key: "all"
  }, "All Components"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Menu */ .W1.Item, {
    key: "simple"
  }, "Simple"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Menu */ .W1.Item, {
    key: "medium"
  }, "Medium"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Menu */ .W1.Item, {
    key: "complex"
  }, "Complex"));

  // Sort menu for dropdown
  var sortMenu = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Menu */ .W1, {
    selectedKeys: [sortBy],
    onClick: function onClick(_ref3) {
      var key = _ref3.key;
      return setSortBy(key);
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Menu */ .W1.Item, {
    key: "name"
  }, "Sort by Name"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Menu */ .W1.Item, {
    key: "complexity"
  }, "Sort by Complexity"));
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(PaletteContainer, {
    role: "region",
    "aria-label": "Component Palette"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(PaletteHeader, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Title, {
    level: 5,
    style: {
      margin: 0,
      color: 'white'
    }
  }, "Component Palette"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    style: {
      color: 'rgba(255, 255, 255, 0.8)'
    }
  }, "Drag components to the canvas or click to add")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(SearchContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Search, {
    placeholder: "Search components...",
    value: searchTerm,
    onChange: function onChange(e) {
      return setSearchTerm(e.target.value);
    },
    prefix: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .SearchOutlined */ .VrN, null),
    allowClear: true,
    style: {
      marginBottom: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[2]
    },
    "aria-label": "Search components"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(FilterControls, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, {
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Dropdown */ .ms, {
    overlay: filterMenu,
    trigger: ['click']
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    size: "small",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .FilterOutlined */ .Lxx, null)
  }, "Filter ", /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .DownOutlined */ .lHd, null))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Dropdown */ .ms, {
    overlay: sortMenu,
    trigger: ['click']
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Button */ .$n, {
    size: "small"
  }, "Sort ", /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .DownOutlined */ .lHd, null)))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, {
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(Text, {
    style: {
      fontSize: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.typography.fontSize.xs
    }
  }, "Descriptions:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Switch */ .dO, {
    size: "small",
    checked: showDescriptions,
    onChange: setShowDescriptions
  })))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(ScrollableContent, null, !loading && filteredGroups.map(function (group) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
      key: group.id
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(CategoryHeader, {
      onClick: function onClick() {
        return handleCategoryToggle(group.id);
      },
      tabIndex: 0,
      role: "button",
      "aria-expanded": expandedCategories.includes(group.id),
      "aria-controls": "category-".concat(group.id)
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
      style: {
        width: 12,
        height: 12,
        borderRadius: '50%',
        backgroundColor: group.color
      }
    }), group.icon, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("span", null, group.title), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Badge */ .Ex, {
      count: group.components.length,
      size: "small"
    }), group.isAI && aiLoading && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Badge */ .Ex, {
      status: "processing"
    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_6__/* .DownOutlined */ .lHd, {
      style: {
        transform: expandedCategories.includes(group.id) ? 'rotate(180deg)' : 'rotate(0deg)',
        transition: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.transitions["default"]
      }
    })), expandedCategories.includes(group.id) && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(ComponentGrid, {
      id: "category-".concat(group.id)
    }, group.components.map(function (component) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(ComponentCard, {
        key: component.type,
        size: "small",
        hoverable: true,
        complexity: component.complexity,
        isDragging: (draggedComponent === null || draggedComponent === void 0 ? void 0 : draggedComponent.type) === component.type,
        draggable: true,
        onDragStart: function onDragStart(e) {
          return handleDragStart(e, component);
        },
        onDragEnd: handleDragEnd,
        onClick: function onClick() {
          return handleComponentClick(component);
        },
        tabIndex: 0,
        role: "button",
        "aria-label": "Add ".concat(component.label, " component")
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(ComponentIcon, {
        className: "component-icon",
        color: group.color
      }, component.icon), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(ComponentLabel, {
        className: "component-label"
      }, component.label), showDescriptions && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(ComponentDescription, null, component.description), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(ComponentPreview, null, component.preview), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(ComplexityBadge, {
        complexity: component.complexity
      }), showTags && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement("div", {
        style: {
          marginTop: _design_system__WEBPACK_IMPORTED_MODULE_8__.theme.spacing[1]
        }
      }, component.tags.slice(0, 2).map(function (tag) {
        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4__.createElement(antd__WEBPACK_IMPORTED_MODULE_5__/* .Tag */ .vw, {
          key: tag,
          size: "small",
          style: {
            fontSize: '10px'
          }
        }, tag);
      })));
    })));
  })));
}

/***/ })

}]);