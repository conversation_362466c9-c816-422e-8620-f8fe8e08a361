"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[5669],{

/***/ 5669:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   b: () => (/* binding */ generateCode)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(64467);
/* harmony import */ var _enhanced_code_generators_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(99131);
/* harmony import */ var _project_generators_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(36526);



function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * Enhanced code generation utilities for App Builder
 * Supports multiple frameworks, TypeScript, accessibility, and modern best practices
 */




/**
 * Generate code based on the app structure
 * @param {Object} appData - The app data structure
 * @param {string} format - The format to generate (react, html, css, etc.)
 * @param {Object} options - Generation options
 * @returns {string|Object} - The generated code or project structure
 */
var generateCode = function generateCode(appData) {
  var format = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'react';
  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
  var defaultOptions = _objectSpread({
    typescript: false,
    includeAccessibility: true,
    includeTests: false,
    includeStorybook: false,
    styleFramework: 'styled-components',
    // 'styled-components', 'emotion', 'tailwind', 'css-modules'
    stateManagement: 'useState',
    // 'useState', 'redux', 'zustand', 'context'
    projectStructure: 'single-file',
    // 'single-file', 'multi-file', 'full-project'
    bundler: 'vite',
    // 'vite', 'webpack', 'parcel'
    packageManager: 'npm'
  }, options);
  switch (format) {
    case 'react':
      return generateReactCode(appData, defaultOptions);
    case 'react-ts':
      return generateReactCode(appData, _objectSpread(_objectSpread({}, defaultOptions), {}, {
        typescript: true
      }));
    case 'vue':
      return (0,_enhanced_code_generators_js__WEBPACK_IMPORTED_MODULE_3__/* .generateVueCode */ .HM)(appData, defaultOptions);
    case 'vue-ts':
      return (0,_enhanced_code_generators_js__WEBPACK_IMPORTED_MODULE_3__/* .generateVueCode */ .HM)(appData, _objectSpread(_objectSpread({}, defaultOptions), {}, {
        typescript: true
      }));
    case 'angular':
      return (0,_enhanced_code_generators_js__WEBPACK_IMPORTED_MODULE_3__/* .generateAngularCode */ .hi)(appData, defaultOptions);
    case 'svelte':
      return (0,_enhanced_code_generators_js__WEBPACK_IMPORTED_MODULE_3__/* .generateSvelteCode */ ._k)(appData, defaultOptions);
    case 'next':
      return (0,_enhanced_code_generators_js__WEBPACK_IMPORTED_MODULE_3__/* .generateNextJSCode */ .Pp)(appData, defaultOptions);
    case 'nuxt':
      return generateNuxtCode(appData, defaultOptions);
    case 'html':
      return generateHtmlCode(appData, defaultOptions);
    case 'css':
      return generateCssCode(appData, defaultOptions);
    case 'react-native':
      return (0,_enhanced_code_generators_js__WEBPACK_IMPORTED_MODULE_3__/* .generateReactNativeCode */ .Zh)(appData, defaultOptions);
    case 'flutter':
      return generateFlutterCode(appData, defaultOptions);
    case 'ionic':
      return generateIonicCode(appData, defaultOptions);
    default:
      return JSON.stringify(appData, null, 2);
  }
};

/**
 * Generate enhanced React code with modern best practices
 * @param {Object} appData - The app data structure
 * @param {Object} options - Generation options
 * @returns {string|Object} - The generated React code or project structure
 */
var generateReactCode = function generateReactCode(appData) {
  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var components = appData.components,
    layouts = appData.layouts,
    styles = appData.styles,
    data = appData.data;
  var typescript = options.typescript,
    includeAccessibility = options.includeAccessibility,
    projectStructure = options.projectStructure,
    styleFramework = options.styleFramework,
    stateManagement = options.stateManagement;
  if (projectStructure === 'full-project') {
    return generateReactProject(appData, options);
  }

  // Generate imports
  var imports = generateReactImports(components, options);

  // Generate TypeScript interfaces if needed
  var interfaces = '';
  if (typescript) {
    interfaces = generateTypeScriptInterfaces(components, layouts);
  }

  // Generate styled components or CSS
  var stylesCode = generateReactStyles(styles, components, layouts, options);

  // Generate component definitions
  var componentDefinitions = generateReactComponents(components, options);

  // Generate main App component
  var appComponent = generateReactAppComponent(components, layouts, data, options);

  // Combine all parts
  var fileExtension = typescript ? 'tsx' : 'jsx';
  var code = "".concat(imports, "\n").concat(interfaces, "\n").concat(stylesCode, "\n").concat(componentDefinitions, "\n").concat(appComponent);
  if (projectStructure === 'single-file') {
    return code;
  } else {
    return _objectSpread((0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)((0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)((0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)({}, "App.".concat(fileExtension), code), 'package.json', (0,_project_generators_js__WEBPACK_IMPORTED_MODULE_4__/* .generatePackageJson */ .Qd)('react-app', options)), 'README.md', (0,_project_generators_js__WEBPACK_IMPORTED_MODULE_4__/* .generateReadme */ .uc)('React', options)), typescript && {
      'tsconfig.json': (0,_project_generators_js__WEBPACK_IMPORTED_MODULE_4__/* .generateTSConfig */ .v9)()
    });
  }
};

/**
 * Generate React imports based on components and options
 */
var generateReactImports = function generateReactImports(components, options) {
  var typescript = options.typescript,
    styleFramework = options.styleFramework,
    stateManagement = options.stateManagement;
  var imports = "import React".concat(stateManagement === 'useState' ? ', { useState, useEffect }' : '', " from 'react';\n");

  // Style framework imports
  if (styleFramework === 'styled-components') {
    imports += "import styled from 'styled-components';\n";
  } else if (styleFramework === 'emotion') {
    imports += "import styled from '@emotion/styled';\n";
  }

  // State management imports
  if (stateManagement === 'redux') {
    imports += "import { useSelector, useDispatch } from 'react-redux';\n";
  } else if (stateManagement === 'zustand') {
    imports += "import { useStore } from './store';\n";
  }

  // PropTypes for non-TypeScript projects
  if (!typescript) {
    imports += "import PropTypes from 'prop-types';\n";
  }
  return imports + '\n';
};

/**
 * Generate TypeScript interfaces
 */
var generateTypeScriptInterfaces = function generateTypeScriptInterfaces(components, layouts) {
  var interfaces = '// TypeScript Interfaces\n';

  // Component props interfaces
  var componentTypes = (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(new Set(components.map(function (c) {
    return c.type;
  })));
  componentTypes.forEach(function (type) {
    var component = components.find(function (c) {
      return c.type === type;
    });
    if (component && component.props) {
      interfaces += "interface ".concat(type, "Props {\n");
      Object.entries(component.props).forEach(function (_ref) {
        var _ref2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_ref, 2),
          prop = _ref2[0],
          value = _ref2[1];
        var propType = typeof value === 'string' ? 'string' : typeof value === 'number' ? 'number' : typeof value === 'boolean' ? 'boolean' : 'any';
        interfaces += "  ".concat(prop, "?: ").concat(propType, ";\n");
      });
      interfaces += "  className?: string;\n";
      interfaces += "  children?: React.ReactNode;\n";
      interfaces += "}\n\n";
    }
  });

  // App data interface
  interfaces += "interface AppData {\n";
  interfaces += "  components: ComponentData[];\n";
  interfaces += "  layouts: LayoutData[];\n";
  interfaces += "}\n\n";
  return interfaces;
};

/**
 * Generate React styles based on framework choice
 */
var generateReactStyles = function generateReactStyles(styles, components, layouts, options) {
  var styleFramework = options.styleFramework;
  if (styleFramework === 'styled-components' || styleFramework === 'emotion') {
    return generateStyledComponents(styles, components, layouts);
  } else if (styleFramework === 'tailwind') {
    return '// Tailwind CSS classes will be used inline\n\n';
  } else {
    return generateCSSModules(styles);
  }
};

/**
 * Generate styled-components
 */
var generateStyledComponents = function generateStyledComponents(styles, components, layouts) {
  var styledCode = '// Styled Components\n';

  // Container styles
  styledCode += "const AppContainer = styled.div`\n  min-height: 100vh;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\n  line-height: 1.6;\n  color: #333;\n`;\n\n";

  // Layout styles
  layouts.forEach(function (layout) {
    var layoutName = layout.name || layout.type || 'Layout';
    styledCode += "const ".concat(pascalCase(layoutName), "Container = styled.div`\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n  padding: 1rem;\n  ").concat(layout.styles ? Object.entries(layout.styles).map(function (_ref3) {
      var _ref4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_ref3, 2),
        prop = _ref4[0],
        value = _ref4[1];
      return "".concat(kebabToCamelCase(prop), ": ").concat(value, ";");
    }).join('\n  ') : '', "\n`;\n\n");
  });

  // Component styles
  var componentTypes = (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(new Set(components.map(function (c) {
    return c.type;
  })));
  componentTypes.forEach(function (type) {
    styledCode += "const Styled".concat(type, " = styled.div`\n  ").concat(generateComponentStyles(type), "\n`;\n\n");
  });
  return styledCode;
};

/**
 * Generate HTML code
 * @param {Object} appData - The app data structure
 * @param {Object} options - Generation options
 * @returns {string} - The generated HTML code
 */
var generateHtmlCode = function generateHtmlCode(appData) {
  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var components = appData.components,
    layouts = appData.layouts,
    styles = appData.styles;

  // Generate CSS
  var css = '';
  Object.entries(styles).forEach(function (_ref5) {
    var _ref6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_ref5, 2),
      selector = _ref6[0],
      style = _ref6[1];
    css += "".concat(selector, " {\n");
    Object.entries(style).forEach(function (_ref7) {
      var _ref8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_ref7, 2),
        prop = _ref8[0],
        value = _ref8[1];
      css += "  ".concat(prop, ": ").concat(value, ";\n");
    });
    css += '}\n\n';
  });

  // Generate HTML
  var html = '';
  layouts.forEach(function (layout) {
    html += "<div class=\"".concat(layout.type, "\">\n");
    layout.components.forEach(function (componentId) {
      var component = components.find(function (c) {
        return c.id === componentId;
      });
      if (component) {
        switch (component.type) {
          case 'Button':
            html += "  <button class=\"".concat(component.props.className || '', "\">").concat(component.props.text || 'Button', "</button>\n");
            break;
          case 'Input':
            html += "  <input type=\"".concat(component.props.type || 'text', "\" placeholder=\"").concat(component.props.placeholder || '', "\" class=\"").concat(component.props.className || '', "\" />\n");
            break;
          case 'Text':
            html += "  <p class=\"".concat(component.props.className || '', "\">").concat(component.props.content || '', "</p>\n");
            break;
          case 'Image':
            html += "  <img src=\"".concat(component.props.src || '', "\" alt=\"").concat(component.props.alt || '', "\" class=\"").concat(component.props.className || '', "\" />\n");
            break;
          default:
            html += "  <div class=\"".concat(component.type.toLowerCase(), "\">").concat(component.props.content || component.type, "</div>\n");
        }
      }
    });
    html += '</div>\n';
  });
  return "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>Generated App</title>\n  <style>\n".concat(css, "\n  </style>\n</head>\n<body>\n  <div class=\"app\">\n").concat(html, "\n  </div>\n</body>\n</html>\n");
};

/**
 * Generate CSS code
 * @param {Object} appData - The app data structure
 * @returns {string} - The generated CSS code
 */
var generateCssCode = function generateCssCode(appData) {
  var styles = appData.styles;
  var css = '';
  Object.entries(styles).forEach(function (_ref9) {
    var _ref0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_ref9, 2),
      selector = _ref0[0],
      style = _ref0[1];
    css += "".concat(selector, " {\n");
    Object.entries(style).forEach(function (_ref1) {
      var _ref10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_ref1, 2),
        prop = _ref10[0],
        value = _ref10[1];
      css += "  ".concat(prop, ": ").concat(value, ";\n");
    });
    css += '}\n\n';
  });
  return css;
};

/**
 * Generate React component definitions
 */
var generateReactComponents = function generateReactComponents(components, options) {
  var typescript = options.typescript,
    includeAccessibility = options.includeAccessibility;
  var componentCode = '// Component Definitions\n';
  var componentTypes = (0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(new Set(components.map(function (c) {
    return c.type;
  })));
  componentTypes.forEach(function (type) {
    var component = components.find(function (c) {
      return c.type === type;
    });
    var propsType = typescript ? "".concat(type, "Props") : '';
    componentCode += "const ".concat(type, " = (").concat(typescript ? "props: ".concat(propsType) : 'props', ") => {\n");
    componentCode += "  const { ".concat(Object.keys(component.props || {}).join(', '), ", className, children, ...rest } = props;\n\n");
    componentCode += "  return (\n";
    componentCode += "    <Styled".concat(type, "\n");
    componentCode += "      className={className}\n";
    if (includeAccessibility) {
      componentCode += "      role=\"".concat(getAriaRole(type), "\"\n");
      if (type === 'Button') {
        componentCode += "      aria-label={props['aria-label'] || props.text || 'Button'}\n";
      }
    }
    componentCode += "      {...rest}\n";
    componentCode += "    >\n";
    componentCode += "      ".concat(generateComponentContent(type, component.props), "\n");
    componentCode += "      {children}\n";
    componentCode += "    </Styled".concat(type, ">\n");
    componentCode += "  );\n";
    componentCode += "};\n\n";

    // Add PropTypes for non-TypeScript projects
    if (!typescript) {
      componentCode += "".concat(type, ".propTypes = {\n");
      Object.entries(component.props || {}).forEach(function (_ref11) {
        var _ref12 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_ref11, 2),
          prop = _ref12[0],
          value = _ref12[1];
        var propType = typeof value === 'string' ? 'PropTypes.string' : typeof value === 'number' ? 'PropTypes.number' : typeof value === 'boolean' ? 'PropTypes.bool' : 'PropTypes.any';
        componentCode += "  ".concat(prop, ": ").concat(propType, ",\n");
      });
      componentCode += "  className: PropTypes.string,\n";
      componentCode += "  children: PropTypes.node\n";
      componentCode += "};\n\n";
    }
  });
  return componentCode;
};

/**
 * Generate React App component
 */
var generateReactAppComponent = function generateReactAppComponent(components, layouts, data, options) {
  var typescript = options.typescript,
    stateManagement = options.stateManagement,
    includeAccessibility = options.includeAccessibility;
  var appCode = '// Main App Component\n';

  // State management setup
  if (stateManagement === 'useState' && data && Object.keys(data).length > 0) {
    appCode += "const App = () => {\n";
    Object.entries(data).forEach(function (_ref13) {
      var _ref14 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_ref13, 2),
        key = _ref14[0],
        value = _ref14[1];
      appCode += "  const [".concat(key, ", set").concat(pascalCase(key), "] = useState(").concat(JSON.stringify(value), ");\n");
    });
    appCode += '\n';
  } else {
    appCode += "const App = () => {\n";
  }
  appCode += "  return (\n";
  appCode += "    <AppContainer".concat(includeAccessibility ? ' role="main"' : '', ">\n");

  // Generate layout structure
  layouts.forEach(function (layout) {
    var layoutName = layout.name || layout.type || 'Layout';
    appCode += "      <".concat(pascalCase(layoutName), "Container>\n");
    if (layout.components && layout.components.length > 0) {
      layout.components.forEach(function (componentId) {
        var component = components.find(function (c) {
          return c.id === componentId;
        });
        if (component) {
          appCode += "        <".concat(component.type);
          Object.entries(component.props || {}).forEach(function (_ref15) {
            var _ref16 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_ref15, 2),
              prop = _ref16[0],
              value = _ref16[1];
            if (typeof value === 'string') {
              appCode += "\n          ".concat(prop, "=\"").concat(value, "\"");
            } else {
              appCode += "\n          ".concat(prop, "={").concat(JSON.stringify(value), "}");
            }
          });
          appCode += "\n        />\n";
        }
      });
    } else {
      appCode += "        {/* Add components here */}\n";
    }
    appCode += "      </".concat(pascalCase(layoutName), "Container>\n");
  });
  appCode += "    </AppContainer>\n";
  appCode += "  );\n";
  appCode += "};\n\n";
  appCode += "export default App;\n";
  return appCode;
};

/**
 * Utility functions
 */

/**
 * Convert kebab-case to camelCase
 * @param {string} str - The string to convert
 * @returns {string} - The camelCase string
 */
var camelCase = function camelCase(str) {
  return str.replace(/-([a-z])/g, function (g) {
    return g[1].toUpperCase();
  });
};

/**
 * Convert string to PascalCase
 */
var pascalCase = function pascalCase(str) {
  return str.replace(/(?:^|[\s-_])(\w)/g, function (match, letter) {
    return letter.toUpperCase();
  }).replace(/[\s-_]/g, '');
};

/**
 * Convert kebab-case to camelCase for CSS properties
 */
var kebabToCamelCase = function kebabToCamelCase(str) {
  return str.replace(/-([a-z])/g, function (g) {
    return g[1].toUpperCase();
  });
};

/**
 * Get appropriate ARIA role for component type
 */
var getAriaRole = function getAriaRole(type) {
  var roleMap = {
    'Button': 'button',
    'Input': 'textbox',
    'Text': 'text',
    'Image': 'img',
    'Header': 'banner',
    'Section': 'region',
    'Card': 'article',
    'List': 'list'
  };
  return roleMap[type] || 'generic';
};

/**
 * Generate component content based on type
 */
var generateComponentContent = function generateComponentContent(type, props) {
  switch (type) {
    case 'Button':
      return (props === null || props === void 0 ? void 0 : props.text) || 'Button';
    case 'Text':
      return (props === null || props === void 0 ? void 0 : props.content) || 'Text content';
    case 'Input':
      return '';
    // Self-closing
    case 'Image':
      return '';
    // Self-closing
    default:
      return (props === null || props === void 0 ? void 0 : props.content) || type;
  }
};

/**
 * Generate component styles based on type
 */
var generateComponentStyles = function generateComponentStyles(type) {
  var styleMap = {
    'Button': "\n  padding: 0.5rem 1rem;\n  border: none;\n  border-radius: 0.25rem;\n  background-color: #007bff;\n  color: white;\n  cursor: pointer;\n  font-size: 1rem;\n  transition: background-color 0.2s;\n\n  &:hover {\n    background-color: #0056b3;\n  }\n\n  &:focus {\n    outline: 2px solid #007bff;\n    outline-offset: 2px;\n  }",
    'Input': "\n  padding: 0.5rem;\n  border: 1px solid #ccc;\n  border-radius: 0.25rem;\n  font-size: 1rem;\n\n  &:focus {\n    outline: 2px solid #007bff;\n    border-color: #007bff;\n  }",
    'Text': "\n  margin: 0;\n  line-height: 1.5;",
    'Image': "\n  max-width: 100%;\n  height: auto;",
    'Card': "\n  padding: 1rem;\n  border: 1px solid #e0e0e0;\n  border-radius: 0.5rem;\n  background-color: white;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);"
  };
  return styleMap[type] || "\n  /* Add styles for ".concat(type, " */");
};

/**
 * Generate CSS modules styles
 */
var generateCSSModules = function generateCSSModules(styles) {
  var css = '';
  Object.entries(styles).forEach(function (_ref17) {
    var _ref18 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_ref17, 2),
      selector = _ref18[0],
      style = _ref18[1];
    css += "".concat(selector, " {\n");
    Object.entries(style).forEach(function (_ref19) {
      var _ref20 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_ref19, 2),
        prop = _ref20[0],
        value = _ref20[1];
      css += "  ".concat(prop, ": ").concat(value, ";\n");
    });
    css += '}\n\n';
  });
  return css;
};

/**
 * Generate full React project structure
 */
var generateReactProject = function generateReactProject(appData, options) {
  var typescript = options.typescript,
    _options$bundler = options.bundler,
    bundler = _options$bundler === void 0 ? 'vite' : _options$bundler;
  var fileExtension = typescript ? 'tsx' : 'jsx';
  var projectStructure = (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)((0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)((0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)((0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)((0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)((0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)((0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)((0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)((0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)({
    'package.json': (0,_project_generators_js__WEBPACK_IMPORTED_MODULE_4__/* .generatePackageJson */ .Qd)('react-app', options),
    'README.md': (0,_project_generators_js__WEBPACK_IMPORTED_MODULE_4__/* .generateReadme */ .uc)('React', options)
  }, "src/App.".concat(fileExtension), generateReactCode(appData, _objectSpread(_objectSpread({}, options), {}, {
    projectStructure: 'single-file'
  }))), "src/index.".concat(fileExtension), generateReactIndex(typescript)), 'src/index.css', generateGlobalStyles()), 'public/index.html', generateIndexHTML()), '.gitignore', generateGitIgnore()), '.eslintrc.json', (0,_project_generators_js__WEBPACK_IMPORTED_MODULE_4__/* .generateESLintConfig */ .rR)('react', typescript)), 'Dockerfile', (0,_project_generators_js__WEBPACK_IMPORTED_MODULE_4__/* .generateDockerfile */ .wN)('react')), 'docker-compose.yml', (0,_project_generators_js__WEBPACK_IMPORTED_MODULE_4__/* .generateDockerCompose */ .yF)()), '.github/workflows/ci.yml', (0,_project_generators_js__WEBPACK_IMPORTED_MODULE_4__/* .generateGitHubActions */ .GK)('react'));
  if (typescript) {
    projectStructure['tsconfig.json'] = (0,_project_generators_js__WEBPACK_IMPORTED_MODULE_4__/* .generateTSConfig */ .v9)();
  }
  if (bundler === 'vite') {
    projectStructure['vite.config.js'] = (0,_project_generators_js__WEBPACK_IMPORTED_MODULE_4__/* .generateViteConfig */ .AU)('react', typescript);
  }
  return projectStructure;
};

/**
 * Generate React index file
 */
var generateReactIndex = function generateReactIndex(typescript) {
  return "import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport './index.css';\nimport App from './App';\n\nconst root = ReactDOM.createRoot(\n  document.getElementById('root')".concat(typescript ? ' as HTMLElement' : '', "\n);\n\nroot.render(\n  <React.StrictMode>\n    <App />\n  </React.StrictMode>\n);");
};

/**
 * Generate global styles
 */
var generateGlobalStyles = function generateGlobalStyles() {
  return "body {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n\n* {\n  box-sizing: border-box;\n}\n\nhtml {\n  scroll-behavior: smooth;\n}\n\nbutton {\n  cursor: pointer;\n}\n\ninput, textarea, select {\n  font-family: inherit;\n}\n\nimg {\n  max-width: 100%;\n  height: auto;\n}";
};

/**
 * Generate index.html
 */
var generateIndexHTML = function generateIndexHTML() {
  return "<!DOCTYPE html>\n<html lang=\"en\">\n  <head>\n    <meta charset=\"utf-8\" />\n    <link rel=\"icon\" href=\"%PUBLIC_URL%/favicon.ico\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\" />\n    <meta name=\"theme-color\" content=\"#000000\" />\n    <meta name=\"description\" content=\"Generated by App Builder\" />\n    <link rel=\"apple-touch-icon\" href=\"%PUBLIC_URL%/logo192.png\" />\n    <link rel=\"manifest\" href=\"%PUBLIC_URL%/manifest.json\" />\n    <title>Generated App</title>\n  </head>\n  <body>\n    <noscript>You need to enable JavaScript to run this app.</noscript>\n    <div id=\"root\"></div>\n  </body>\n</html>";
};

/**
 * Generate .gitignore
 */
var generateGitIgnore = function generateGitIgnore() {
  return "# Dependencies\nnode_modules/\n/.pnp\n.pnp.js\n\n# Testing\n/coverage\n\n# Production\n/build\n/dist\n\n# Environment variables\n.env\n.env.local\n.env.development.local\n.env.test.local\n.env.production.local\n\n# Logs\nnpm-debug.log*\nyarn-debug.log*\nyarn-error.log*\nlerna-debug.log*\n\n# Runtime data\npids\n*.pid\n*.seed\n*.pid.lock\n\n# IDE\n.vscode/\n.idea/\n*.swp\n*.swo\n\n# OS\n.DS_Store\nThumbs.db\n\n# Temporary folders\ntmp/\ntemp/";
};

/***/ })

}]);