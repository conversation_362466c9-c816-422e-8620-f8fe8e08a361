import React, { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { toggleSidebar, setCurrentView, togglePreviewMode } from '../redux/minimal-store';
import { selectUISidebarAndView, selectPreviewMode, selectWebSocketStatus } from '../redux/selectors';
import ComponentBuilder from './ComponentBuilder';
import LayoutDesigner from './LayoutDesigner';
import PageBuilder from './PageBuilder';
import ThemeManager from './ThemeManager';
import SimpleWebSocket from './SimpleWebSocket';

const AppBuilderDashboard = () => {
  const dispatch = useDispatch();
  const { sidebarOpen, currentView } = useSelector(selectUISidebarAndView);
  const previewMode = useSelector(selectPreviewMode);
  const websocketConnected = useSelector(selectWebSocketStatus);

  const handleToggleSidebar = () => {
    dispatch(toggleSidebar());
  };

  const handleSetCurrentView = (view) => {
    dispatch(setCurrentView(view));
  };

  const handleTogglePreviewMode = () => {
    dispatch(togglePreviewMode());
  };

  const renderContent = () => {
    switch (currentView) {
      case 'components':
        return <ComponentBuilder />;
      case 'layouts':
        return <LayoutDesigner />;
      case 'pages':
        return <PageBuilder />;
      case 'themes':
        return <ThemeManager />;
      case 'websocket':
        return <SimpleWebSocket />;
      default:
        return <ComponentBuilder />;
    }
  };

  return (
    <div style={{
      display: 'flex',
      minHeight: 'calc(100vh - 200px)',
      backgroundColor: '#f9fafb',
      border: '1px solid #e5e7eb',
      borderRadius: '0.5rem',
      overflow: 'hidden',
      marginTop: '1rem'
    }}>
      {/* Sidebar */}
      {sidebarOpen && (
        <div style={{
          width: '250px',
          backgroundColor: '#1E293B',
          color: 'white',
          padding: '1rem'
        }}>
          <h3 style={{ marginBottom: '1rem' }}>App Builder</h3>

          <nav>
            <ul style={{ listStyle: 'none', padding: 0 }}>
              <li style={{
                padding: '0.75rem',
                backgroundColor: currentView === 'components' ? '#2563EB' : 'transparent',
                borderRadius: '0.25rem',
                marginBottom: '0.5rem',
                cursor: 'pointer'
              }} onClick={() => handleSetCurrentView('components')}>
                Components
              </li>
              <li style={{
                padding: '0.75rem',
                backgroundColor: currentView === 'layouts' ? '#2563EB' : 'transparent',
                borderRadius: '0.25rem',
                marginBottom: '0.5rem',
                cursor: 'pointer'
              }} onClick={() => handleSetCurrentView('layouts')}>
                Layouts
              </li>
              <li style={{
                padding: '0.75rem',
                backgroundColor: currentView === 'pages' ? '#2563EB' : 'transparent',
                borderRadius: '0.25rem',
                marginBottom: '0.5rem',
                cursor: 'pointer'
              }} onClick={() => handleSetCurrentView('pages')}>
                Pages
              </li>
              <li style={{
                padding: '0.75rem',
                backgroundColor: currentView === 'themes' ? '#2563EB' : 'transparent',
                borderRadius: '0.25rem',
                marginBottom: '0.5rem',
                cursor: 'pointer'
              }} onClick={() => handleSetCurrentView('themes')}>
                Themes
              </li>
              <li style={{
                padding: '0.75rem',
                backgroundColor: currentView === 'websocket' ? '#2563EB' : 'transparent',
                borderRadius: '0.25rem',
                marginBottom: '0.5rem',
                cursor: 'pointer'
              }} onClick={() => handleSetCurrentView('websocket')}>
                WebSocket
              </li>
            </ul>
          </nav>

          <div style={{ marginTop: '2rem' }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              marginBottom: '1rem'
            }}>
              <div style={{
                width: '10px',
                height: '10px',
                borderRadius: '50%',
                backgroundColor: websocketConnected ? '#10B981' : '#EF4444',
                marginRight: '0.5rem'
              }}></div>
              <span>{websocketConnected ? 'Connected' : 'Disconnected'}</span>
            </div>

            <button
              onClick={handleTogglePreviewMode}
              style={{
                width: '100%',
                padding: '0.5rem',
                backgroundColor: previewMode ? '#10B981' : '#2563EB',
                color: 'white',
                border: 'none',
                borderRadius: '0.25rem',
                cursor: 'pointer'
              }}
            >
              {previewMode ? 'Exit Preview' : 'Preview Mode'}
            </button>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div style={{ flex: 1, padding: '1rem', position: 'relative' }}>
        <button
          onClick={handleToggleSidebar}
          style={{
            position: 'absolute',
            top: '1rem',
            left: sidebarOpen ? 'auto' : '1rem',
            padding: '0.5rem',
            backgroundColor: '#1E293B',
            color: 'white',
            border: 'none',
            borderRadius: '0.25rem',
            cursor: 'pointer',
            zIndex: 10
          }}
        >
          {sidebarOpen ? '←' : '→'}
        </button>

        <div style={{ marginTop: '3rem' }}>
          {previewMode ? (
            <div style={{
              padding: '1rem',
              border: '1px solid #e5e7eb',
              borderRadius: '0.5rem',
              backgroundColor: 'white'
            }}>
              <h2>Preview Mode</h2>
              <p>This is a preview of your application. In a real implementation, this would render your actual app with the components, layouts, and themes you've created.</p>

              <div style={{ marginTop: '1rem' }}>
                <button
                  onClick={handleTogglePreviewMode}
                  style={{
                    padding: '0.5rem 1rem',
                    backgroundColor: '#6B7280',
                    color: 'white',
                    border: 'none',
                    borderRadius: '0.25rem',
                    cursor: 'pointer'
                  }}
                >
                  Exit Preview
                </button>
              </div>
            </div>
          ) : (
            renderContent()
          )}
        </div>
      </div>
    </div>
  );
};

export default AppBuilderDashboard;
