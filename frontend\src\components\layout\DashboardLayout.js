import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  SearchOutlined,
  HomeOutlined,
  AppstoreOutlined,
  LayoutOutlined,
  FileOutlined,
  BgColorsOutlined,
  SettingOutlined,
  KeyOutlined,
  BellOutlined,
  UserOutlined,
  WifiOutlined
} from '@ant-design/icons';

import { toggleSidebar, setCurrentView } from '../../redux/reducers/uiReducer';
import { selectUISidebarAndView, selectWebSocketStatus } from '../../redux/selectors';
import { styled } from '../../design-system';
import { Button, Input, Card } from '../../design-system';
import theme from '../../design-system/theme';

// Custom hook for media queries
const useMediaQuery = (query) => {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia(query);
    setMatches(mediaQuery.matches);

    const handler = (event) => setMatches(event.matches);
    mediaQuery.addEventListener('change', handler);

    return () => mediaQuery.removeEventListener('change', handler);
  }, [query]);

  return matches;
};

const DashboardContainer = styled.div`
  display: flex;
  height: 100vh;
  overflow: hidden;
`;

const Sidebar = styled.div`
  width: ${props => props.collapsed ? '80px' : '250px'};
  background-color: ${theme.colors.neutral[800]};
  color: ${theme.colors.neutral[100]};
  transition: width 0.3s ease;
  overflow-y: auto;
  overflow-x: hidden;

  @media (max-width: ${theme.breakpoints.md}) {
    position: fixed;
    z-index: 1000;
    height: 100vh;
    left: ${props => props.collapsed ? '-80px' : '0'};
    box-shadow: ${props => props.collapsed ? 'none' : '0 0 10px rgba(0,0,0,0.2)'};
  }
`;

const MainContent = styled.div`
  flex: 1;
  overflow-y: auto;
  background-color: ${theme.colors.neutral[50]};
`;

const Header = styled.header`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 ${theme.spacing[4]};
  height: 64px;
  background-color: white;
  border-bottom: 1px solid ${theme.colors.neutral[200]};
`;

const NavItem = styled.div`
  padding: ${theme.spacing[3]} ${theme.spacing[4]};
  display: flex;
  align-items: center;
  cursor: pointer;
  background-color: ${props => props.active ? theme.colors.primary.main : 'transparent'};
  color: ${props => props.active ? 'white' : theme.colors.neutral[300]};
  transition: all 0.2s ease;

  &:hover {
    background-color: ${props => props.active ? theme.colors.primary.main : theme.colors.neutral[700]};
    color: white;
  }

  .nav-icon {
    margin-right: ${props => props.collapsed ? '0' : theme.spacing[3]};
    font-size: ${theme.typography.fontSize.lg};
  }

  .nav-text {
    font-size: ${theme.typography.fontSize.md};
    font-weight: ${theme.typography.fontWeight.medium};
  }
`;

const ConnectionStatus = styled.div`
  display: flex;
  align-items: center;
  padding: ${theme.spacing[3]} ${theme.spacing[4]};
  border-top: 1px solid ${theme.colors.neutral[700]};
  margin-top: auto;

  .status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: ${props => props.connected ? theme.colors.success.main : theme.colors.error.main};
    margin-right: ${theme.spacing[2]};
  }

  .status-text {
    font-size: ${theme.typography.fontSize.sm};
    color: ${theme.colors.neutral[300]};
  }
`;

const DashboardLayout = ({ children }) => {
  const dispatch = useDispatch();
  const location = useLocation();
  const navigate = useNavigate();
  const isMobile = useMediaQuery(`(max-width: ${theme.breakpoints.md})`);

  const { sidebarOpen, currentView } = useSelector(selectUISidebarAndView);
  const websocketConnected = useSelector(selectWebSocketStatus);
  const [searchQuery, setSearchQuery] = useState('');

  // Close sidebar on mobile when navigating
  useEffect(() => {
    if (isMobile && sidebarOpen) {
      dispatch(toggleSidebar());
    }
  }, [location.pathname, isMobile]);

  const handleToggleSidebar = () => {
    dispatch(toggleSidebar());
  };

  const handleSetCurrentView = (view) => {
    dispatch(setCurrentView(view));

    // If the view is 'websocket', navigate to the dedicated page
    if (view === 'websocket') {
      navigate('/websocket');
    }
  };

  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
    // Implement search functionality
  };

  const navigationItems = [
    { key: 'components', icon: <AppstoreOutlined className="nav-icon" />, label: 'Components' },
    { key: 'layouts', icon: <LayoutOutlined className="nav-icon" />, label: 'Layouts' },
    { key: 'pages', icon: <FileOutlined className="nav-icon" />, label: 'Pages' },
    { key: 'themes', icon: <BgColorsOutlined className="nav-icon" />, label: 'Themes' },
    { key: 'websocket', icon: <WifiOutlined className="nav-icon" />, label: 'WebSocket' },
    { key: 'api-keys', icon: <KeyOutlined className="nav-icon" />, label: 'API Keys' },
  ];

  return (
    <DashboardContainer>
      <Sidebar collapsed={!sidebarOpen}>
        <div style={{ padding: theme.spacing[4], textAlign: sidebarOpen ? 'left' : 'center' }}>
          {sidebarOpen ? (
            <h2 style={{ margin: 0, color: 'white' }}>App Builder</h2>
          ) : (
            <h2 style={{ margin: 0, color: 'white' }}>AB</h2>
          )}
        </div>

        <nav>
          {navigationItems.map(item => (
            <NavItem
              key={item.key}
              active={currentView === item.key}
              collapsed={!sidebarOpen}
              onClick={() => handleSetCurrentView(item.key)}
            >
              {item.icon}
              {sidebarOpen && <span className="nav-text">{item.label}</span>}
            </NavItem>
          ))}
        </nav>

        <ConnectionStatus connected={websocketConnected}>
          <div className="status-indicator"></div>
          {sidebarOpen && (
            <span className="status-text">
              {websocketConnected ? 'Connected' : 'Disconnected'}
            </span>
          )}
        </ConnectionStatus>
      </Sidebar>

      <MainContent>
        <Header>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Button
              variant="text"
              onClick={handleToggleSidebar}
              style={{ marginRight: theme.spacing[4] }}
            >
              {sidebarOpen ? <MenuFoldOutlined /> : <MenuUnfoldOutlined />}
            </Button>

            <div style={{ width: '300px' }}>
              <Input
                placeholder="Search..."
                value={searchQuery}
                onChange={handleSearch}
                prefix={<SearchOutlined />}
                style={{ backgroundColor: theme.colors.neutral[100] }}
              />
            </div>
          </div>

          <div style={{ display: 'flex', alignItems: 'center', gap: theme.spacing[4] }}>
            <Button variant="text" style={{ padding: theme.spacing[2] }}>
              <BellOutlined style={{ fontSize: '20px' }} />
            </Button>

            <div style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
              <div style={{
                width: '32px',
                height: '32px',
                borderRadius: '50%',
                backgroundColor: theme.colors.primary.main,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white'
              }}>
                <UserOutlined />
              </div>
              {!isMobile && <span style={{ marginLeft: theme.spacing[2] }}>User</span>}
            </div>
          </div>
        </Header>

        <div style={{ padding: theme.spacing[4] }}>
          {children}
        </div>
      </MainContent>
    </DashboardContainer>
  );
};

export default DashboardLayout;
