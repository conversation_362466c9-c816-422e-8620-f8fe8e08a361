"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[1338],{

/***/ 47119:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $l: () => (/* binding */ useEnhancedDragDrop)
/* harmony export */ });
/* unused harmony exports useDragVisualFeedback, useDragReorder */
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96540);




/**
 * Enhanced drag and drop hook with visual feedback and animations
 * @param {Object} options - Configuration options
 * @param {Function} options.onDrop - Callback when item is dropped
 * @param {Function} options.onDragStart - Callback when drag starts
 * @param {Function} options.onDragEnd - Callback when drag ends
 * @param {Function} options.onDragOver - Callback when dragging over
 * @param {Function} options.onDragLeave - Callback when leaving drag area
 * @param {boolean} options.snapToGrid - Enable snap to grid functionality
 * @param {number} options.gridSize - Grid size for snapping
 * @param {boolean} options.showDropZones - Show visual drop zones
 * @param {Array} options.acceptedTypes - Accepted drag data types
 * @returns {Object} Drag and drop state and handlers
 */
var useEnhancedDragDrop = function useEnhancedDragDrop() {
  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  var onDrop = options.onDrop,
    onDragStart = options.onDragStart,
    onDragEnd = options.onDragEnd,
    onDragOver = options.onDragOver,
    onDragLeave = options.onDragLeave,
    _options$snapToGrid = options.snapToGrid,
    snapToGrid = _options$snapToGrid === void 0 ? false : _options$snapToGrid,
    _options$gridSize = options.gridSize,
    gridSize = _options$gridSize === void 0 ? 20 : _options$gridSize,
    _options$showDropZone = options.showDropZones,
    showDropZones = _options$showDropZone === void 0 ? true : _options$showDropZone,
    _options$acceptedType = options.acceptedTypes,
    acceptedTypes = _options$acceptedType === void 0 ? ['application/json'] : _options$acceptedType;

  // State
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    isDragging = _useState2[0],
    setIsDragging = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState3, 2),
    isOver = _useState4[0],
    setIsOver = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState5, 2),
    dragData = _useState6[0],
    setDragData = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({
      x: 0,
      y: 0
    }),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState7, 2),
    dropPosition = _useState8[0],
    setDropPosition = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState9, 2),
    validDropZone = _useState0[0],
    setValidDropZone = _useState0[1];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState1, 2),
    dragPreview = _useState10[0],
    setDragPreview = _useState10[1];

  // Refs
  var dropZoneRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);
  var dragPreviewRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);

  // Handle drag start
  var handleDragStart = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (e, data) {
    setIsDragging(true);
    setDragData(data);

    // Set drag data
    if (data) {
      e.dataTransfer.setData('application/json', JSON.stringify(data));
    }
    e.dataTransfer.effectAllowed = 'copy';

    // Create custom drag preview if provided
    if (dragPreviewRef.current) {
      var preview = dragPreviewRef.current.cloneNode(true);
      preview.style.position = 'absolute';
      preview.style.top = '-1000px';
      preview.style.left = '-1000px';
      preview.style.opacity = '0.8';
      preview.style.transform = 'rotate(5deg) scale(0.9)';
      preview.style.pointerEvents = 'none';
      preview.style.zIndex = '9999';
      document.body.appendChild(preview);
      e.dataTransfer.setDragImage(preview, 50, 25);

      // Clean up preview after drag
      setTimeout(function () {
        if (document.body.contains(preview)) {
          document.body.removeChild(preview);
        }
      }, 0);
    }
    if (onDragStart) {
      onDragStart(e, data);
    }
  }, [onDragStart]);

  // Handle drag end
  var handleDragEnd = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (e) {
    setIsDragging(false);
    setDragData(null);
    setDropPosition({
      x: 0,
      y: 0
    });
    setValidDropZone(true);
    if (onDragEnd) {
      onDragEnd(e);
    }
  }, [onDragEnd]);

  // Handle drag enter
  var handleDragEnter = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (e) {
    e.preventDefault();
    setIsOver(true);
  }, []);

  // Handle drag over
  var handleDragOver = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (e) {
    e.preventDefault();

    // Calculate drop position
    if (dropZoneRef.current) {
      var rect = dropZoneRef.current.getBoundingClientRect();
      var x = e.clientX - rect.left;
      var y = e.clientY - rect.top;

      // Apply snap to grid if enabled
      if (snapToGrid) {
        x = Math.round(x / gridSize) * gridSize;
        y = Math.round(y / gridSize) * gridSize;
      }
      setDropPosition({
        x: x,
        y: y
      });
    }

    // Check if drop is valid
    var dragType = e.dataTransfer.types[0];
    var isValidType = acceptedTypes.includes(dragType) || acceptedTypes.length === 0;
    setValidDropZone(isValidType);
    e.dataTransfer.dropEffect = isValidType ? 'copy' : 'none';
    if (onDragOver) {
      onDragOver(e);
    }
  }, [snapToGrid, gridSize, acceptedTypes, onDragOver]);

  // Handle drag leave
  var handleDragLeave = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (e) {
    e.preventDefault();

    // Only set isOver to false if we're actually leaving the drop zone
    if (!e.currentTarget.contains(e.relatedTarget)) {
      setIsOver(false);
      setValidDropZone(true);
      if (onDragLeave) {
        onDragLeave(e);
      }
    }
  }, [onDragLeave]);

  // Handle drop
  var handleDrop = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (e) {
    e.preventDefault();
    setIsOver(false);
    setValidDropZone(true);
    try {
      // Get dropped data
      var jsonData = e.dataTransfer.getData('application/json');
      var droppedData = null;
      if (jsonData) {
        droppedData = JSON.parse(jsonData);
      }

      // Calculate final position
      var finalPosition = dropPosition;
      if (snapToGrid) {
        finalPosition = {
          x: Math.round(dropPosition.x / gridSize) * gridSize,
          y: Math.round(dropPosition.y / gridSize) * gridSize
        };
      }
      if (onDrop) {
        onDrop(e, droppedData, finalPosition);
      }
    } catch (error) {
      console.error('Error handling drop:', error);
    }
  }, [dropPosition, snapToGrid, gridSize, onDrop]);

  // Set up event listeners
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    var dropZone = dropZoneRef.current;
    if (!dropZone) return;
    dropZone.addEventListener('dragenter', handleDragEnter);
    dropZone.addEventListener('dragover', handleDragOver);
    dropZone.addEventListener('dragleave', handleDragLeave);
    dropZone.addEventListener('drop', handleDrop);
    return function () {
      dropZone.removeEventListener('dragenter', handleDragEnter);
      dropZone.removeEventListener('dragover', handleDragOver);
      dropZone.removeEventListener('dragleave', handleDragLeave);
      dropZone.removeEventListener('drop', handleDrop);
    };
  }, [handleDragEnter, handleDragOver, handleDragLeave, handleDrop]);
  return {
    // State
    isDragging: isDragging,
    isOver: isOver,
    dragData: dragData,
    dropPosition: dropPosition,
    validDropZone: validDropZone,
    // Refs
    dropZoneRef: dropZoneRef,
    dragPreviewRef: dragPreviewRef,
    // Handlers
    handleDragStart: handleDragStart,
    handleDragEnd: handleDragEnd,
    // Utilities
    reset: function reset() {
      setIsDragging(false);
      setIsOver(false);
      setDragData(null);
      setDropPosition({
        x: 0,
        y: 0
      });
      setValidDropZone(true);
    }
  };
};

/**
 * Hook for managing drag visual feedback
 * @param {Object} options - Configuration options
 * @returns {Object} Visual feedback utilities
 */
var useDragVisualFeedback = function useDragVisualFeedback() {
  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  var _options$showGhost = options.showGhost,
    showGhost = _options$showGhost === void 0 ? true : _options$showGhost,
    _options$showDropIndi = options.showDropIndicator,
    showDropIndicator = _options$showDropIndi === void 0 ? true : _options$showDropIndi,
    _options$animationDur = options.animationDuration,
    animationDuration = _options$animationDur === void 0 ? 300 : _options$animationDur;
  var _useState11 = useState({
      x: 0,
      y: 0
    }),
    _useState12 = _slicedToArray(_useState11, 2),
    ghostPosition = _useState12[0],
    setGhostPosition = _useState12[1];
  var _useState13 = useState(false),
    _useState14 = _slicedToArray(_useState13, 2),
    showGhostElement = _useState14[0],
    setShowGhostElement = _useState14[1];
  var _useState15 = useState(null),
    _useState16 = _slicedToArray(_useState15, 2),
    dropIndicatorPosition = _useState16[0],
    setDropIndicatorPosition = _useState16[1];

  // Update ghost position during drag
  var updateGhostPosition = useCallback(function (x, y) {
    if (showGhost) {
      setGhostPosition({
        x: x,
        y: y
      });
    }
  }, [showGhost]);

  // Show/hide ghost element
  var toggleGhost = useCallback(function (show) {
    setShowGhostElement(show);
  }, []);

  // Update drop indicator
  var updateDropIndicator = useCallback(function (position) {
    if (showDropIndicator) {
      setDropIndicatorPosition(position);
    }
  }, [showDropIndicator]);

  // Clear all visual feedback
  var clearFeedback = useCallback(function () {
    setShowGhostElement(false);
    setDropIndicatorPosition(null);
    setGhostPosition({
      x: 0,
      y: 0
    });
  }, []);
  return {
    ghostPosition: ghostPosition,
    showGhostElement: showGhostElement,
    dropIndicatorPosition: dropIndicatorPosition,
    updateGhostPosition: updateGhostPosition,
    toggleGhost: toggleGhost,
    updateDropIndicator: updateDropIndicator,
    clearFeedback: clearFeedback
  };
};

/**
 * Hook for managing component reordering with drag and drop
 * @param {Array} items - Array of items to reorder
 * @param {Function} onReorder - Callback when items are reordered
 * @returns {Object} Reordering utilities
 */
var useDragReorder = function useDragReorder(items, onReorder) {
  var _useState17 = useState(null),
    _useState18 = _slicedToArray(_useState17, 2),
    draggedItem = _useState18[0],
    setDraggedItem = _useState18[1];
  var _useState19 = useState(null),
    _useState20 = _slicedToArray(_useState19, 2),
    draggedOverItem = _useState20[0],
    setDraggedOverItem = _useState20[1];
  var _useState21 = useState('after'),
    _useState22 = _slicedToArray(_useState21, 2),
    dropPosition = _useState22[0],
    setDropPosition = _useState22[1]; // 'before' or 'after'

  var handleDragStart = useCallback(function (e, item) {
    setDraggedItem(item);
    e.dataTransfer.setData('application/json', JSON.stringify(item));
    e.dataTransfer.effectAllowed = 'move';
  }, []);
  var handleDragOver = useCallback(function (e, item) {
    e.preventDefault();
    if (draggedItem && draggedItem.id !== item.id) {
      setDraggedOverItem(item);

      // Determine drop position based on mouse position
      var rect = e.currentTarget.getBoundingClientRect();
      var midpoint = rect.top + rect.height / 2;
      setDropPosition(e.clientY < midpoint ? 'before' : 'after');
    }
    e.dataTransfer.dropEffect = 'move';
  }, [draggedItem]);
  var handleDrop = useCallback(function (e, targetItem) {
    e.preventDefault();
    if (draggedItem && targetItem && draggedItem.id !== targetItem.id) {
      var draggedIndex = items.findIndex(function (item) {
        return item.id === draggedItem.id;
      });
      var targetIndex = items.findIndex(function (item) {
        return item.id === targetItem.id;
      });
      if (draggedIndex !== -1 && targetIndex !== -1) {
        var newItems = _toConsumableArray(items);
        var _newItems$splice = newItems.splice(draggedIndex, 1),
          _newItems$splice2 = _slicedToArray(_newItems$splice, 1),
          removed = _newItems$splice2[0];
        var insertIndex = dropPosition === 'before' ? targetIndex : targetIndex + 1;
        newItems.splice(insertIndex, 0, removed);
        if (onReorder) {
          onReorder(newItems);
        }
      }
    }
    setDraggedItem(null);
    setDraggedOverItem(null);
    setDropPosition('after');
  }, [items, draggedItem, dropPosition, onReorder]);
  var handleDragEnd = useCallback(function () {
    setDraggedItem(null);
    setDraggedOverItem(null);
    setDropPosition('after');
  }, []);
  return {
    draggedItem: draggedItem,
    draggedOverItem: draggedOverItem,
    dropPosition: dropPosition,
    handleDragStart: handleDragStart,
    handleDragOver: handleDragOver,
    handleDrop: handleDrop,
    handleDragEnd: handleDragEnd
  };
};
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ((/* unused pure expression or super */ null && (useEnhancedDragDrop)));

/***/ }),

/***/ 94588:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Cd: () => (/* binding */ useSelection),
/* harmony export */   EF: () => (/* binding */ useContextMenu),
/* harmony export */   KW: () => (/* binding */ useKeyboardShortcuts),
/* harmony export */   R2: () => (/* binding */ useLoadingState),
/* harmony export */   aD: () => (/* binding */ useUndoRedo),
/* harmony export */   iD: () => (/* binding */ useClipboard)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96540);


function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }


/**
 * Hook for managing undo/redo functionality
 * @param {*} initialState - Initial state value
 * @param {number} maxHistorySize - Maximum number of history entries to keep
 * @returns {Object} State and undo/redo functions
 */
var useUndoRedo = function useUndoRedo(initialState) {
  var maxHistorySize = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 50;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([initialState]),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    history = _useState2[0],
    setHistory = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState3, 2),
    currentIndex = _useState4[0],
    setCurrentIndex = _useState4[1];
  var isUndoRedoAction = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(false);

  // Get current state
  var currentState = history[currentIndex];

  // Push new state to history
  var pushState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (newState) {
    if (isUndoRedoAction.current) {
      isUndoRedoAction.current = false;
      return;
    }
    setHistory(function (prev) {
      var newHistory = prev.slice(0, currentIndex + 1);
      newHistory.push(newState);

      // Limit history size
      if (newHistory.length > maxHistorySize) {
        newHistory.shift();
        return newHistory;
      }
      return newHistory;
    });
    setCurrentIndex(function (prev) {
      var newIndex = Math.min(prev + 1, maxHistorySize - 1);
      return newIndex;
    });
  }, [currentIndex, maxHistorySize]);

  // Undo function
  var undo = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if (currentIndex > 0) {
      isUndoRedoAction.current = true;
      setCurrentIndex(function (prev) {
        return prev - 1;
      });
      return history[currentIndex - 1];
    }
    return currentState;
  }, [currentIndex, history, currentState]);

  // Redo function
  var redo = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if (currentIndex < history.length - 1) {
      isUndoRedoAction.current = true;
      setCurrentIndex(function (prev) {
        return prev + 1;
      });
      return history[currentIndex + 1];
    }
    return currentState;
  }, [currentIndex, history, currentState]);

  // Check if undo is available
  var canUndo = currentIndex > 0;

  // Check if redo is available
  var canRedo = currentIndex < history.length - 1;

  // Clear history
  var clearHistory = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    setHistory([currentState]);
    setCurrentIndex(0);
  }, [currentState]);

  // Get history info
  var getHistoryInfo = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    return {
      totalStates: history.length,
      currentIndex: currentIndex,
      canUndo: canUndo,
      canRedo: canRedo
    };
  }, [history.length, currentIndex, canUndo, canRedo]);
  return {
    state: currentState,
    pushState: pushState,
    undo: undo,
    redo: redo,
    canUndo: canUndo,
    canRedo: canRedo,
    clearHistory: clearHistory,
    getHistoryInfo: getHistoryInfo
  };
};

/**
 * Hook for keyboard shortcuts
 * @param {Object} shortcuts - Object mapping key combinations to functions
 * @param {Array} dependencies - Dependencies for the effect
 */
var useKeyboardShortcuts = function useKeyboardShortcuts(shortcuts) {
  var dependencies = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    var handleKeyDown = function handleKeyDown(event) {
      var ctrlKey = event.ctrlKey,
        metaKey = event.metaKey,
        shiftKey = event.shiftKey,
        altKey = event.altKey,
        key = event.key;

      // Create key combination string
      var modifiers = [];
      if (ctrlKey || metaKey) modifiers.push('ctrl');
      if (shiftKey) modifiers.push('shift');
      if (altKey) modifiers.push('alt');
      var combination = [].concat(modifiers, [key.toLowerCase()]).join('+');

      // Check if combination exists in shortcuts
      if (shortcuts[combination]) {
        event.preventDefault();
        shortcuts[combination](event);
      }
    };
    document.addEventListener('keydown', handleKeyDown);
    return function () {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, dependencies);
};

/**
 * Hook for managing contextual menus
 * @returns {Object} Context menu state and functions
 */
var useContextMenu = function useContextMenu() {
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({
      visible: false,
      x: 0,
      y: 0,
      items: []
    }),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState5, 2),
    contextMenu = _useState6[0],
    setContextMenu = _useState6[1];
  var showContextMenu = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (event, items) {
    event.preventDefault();
    setContextMenu({
      visible: true,
      x: event.clientX,
      y: event.clientY,
      items: items || []
    });
  }, []);
  var hideContextMenu = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    setContextMenu(function (prev) {
      return _objectSpread(_objectSpread({}, prev), {}, {
        visible: false
      });
    });
  }, []);

  // Hide context menu when clicking outside
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    var handleClick = function handleClick() {
      if (contextMenu.visible) {
        hideContextMenu();
      }
    };
    document.addEventListener('click', handleClick);
    return function () {
      document.removeEventListener('click', handleClick);
    };
  }, [contextMenu.visible, hideContextMenu]);
  return {
    contextMenu: contextMenu,
    showContextMenu: showContextMenu,
    hideContextMenu: hideContextMenu
  };
};

/**
 * Hook for managing loading states with debouncing
 * @param {number} delay - Delay before showing loading state
 * @returns {Object} Loading state and functions
 */
var useLoadingState = function useLoadingState() {
  var delay = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 200;
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState7, 2),
    isLoading = _useState8[0],
    setIsLoading = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(''),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState9, 2),
    loadingMessage = _useState0[0],
    setLoadingMessage = _useState0[1];
  var timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);
  var startLoading = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    var message = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'Loading...';
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = setTimeout(function () {
      setIsLoading(true);
      setLoadingMessage(message);
    }, delay);
  }, [delay]);
  var stopLoading = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    setIsLoading(false);
    setLoadingMessage('');
  }, []);

  // Cleanup timeout on unmount
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    return function () {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);
  return {
    isLoading: isLoading,
    loadingMessage: loadingMessage,
    startLoading: startLoading,
    stopLoading: stopLoading
  };
};

/**
 * Hook for managing component selection with multi-select support
 * @param {Array} items - Array of selectable items
 * @returns {Object} Selection state and functions
 */
var useSelection = function useSelection() {
  var items = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(new Set()),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState1, 2),
    selectedItems = _useState10[0],
    setSelectedItems = _useState10[1];
  var _useState11 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(-1),
    _useState12 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState11, 2),
    lastSelectedIndex = _useState12[0],
    setLastSelectedIndex = _useState12[1];
  var selectItem = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (item) {
    var multiSelect = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
    var itemIndex = items.findIndex(function (i) {
      return i.id === item.id;
    });
    setSelectedItems(function (prev) {
      var newSelection = new Set(multiSelect ? prev : []);
      if (newSelection.has(item.id)) {
        newSelection["delete"](item.id);
      } else {
        newSelection.add(item.id);
      }
      return newSelection;
    });
    setLastSelectedIndex(itemIndex);
  }, [items]);
  var selectRange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (item) {
    var itemIndex = items.findIndex(function (i) {
      return i.id === item.id;
    });
    if (lastSelectedIndex !== -1) {
      var start = Math.min(lastSelectedIndex, itemIndex);
      var end = Math.max(lastSelectedIndex, itemIndex);
      setSelectedItems(function (prev) {
        var newSelection = new Set(prev);
        for (var i = start; i <= end; i++) {
          if (items[i]) {
            newSelection.add(items[i].id);
          }
        }
        return newSelection;
      });
    } else {
      selectItem(item);
    }
  }, [items, lastSelectedIndex, selectItem]);
  var selectAll = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    setSelectedItems(new Set(items.map(function (item) {
      return item.id;
    })));
  }, [items]);
  var clearSelection = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    setSelectedItems(new Set());
    setLastSelectedIndex(-1);
  }, []);
  var isSelected = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (itemId) {
    return selectedItems.has(itemId);
  }, [selectedItems]);
  var getSelectedItems = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    return items.filter(function (item) {
      return selectedItems.has(item.id);
    });
  }, [items, selectedItems]);
  return {
    selectedItems: Array.from(selectedItems),
    selectItem: selectItem,
    selectRange: selectRange,
    selectAll: selectAll,
    clearSelection: clearSelection,
    isSelected: isSelected,
    getSelectedItems: getSelectedItems,
    selectedCount: selectedItems.size
  };
};

/**
 * Hook for managing clipboard operations
 * @returns {Object} Clipboard functions
 */
var useClipboard = function useClipboard() {
  var _useState13 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null),
    _useState14 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState13, 2),
    clipboardData = _useState14[0],
    setClipboardData = _useState14[1];
  var copy = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (data) {
    setClipboardData(data);

    // Also copy to system clipboard if possible
    if (navigator.clipboard && typeof data === 'string') {
      navigator.clipboard.writeText(data)["catch"](console.error);
    }
  }, []);
  var paste = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    return clipboardData;
  }, [clipboardData]);
  var clear = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    setClipboardData(null);
  }, []);
  var hasData = clipboardData !== null;
  return {
    copy: copy,
    paste: paste,
    clear: clear,
    hasData: hasData,
    data: clipboardData
  };
};
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ((/* unused pure expression or super */ null && (useUndoRedo)));

/***/ })

}]);