"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[5505],{

/***/ 95505:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": () => (/* binding */ enhanced_LayoutDesigner)
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(60436);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(64467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(5544);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js + 1 modules
var objectWithoutProperties = __webpack_require__(53986);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js
var taggedTemplateLiteral = __webpack_require__(57528);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
// EXTERNAL MODULE: ./node_modules/react-redux/dist/react-redux.mjs
var react_redux = __webpack_require__(71468);
// EXTERNAL MODULE: ./node_modules/reselect/es/index.js + 1 modules
var es = __webpack_require__(22325);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/index.js + 1565 modules
var icons_es = __webpack_require__(36031);
// EXTERNAL MODULE: ./node_modules/antd/es/index.js + 112 modules
var antd_es = __webpack_require__(33966);
;// ./src/components/enhanced/BasicLayoutDesigner.js





var Title = antd_es/* Typography */.o5.Title,
  Paragraph = antd_es/* Typography */.o5.Paragraph;
var Option = antd_es/* Select */.l6.Option;

/**
 * Basic Layout Designer - Fallback component
 * Simple layout designer that always works
 */
var BasicLayoutDesigner = function BasicLayoutDesigner() {
  var _useState = (0,react.useState)([{
      id: '1',
      name: 'Header Layout',
      type: 'flex',
      description: 'Simple header with navigation',
      items: ['Header', 'Navigation', 'Content']
    }, {
      id: '2',
      name: 'Sidebar Layout',
      type: 'grid',
      description: 'Layout with sidebar and main content',
      items: ['Sidebar', 'Main Content', 'Footer']
    }, {
      id: '3',
      name: 'Card Grid',
      type: 'grid',
      description: 'Responsive card grid layout',
      items: ['Card 1', 'Card 2', 'Card 3', 'Card 4']
    }]),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    layouts = _useState2[0],
    setLayouts = _useState2[1];
  var _useState3 = (0,react.useState)(''),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    newLayoutName = _useState4[0],
    setNewLayoutName = _useState4[1];
  var _useState5 = (0,react.useState)('grid'),
    _useState6 = (0,slicedToArray/* default */.A)(_useState5, 2),
    newLayoutType = _useState6[0],
    setNewLayoutType = _useState6[1];
  var _useState7 = (0,react.useState)(null),
    _useState8 = (0,slicedToArray/* default */.A)(_useState7, 2),
    selectedLayout = _useState8[0],
    setSelectedLayout = _useState8[1];
  var handleAddLayout = function handleAddLayout() {
    if (!newLayoutName.trim()) return;
    var newLayout = {
      id: Date.now().toString(),
      name: newLayoutName.trim(),
      type: newLayoutType,
      description: 'Custom layout',
      items: ['Item 1', 'Item 2']
    };
    setLayouts([].concat((0,toConsumableArray/* default */.A)(layouts), [newLayout]));
    setNewLayoutName('');
    setNewLayoutType('grid');
  };
  var handleDeleteLayout = function handleDeleteLayout(id) {
    setLayouts(layouts.filter(function (layout) {
      return layout.id !== id;
    }));
    if (selectedLayout && selectedLayout.id === id) {
      setSelectedLayout(null);
    }
  };
  var handleSelectLayout = function handleSelectLayout(layout) {
    setSelectedLayout(layout);
  };
  return /*#__PURE__*/react.createElement("div", {
    style: {
      padding: '20px'
    }
  }, /*#__PURE__*/react.createElement(Title, {
    level: 3
  }, "Layout Designer"), /*#__PURE__*/react.createElement(Paragraph, null, "Create and manage layout templates for your application. This is a basic version of the layout designer."), /*#__PURE__*/react.createElement(antd_es/* Alert */.Fc, {
    message: "Basic Layout Designer",
    description: "This is a simplified version of the layout designer. It provides basic layout management functionality.",
    type: "info",
    showIcon: true,
    style: {
      marginBottom: '24px'
    }
  }), /*#__PURE__*/react.createElement(antd_es/* Row */.fI, {
    gutter: [24, 24]
  }, /*#__PURE__*/react.createElement(antd_es/* Col */.fv, {
    xs: 24,
    lg: 12
  }, /*#__PURE__*/react.createElement(antd_es/* Card */.Zp, {
    title: "Create New Layout",
    style: {
      marginBottom: '24px'
    }
  }, /*#__PURE__*/react.createElement(antd_es/* Space */.$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react.createElement(antd_es/* Input */.pd, {
    placeholder: "Layout name",
    value: newLayoutName,
    onChange: function onChange(e) {
      return setNewLayoutName(e.target.value);
    },
    prefix: /*#__PURE__*/react.createElement(icons_es/* LayoutOutlined */.hy2, null)
  }), /*#__PURE__*/react.createElement(antd_es/* Select */.l6, {
    value: newLayoutType,
    onChange: setNewLayoutType,
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react.createElement(Option, {
    value: "grid"
  }, "Grid Layout"), /*#__PURE__*/react.createElement(Option, {
    value: "flex"
  }, "Flex Layout"), /*#__PURE__*/react.createElement(Option, {
    value: "custom"
  }, "Custom Layout")), /*#__PURE__*/react.createElement(antd_es/* Button */.$n, {
    type: "primary",
    icon: /*#__PURE__*/react.createElement(icons_es/* PlusOutlined */.bW0, null),
    onClick: handleAddLayout,
    disabled: !newLayoutName.trim(),
    block: true
  }, "Add Layout"))), /*#__PURE__*/react.createElement(antd_es/* Card */.Zp, {
    title: "Layout Library"
  }, /*#__PURE__*/react.createElement(antd_es/* Space */.$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, layouts.map(function (layout) {
    return /*#__PURE__*/react.createElement(antd_es/* Card */.Zp, {
      key: layout.id,
      size: "small",
      style: {
        cursor: 'pointer',
        border: (selectedLayout === null || selectedLayout === void 0 ? void 0 : selectedLayout.id) === layout.id ? '2px solid #1890ff' : '1px solid #d9d9d9'
      },
      onClick: function onClick() {
        return handleSelectLayout(layout);
      },
      actions: [/*#__PURE__*/react.createElement(icons_es/* EditOutlined */.xjh, {
        key: "edit",
        onClick: function onClick(e) {
          e.stopPropagation();
          handleSelectLayout(layout);
        }
      }), /*#__PURE__*/react.createElement(icons_es/* DeleteOutlined */.SUY, {
        key: "delete",
        onClick: function onClick(e) {
          e.stopPropagation();
          handleDeleteLayout(layout.id);
        }
      })]
    }, /*#__PURE__*/react.createElement(antd_es/* Card */.Zp.Meta, {
      title: layout.name,
      description: /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement("div", null, "Type: ", layout.type), /*#__PURE__*/react.createElement("div", null, layout.description), /*#__PURE__*/react.createElement("div", null, "Items: ", layout.items.join(', ')))
    }));
  })))), /*#__PURE__*/react.createElement(antd_es/* Col */.fv, {
    xs: 24,
    lg: 12
  }, /*#__PURE__*/react.createElement(antd_es/* Card */.Zp, {
    title: "Layout Preview"
  }, selectedLayout ? /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(Title, {
    level: 4
  }, selectedLayout.name), /*#__PURE__*/react.createElement(Paragraph, null, selectedLayout.description), /*#__PURE__*/react.createElement("div", {
    style: {
      border: '2px dashed #d9d9d9',
      borderRadius: '8px',
      padding: '20px',
      minHeight: '300px',
      background: '#fafafa'
    }
  }, /*#__PURE__*/react.createElement("div", {
    style: {
      display: selectedLayout.type === 'flex' ? 'flex' : 'grid',
      gridTemplateColumns: selectedLayout.type === 'grid' ? 'repeat(auto-fit, minmax(150px, 1fr))' : 'none',
      gap: '16px',
      height: '100%'
    }
  }, selectedLayout.items.map(function (item, index) {
    return /*#__PURE__*/react.createElement("div", {
      key: index,
      style: {
        background: 'white',
        border: '1px solid #d9d9d9',
        borderRadius: '4px',
        padding: '16px',
        textAlign: 'center',
        minHeight: '80px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }
    }, item);
  }))), /*#__PURE__*/react.createElement("div", {
    style: {
      marginTop: '16px'
    }
  }, /*#__PURE__*/react.createElement(antd_es/* Button */.$n, {
    type: "primary",
    icon: /*#__PURE__*/react.createElement(icons_es/* SaveOutlined */.ylI, null)
  }, "Save Layout"))) : /*#__PURE__*/react.createElement("div", {
    style: {
      textAlign: 'center',
      padding: '60px 20px',
      color: '#999'
    }
  }, /*#__PURE__*/react.createElement(icons_es/* LayoutOutlined */.hy2, {
    style: {
      fontSize: '48px',
      marginBottom: '16px'
    }
  }), /*#__PURE__*/react.createElement("div", null, "Select a layout to preview"))))));
};
/* harmony default export */ const enhanced_BasicLayoutDesigner = (BasicLayoutDesigner);
;// ./src/components/enhanced/LayoutDesigner.js





var _excluded = ["children"],
  _excluded2 = ["children"],
  _excluded3 = ["children"],
  _excluded4 = ["children"],
  _excluded5 = ["children"],
  _excluded6 = ["children"],
  _excluded7 = ["children"],
  _excluded8 = ["children"],
  _excluded9 = ["children"],
  _excluded0 = ["children"];
var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6, _templateObject7, _templateObject8, _templateObject9, _templateObject0;
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }





// Import fallback component


// Try to import Redux actions with fallback
var addLayout, updateLayout, removeLayout;
try {
  var actions = __webpack_require__(34816);
  addLayout = actions.addLayout;
  updateLayout = actions.updateLayout;
  removeLayout = actions.removeLayout;
} catch (error) {
  console.warn('Redux actions not available, using fallback');
  addLayout = function addLayout() {
    return {
      type: 'ADD_LAYOUT'
    };
  };
  updateLayout = function updateLayout() {
    return {
      type: 'UPDATE_LAYOUT'
    };
  };
  removeLayout = function removeLayout() {
    return {
      type: 'REMOVE_LAYOUT'
    };
  };
}

// Try to import design system with fallback
var styled, Button, Card, Input, Select, theme;
var hasDesignSystem = true;
try {
  var designSystem = __webpack_require__(79146);
  styled = designSystem.styled;
  Button = designSystem.Button;
  Card = designSystem.Card;
  Input = designSystem.Input;
  Select = designSystem.Select;
  theme = (__webpack_require__(86020)/* ["default"] */ .Ay);
} catch (error) {
  console.warn('Design system not available, using fallback');
  hasDesignSystem = false;
  // Provide fallback theme
  theme = {
    spacing: ['0', '4px', '8px', '12px', '16px', '20px', '24px', '32px', '48px'],
    colors: {
      primary: {
        main: '#1976d2',
        light: '#e3f2fd'
      },
      neutral: {
        100: '#f5f5f5',
        300: '#e0e0e0',
        400: '#bdbdbd',
        500: '#9e9e9e'
      },
      danger: {
        main: '#d32f2f'
      }
    },
    borderRadius: {
      md: '4px'
    },
    typography: {
      fontWeight: {
        medium: 500,
        semibold: 600
      },
      fontSize: {
        sm: '14px'
      }
    }
  };
}

// Define styled components with fallback support
var LayoutDesignerContainer, LayoutGrid, LayoutCanvas, LayoutItem, ComponentPalette, PaletteItem, PropertyEditor, PropertyGroup, GridControls, EmptyState;
if (hasDesignSystem && styled) {
  LayoutDesignerContainer = styled.div(_templateObject || (_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n    display: flex;\n    flex-direction: column;\n    gap: ", ";\n  "])), theme.spacing[4]);
  LayoutGrid = styled.div(_templateObject2 || (_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n    display: grid;\n    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n    gap: ", ";\n  "])), theme.spacing[4]);
  LayoutCanvas = styled.div(_templateObject3 || (_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n    display: grid;\n    grid-template-columns: repeat(12, 1fr);\n    grid-template-rows: repeat(12, 40px);\n    gap: ", ";\n    background-color: ", ";\n    border: 1px dashed ", ";\n    border-radius: ", ";\n    padding: ", ";\n    min-height: 500px;\n  "])), theme.spacing[2], theme.colors.neutral[100], theme.colors.neutral[300], theme.borderRadius.md, theme.spacing[4]);
  LayoutItem = styled.div(_templateObject4 || (_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n    grid-column: span ", ";\n    grid-row: span ", ";\n    background-color: ", ";\n    border: 1px solid ", ";\n    border-radius: ", ";\n    padding: ", ";\n    display: flex;\n    flex-direction: column;\n    justify-content: space-between;\n    cursor: ", ";\n    opacity: ", ";\n    box-shadow: ", ";\n  "])), function (props) {
    return props.width || 3;
  }, function (props) {
    return props.height || 2;
  }, function (props) {
    return props.isPlaceholder ? theme.colors.primary.light : 'white';
  }, function (props) {
    return props.isSelected ? theme.colors.primary.main : theme.colors.neutral[300];
  }, theme.borderRadius.md, theme.spacing[2], function (props) {
    return props.isDragging ? 'grabbing' : 'grab';
  }, function (props) {
    return props.isDragging ? 0.5 : 1;
  }, function (props) {
    return props.isSelected ? "0 0 0 2px ".concat(theme.colors.primary.main) : 'none';
  });
  ComponentPalette = styled.div(_templateObject5 || (_templateObject5 = (0,taggedTemplateLiteral/* default */.A)(["\n    display: flex;\n    flex-wrap: wrap;\n    gap: ", ";\n    margin-bottom: ", ";\n  "])), theme.spacing[2], theme.spacing[4]);
  PaletteItem = styled.div(_templateObject6 || (_templateObject6 = (0,taggedTemplateLiteral/* default */.A)(["\n    padding: ", ";\n    background-color: white;\n    border: 1px solid ", ";\n    border-radius: ", ";\n    cursor: grab;\n    display: flex;\n    align-items: center;\n    gap: ", ";\n\n    &:hover {\n      background-color: ", ";\n    }\n  "])), theme.spacing[2], theme.colors.neutral[300], theme.borderRadius.md, theme.spacing[2], theme.colors.neutral[100]);
  PropertyEditor = styled.div(_templateObject7 || (_templateObject7 = (0,taggedTemplateLiteral/* default */.A)(["\n    display: flex;\n    flex-direction: column;\n    gap: ", ";\n  "])), theme.spacing[3]);
  PropertyGroup = styled.div(_templateObject8 || (_templateObject8 = (0,taggedTemplateLiteral/* default */.A)(["\n    display: flex;\n    flex-direction: column;\n    gap: ", ";\n  "])), theme.spacing[2]);
  GridControls = styled.div(_templateObject9 || (_templateObject9 = (0,taggedTemplateLiteral/* default */.A)(["\n    display: flex;\n    gap: ", ";\n    margin-bottom: ", ";\n  "])), theme.spacing[2], theme.spacing[4]);
  EmptyState = styled.div(_templateObject0 || (_templateObject0 = (0,taggedTemplateLiteral/* default */.A)(["\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    padding: ", ";\n    background-color: ", ";\n    border-radius: ", ";\n    text-align: center;\n  "])), theme.spacing[8], theme.colors.neutral[100], theme.borderRadius.md);
} else {
  // Fallback to regular div components
  LayoutDesignerContainer = function LayoutDesignerContainer(_ref) {
    var children = _ref.children,
      props = (0,objectWithoutProperties/* default */.A)(_ref, _excluded);
    return /*#__PURE__*/react.createElement("div", props, children);
  };
  LayoutGrid = function LayoutGrid(_ref2) {
    var children = _ref2.children,
      props = (0,objectWithoutProperties/* default */.A)(_ref2, _excluded2);
    return /*#__PURE__*/react.createElement("div", props, children);
  };
  LayoutCanvas = function LayoutCanvas(_ref3) {
    var children = _ref3.children,
      props = (0,objectWithoutProperties/* default */.A)(_ref3, _excluded3);
    return /*#__PURE__*/react.createElement("div", props, children);
  };
  LayoutItem = function LayoutItem(_ref4) {
    var children = _ref4.children,
      props = (0,objectWithoutProperties/* default */.A)(_ref4, _excluded4);
    return /*#__PURE__*/react.createElement("div", props, children);
  };
  ComponentPalette = function ComponentPalette(_ref5) {
    var children = _ref5.children,
      props = (0,objectWithoutProperties/* default */.A)(_ref5, _excluded5);
    return /*#__PURE__*/react.createElement("div", props, children);
  };
  PaletteItem = function PaletteItem(_ref6) {
    var children = _ref6.children,
      props = (0,objectWithoutProperties/* default */.A)(_ref6, _excluded6);
    return /*#__PURE__*/react.createElement("div", props, children);
  };
  PropertyEditor = function PropertyEditor(_ref7) {
    var children = _ref7.children,
      props = (0,objectWithoutProperties/* default */.A)(_ref7, _excluded7);
    return /*#__PURE__*/react.createElement("div", props, children);
  };
  PropertyGroup = function PropertyGroup(_ref8) {
    var children = _ref8.children,
      props = (0,objectWithoutProperties/* default */.A)(_ref8, _excluded8);
    return /*#__PURE__*/react.createElement("div", props, children);
  };
  GridControls = function GridControls(_ref9) {
    var children = _ref9.children,
      props = (0,objectWithoutProperties/* default */.A)(_ref9, _excluded9);
    return /*#__PURE__*/react.createElement("div", props, children);
  };
  EmptyState = function EmptyState(_ref0) {
    var children = _ref0.children,
      props = (0,objectWithoutProperties/* default */.A)(_ref0, _excluded0);
    return /*#__PURE__*/react.createElement("div", props, children);
  };
}

// Component Item with drag-and-drop functionality
var DraggableComponent = function DraggableComponent(_ref1) {
  var component = _ref1.component,
    index = _ref1.index,
    onSelect = _ref1.onSelect,
    isSelected = _ref1.isSelected,
    onRemove = _ref1.onRemove,
    onDragStart = _ref1.onDragStart,
    onDragEnd = _ref1.onDragEnd;
  var _useState = (0,react.useState)(false),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    isDragging = _useState2[0],
    setIsDragging = _useState2[1];
  var _useState3 = (0,react.useState)({
      x: component.x || 0,
      y: component.y || 0
    }),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    position = _useState4[0],
    setPosition = _useState4[1];

  // Handle drag start
  var handleDragStart = function handleDragStart(e) {
    setIsDragging(true);
    if (onDragStart) onDragStart(component);

    // Store the initial mouse position and component position
    var initialMousePos = {
      x: e.clientX,
      y: e.clientY
    };
    var initialCompPos = {
      x: position.x,
      y: position.y
    };

    // Handle mouse move
    var handleMouseMove = function handleMouseMove(e) {
      var dx = e.clientX - initialMousePos.x;
      var dy = e.clientY - initialMousePos.y;

      // Calculate new position (this would be grid-based in a real implementation)
      var newX = Math.max(0, initialCompPos.x + Math.round(dx / 50));
      var newY = Math.max(0, initialCompPos.y + Math.round(dy / 40));
      setPosition({
        x: newX,
        y: newY
      });
    };

    // Handle mouse up
    var _handleMouseUp = function handleMouseUp() {
      setIsDragging(false);
      if (onDragEnd) onDragEnd(component, position);

      // Remove event listeners
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', _handleMouseUp);
    };

    // Add event listeners
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', _handleMouseUp);
  };
  return /*#__PURE__*/react.createElement(LayoutItem, {
    isDragging: isDragging,
    isSelected: isSelected,
    width: component.width,
    height: component.height,
    style: {
      gridColumn: "".concat(position.x + 1, " / span ").concat(component.width),
      gridRow: "".concat(position.y + 1, " / span ").concat(component.height),
      cursor: isDragging ? 'grabbing' : 'grab',
      position: 'relative',
      zIndex: isDragging ? 10 : 1
    },
    onClick: function onClick(e) {
      e.stopPropagation();
      onSelect(component);
    },
    onMouseDown: handleDragStart
  }, /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement("div", {
    style: {
      fontWeight: theme.typography.fontWeight.semibold
    }
  }, component.name), /*#__PURE__*/react.createElement("div", {
    style: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.neutral[500]
    }
  }, component.type)), /*#__PURE__*/react.createElement("div", {
    style: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center'
    }
  }, /*#__PURE__*/react.createElement("div", {
    style: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.neutral[500]
    }
  }, component.width, "x", component.height), /*#__PURE__*/react.createElement("div", {
    style: {
      display: 'flex',
      gap: '4px'
    }
  }, /*#__PURE__*/react.createElement(icons_es/* DragOutlined */.duJ, {
    style: {
      cursor: 'grab'
    }
  }), /*#__PURE__*/react.createElement(icons_es/* DeleteOutlined */.SUY, {
    style: {
      cursor: 'pointer',
      color: theme.colors.danger.main
    },
    onClick: function onClick(e) {
      e.stopPropagation();
      if (onRemove) onRemove(component.id);
    }
  }))));
};
var DropTarget = function DropTarget(_ref10) {
  var onDrop = _ref10.onDrop,
    children = _ref10.children;
  var _useState5 = (0,react.useState)(false),
    _useState6 = (0,slicedToArray/* default */.A)(_useState5, 2),
    isOver = _useState6[0],
    setIsOver = _useState6[1];
  var dropRef = react.useRef(null);

  // Handle drag over
  var handleDragOver = function handleDragOver(e) {
    e.preventDefault();
    setIsOver(true);
  };

  // Handle drag leave
  var handleDragLeave = function handleDragLeave() {
    setIsOver(false);
  };

  // Handle drop
  var handleDrop = function handleDrop(e) {
    e.preventDefault();
    setIsOver(false);

    // Get drop position relative to the drop target
    var rect = dropRef.current.getBoundingClientRect();
    var x = e.clientX - rect.left;
    var y = e.clientY - rect.top;

    // Calculate grid position
    var gridX = Math.floor(x / 50);
    var gridY = Math.floor(y / 40);

    // Call the onDrop callback with the component data and position
    if (onDrop) {
      try {
        var data = JSON.parse(e.dataTransfer.getData('application/json'));
        onDrop(data, {
          x: gridX,
          y: gridY
        });
      } catch (error) {
        console.error('Error parsing drag data:', error);
      }
    }
  };

  // Handle click on the canvas (deselect items)
  var handleCanvasClick = function handleCanvasClick() {
    // This would typically call a function to deselect any selected items
  };
  return /*#__PURE__*/react.createElement("div", {
    ref: dropRef,
    style: {
      position: 'relative',
      height: '100%'
    },
    onDragOver: handleDragOver,
    onDragLeave: handleDragLeave,
    onDrop: handleDrop,
    onClick: handleCanvasClick
  }, children, isOver && /*#__PURE__*/react.createElement("div", {
    style: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(37, 99, 235, 0.1)',
      border: "2px dashed ".concat(theme.colors.primary.main),
      borderRadius: theme.borderRadius.md,
      zIndex: 10
    }
  }));
};

// Memoized selectors to prevent unnecessary re-renders
var getAppState = function getAppState(state) {
  return state.app || {};
};
var getAppDataState = function getAppDataState(state) {
  return state.appData || {};
};
var selectLayoutComponents = (0,es/* createSelector */.Mz)([getAppState, getAppDataState], function (app, appData) {
  return app.components || appData.components || [];
});
var selectLayoutLayouts = (0,es/* createSelector */.Mz)([getAppState, getAppDataState], function (app, appData) {
  return app.layouts || appData.layouts || [];
});

// Main wrapper component with error handling
var LayoutDesigner = function LayoutDesigner() {
  // Check if we have all required dependencies
  var hasRequiredDeps = hasDesignSystem && styled && Button && Card && Input && Select && theme;
  if (!hasRequiredDeps) {
    console.log('Using basic layout designer due to missing dependencies');
    return /*#__PURE__*/react.createElement(enhanced_BasicLayoutDesigner, null);
  }

  // If we have all dependencies, use the enhanced version
  return /*#__PURE__*/react.createElement(EnhancedLayoutDesigner, null);
};
var EnhancedLayoutDesigner = function EnhancedLayoutDesigner() {
  var dispatch = (0,react_redux/* useDispatch */.wA)();

  // Use memoized selectors to prevent unnecessary re-renders
  var components = (0,react_redux/* useSelector */.d4)(selectLayoutComponents);
  var layouts = (0,react_redux/* useSelector */.d4)(selectLayoutLayouts);
  var _useState7 = (0,react.useState)(''),
    _useState8 = (0,slicedToArray/* default */.A)(_useState7, 2),
    layoutName = _useState8[0],
    setLayoutName = _useState8[1];
  var _useState9 = (0,react.useState)('grid'),
    _useState0 = (0,slicedToArray/* default */.A)(_useState9, 2),
    layoutType = _useState0[0],
    setLayoutType = _useState0[1];
  var _useState1 = (0,react.useState)([]),
    _useState10 = (0,slicedToArray/* default */.A)(_useState1, 2),
    layoutItems = _useState10[0],
    setLayoutItems = _useState10[1];
  var _useState11 = (0,react.useState)(null),
    _useState12 = (0,slicedToArray/* default */.A)(_useState11, 2),
    selectedLayout = _useState12[0],
    setSelectedLayout = _useState12[1];
  var _useState13 = (0,react.useState)(null),
    _useState14 = (0,slicedToArray/* default */.A)(_useState13, 2),
    selectedItem = _useState14[0],
    setSelectedItem = _useState14[1];
  var _useState15 = (0,react.useState)(false),
    _useState16 = (0,slicedToArray/* default */.A)(_useState15, 2),
    editMode = _useState16[0],
    setEditMode = _useState16[1];
  var _useState17 = (0,react.useState)({}),
    _useState18 = (0,slicedToArray/* default */.A)(_useState17, 2),
    errors = _useState18[0],
    setErrors = _useState18[1];

  // Item properties
  var _useState19 = (0,react.useState)(3),
    _useState20 = (0,slicedToArray/* default */.A)(_useState19, 2),
    itemWidth = _useState20[0],
    setItemWidth = _useState20[1];
  var _useState21 = (0,react.useState)(2),
    _useState22 = (0,slicedToArray/* default */.A)(_useState21, 2),
    itemHeight = _useState22[0],
    setItemHeight = _useState22[1];
  var layoutTypes = [{
    value: 'grid',
    label: 'Grid Layout',
    description: 'Responsive grid system with columns'
  }, {
    value: 'flex',
    label: 'Flex Layout',
    description: 'Flexible box layout for dynamic content'
  }, {
    value: 'stack',
    label: 'Stack Layout',
    description: 'Vertical stacking of components'
  }, {
    value: 'masonry',
    label: 'Masonry Layout',
    description: 'Pinterest-style masonry grid'
  }, {
    value: 'sidebar',
    label: 'Sidebar Layout',
    description: 'Main content with sidebar'
  }, {
    value: 'hero',
    label: 'Hero Layout',
    description: 'Large hero section with content below'
  }, {
    value: 'custom',
    label: 'Custom Layout',
    description: 'Custom positioning and styling'
  }];

  // Layout tools and controls
  var layoutTools = [{
    id: 'align-left',
    label: 'Align Left',
    icon: 'AlignLeftOutlined'
  }, {
    id: 'align-center',
    label: 'Align Center',
    icon: 'AlignCenterOutlined'
  }, {
    id: 'align-right',
    label: 'Align Right',
    icon: 'AlignRightOutlined'
  }, {
    id: 'distribute-horizontal',
    label: 'Distribute Horizontally',
    icon: 'ColumnWidthOutlined'
  }, {
    id: 'distribute-vertical',
    label: 'Distribute Vertically',
    icon: 'ColumnHeightOutlined'
  }, {
    id: 'group',
    label: 'Group Items',
    icon: 'GroupOutlined'
  }, {
    id: 'ungroup',
    label: 'Ungroup Items',
    icon: 'UngroupOutlined'
  }, {
    id: 'bring-forward',
    label: 'Bring Forward',
    icon: 'VerticalAlignTopOutlined'
  }, {
    id: 'send-backward',
    label: 'Send Backward',
    icon: 'VerticalAlignBottomOutlined'
  }];

  // Grid settings
  var _useState23 = (0,react.useState)({
      columns: 12,
      gap: '16px',
      padding: '16px',
      responsive: true
    }),
    _useState24 = (0,slicedToArray/* default */.A)(_useState23, 2),
    gridSettings = _useState24[0],
    setGridSettings = _useState24[1];

  // Responsive breakpoints
  var _useState25 = (0,react.useState)({
      xs: 576,
      sm: 768,
      md: 992,
      lg: 1200,
      xl: 1400
    }),
    _useState26 = (0,slicedToArray/* default */.A)(_useState25, 2),
    breakpoints = _useState26[0],
    setBreakpoints = _useState26[1];

  // Layout tool handlers
  var handleLayoutTool = function handleLayoutTool(toolId) {
    if (!selectedItem) {
      if (typeof message !== 'undefined') {
        message.warning('Please select an item first');
      }
      return;
    }
    switch (toolId) {
      case 'align-left':
        updateItemPosition(selectedItem.id, {
          x: 1
        });
        break;
      case 'align-center':
        updateItemPosition(selectedItem.id, {
          x: Math.floor(gridSettings.columns / 2)
        });
        break;
      case 'align-right':
        updateItemPosition(selectedItem.id, {
          x: gridSettings.columns - selectedItem.width
        });
        break;
      case 'bring-forward':
        updateItemZIndex(selectedItem.id, 1);
        break;
      case 'send-backward':
        updateItemZIndex(selectedItem.id, -1);
        break;
      default:
        console.log('Tool not implemented:', toolId);
    }
  };

  // Helper functions for layout tools
  var updateItemPosition = function updateItemPosition(itemId, position) {
    var updatedItems = layoutItems.map(function (item) {
      if (item.id === itemId) {
        return _objectSpread(_objectSpread({}, item), position);
      }
      return item;
    });
    setLayoutItems(updatedItems);
  };
  var updateItemZIndex = function updateItemZIndex(itemId, change) {
    var updatedItems = layoutItems.map(function (item) {
      if (item.id === itemId) {
        return _objectSpread(_objectSpread({}, item), {}, {
          zIndex: (item.zIndex || 0) + change
        });
      }
      return item;
    });
    setLayoutItems(updatedItems);
  };
  var validateForm = function validateForm() {
    var newErrors = {};
    if (!layoutName.trim()) {
      newErrors.name = 'Layout name is required';
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  var handleAddLayout = function handleAddLayout() {
    if (!validateForm()) return;
    try {
      var newLayout = {
        id: Date.now().toString(),
        name: layoutName.trim(),
        type: layoutType,
        items: layoutItems,
        settings: {
          columns: layoutType === 'grid' ? 12 : 1,
          gap: '16px',
          padding: '16px',
          responsive: true,
          breakpoints: {
            xs: 576,
            sm: 768,
            md: 992,
            lg: 1200,
            xl: 1400
          }
        },
        createdAt: new Date().toISOString()
      };
      dispatch(addLayout(newLayout));

      // Reset form
      setLayoutName('');
      setLayoutType('grid');
      setLayoutItems([]);
      setErrors({});

      // Show success message
      if (typeof message !== 'undefined') {
        message.success("Layout \"".concat(newLayout.name, "\" created successfully"));
      }
      console.log('Layout added successfully:', newLayout);
    } catch (error) {
      console.error('Error adding layout:', error);
      setErrors({
        submit: 'Failed to add layout'
      });

      // Show error message
      if (typeof message !== 'undefined') {
        message.error('Failed to create layout');
      }
    }
  };
  var handleUpdateLayout = function handleUpdateLayout() {
    if (!selectedLayout || !validateForm()) return;
    try {
      var updatedLayout = _objectSpread(_objectSpread({}, selectedLayout), {}, {
        name: layoutName.trim(),
        type: layoutType,
        items: layoutItems,
        updatedAt: new Date().toISOString()
      });
      dispatch(updateLayout(updatedLayout.id, updatedLayout));

      // Reset form and exit edit mode
      setLayoutName('');
      setLayoutType('grid');
      setLayoutItems([]);
      setSelectedLayout(null);
      setEditMode(false);
      setErrors({});
      console.log('Layout updated successfully:', updatedLayout);
    } catch (error) {
      console.error('Error updating layout:', error);
      setErrors({
        submit: 'Failed to update layout'
      });
    }
  };
  var handleRemoveLayout = function handleRemoveLayout(id) {
    try {
      dispatch(removeLayout(id));

      // If the removed layout was selected, reset the form
      if (selectedLayout && selectedLayout.id === id) {
        setLayoutName('');
        setLayoutType('grid');
        setLayoutItems([]);
        setSelectedLayout(null);
        setEditMode(false);
      }
      console.log('Layout removed successfully:', id);
    } catch (error) {
      console.error('Error removing layout:', error);
    }
  };
  var handleSelectLayout = function handleSelectLayout(layout) {
    setSelectedLayout(layout);
    setLayoutName(layout.name);
    setLayoutType(layout.type);
    setLayoutItems(layout.items || []);
    setEditMode(true);
    setErrors({});
  };
  var handleCancelEdit = function handleCancelEdit() {
    setLayoutName('');
    setLayoutType('grid');
    setLayoutItems([]);
    setSelectedLayout(null);
    setEditMode(false);
    setErrors({});
  };
  var handleDuplicateLayout = function handleDuplicateLayout(layout) {
    var duplicatedLayout = _objectSpread(_objectSpread({}, layout), {}, {
      id: Date.now().toString(),
      name: "".concat(layout.name, " (Copy)"),
      createdAt: new Date().toISOString()
    });
    dispatch(addLayout(duplicatedLayout));
  };
  var handleDrop = function handleDrop(item, position) {
    // Find the component from the components list
    var component = components.find(function (c) {
      return c.id === item.id;
    });
    if (component) {
      // Calculate grid position based on drop coordinates
      // This is a simplified calculation - in a real app, you'd need more precise positioning
      var gridX = Math.floor(Math.random() * 10) + 1; // Random position for demo
      var gridY = Math.floor(Math.random() * 10) + 1; // Random position for demo

      var newItem = {
        id: Date.now().toString(),
        componentId: component.id,
        name: component.name,
        type: component.type,
        x: gridX,
        y: gridY,
        width: itemWidth,
        height: itemHeight
      };
      setLayoutItems([].concat((0,toConsumableArray/* default */.A)(layoutItems), [newItem]));
    }
  };
  var handleSelectItem = function handleSelectItem(item) {
    setSelectedItem(item);
    setItemWidth(item.width);
    setItemHeight(item.height);
  };
  var handleUpdateItem = function handleUpdateItem() {
    if (!selectedItem) return;
    var updatedItems = layoutItems.map(function (item) {
      if (item.id === selectedItem.id) {
        return _objectSpread(_objectSpread({}, item), {}, {
          width: itemWidth,
          height: itemHeight
        });
      }
      return item;
    });
    setLayoutItems(updatedItems);
    setSelectedItem(null);
  };
  var handleRemoveItem = function handleRemoveItem(itemId) {
    setLayoutItems(layoutItems.filter(function (item) {
      return item.id !== itemId;
    }));
    if (selectedItem && selectedItem.id === itemId) {
      setSelectedItem(null);
    }
  };
  return /*#__PURE__*/react.createElement(LayoutDesignerContainer, null, /*#__PURE__*/react.createElement(Card, null, /*#__PURE__*/react.createElement(Card.Header, null, /*#__PURE__*/react.createElement(Card.Title, null, editMode ? 'Edit Layout' : 'Create Layout'), editMode && /*#__PURE__*/react.createElement(Button, {
    variant: "text",
    size: "small",
    onClick: handleCancelEdit,
    startIcon: /*#__PURE__*/react.createElement(icons_es/* CloseOutlined */.r$3, null)
  }, "Cancel")), /*#__PURE__*/react.createElement(Card.Content, null, /*#__PURE__*/react.createElement(PropertyEditor, null, /*#__PURE__*/react.createElement(PropertyGroup, null, /*#__PURE__*/react.createElement(Input, {
    label: "Layout Name",
    value: layoutName,
    onChange: function onChange(e) {
      return setLayoutName(e.target.value);
    },
    placeholder: "Enter layout name",
    fullWidth: true,
    error: !!errors.name,
    helperText: errors.name
  })), /*#__PURE__*/react.createElement(PropertyGroup, null, /*#__PURE__*/react.createElement(Select, {
    label: "Layout Type",
    value: layoutType,
    onChange: function onChange(e) {
      return setLayoutType(e.target.value);
    },
    options: layoutTypes,
    fullWidth: true
  })))), /*#__PURE__*/react.createElement(Card.Footer, null, editMode ? /*#__PURE__*/react.createElement(Button, {
    variant: "primary",
    onClick: handleUpdateLayout,
    startIcon: /*#__PURE__*/react.createElement(icons_es/* SaveOutlined */.ylI, null)
  }, "Update Layout") : /*#__PURE__*/react.createElement(Button, {
    variant: "primary",
    onClick: handleAddLayout,
    startIcon: /*#__PURE__*/react.createElement(icons_es/* PlusOutlined */.bW0, null)
  }, "Add Layout"))), /*#__PURE__*/react.createElement(Card, null, /*#__PURE__*/react.createElement(Card.Header, null, /*#__PURE__*/react.createElement(Card.Title, null, "Layout Designer")), /*#__PURE__*/react.createElement(Card.Content, null, /*#__PURE__*/react.createElement("div", {
    style: {
      marginBottom: theme.spacing[4],
      padding: theme.spacing[3],
      backgroundColor: theme.colors.neutral[50],
      borderRadius: theme.borderRadius.md,
      border: "1px solid ".concat(theme.colors.neutral[200])
    }
  }, /*#__PURE__*/react.createElement("div", {
    style: {
      marginBottom: theme.spacing[2],
      fontWeight: theme.typography.fontWeight.semibold,
      color: theme.colors.neutral[700]
    }
  }, "Layout Tools"), /*#__PURE__*/react.createElement("div", {
    style: {
      display: 'flex',
      flexWrap: 'wrap',
      gap: theme.spacing[1]
    }
  }, layoutTools.map(function (tool) {
    return /*#__PURE__*/react.createElement(Button, {
      key: tool.id,
      variant: "text",
      size: "small",
      onClick: function onClick() {
        return handleLayoutTool(tool.id);
      },
      disabled: !selectedItem && ['align-left', 'align-center', 'align-right', 'bring-forward', 'send-backward'].includes(tool.id),
      style: {
        minWidth: 'auto',
        padding: "".concat(theme.spacing[1], " ").concat(theme.spacing[2]),
        fontSize: theme.typography.fontSize.xs
      },
      title: tool.label
    }, tool.label);
  })), selectedItem && /*#__PURE__*/react.createElement("div", {
    style: {
      marginTop: theme.spacing[2],
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.neutral[600]
    }
  }, "Selected: ", selectedItem.name, " (", selectedItem.width, "\xD7", selectedItem.height, ")")), /*#__PURE__*/react.createElement(ComponentPalette, null, /*#__PURE__*/react.createElement("div", {
    style: {
      marginRight: theme.spacing[4],
      fontWeight: theme.typography.fontWeight.medium
    }
  }, "Component Palette:"), components.map(function (component) {
    return /*#__PURE__*/react.createElement(PaletteItem, {
      key: component.id,
      draggable: true,
      onDragStart: function onDragStart(e) {
        // Set the drag data
        e.dataTransfer.setData('application/json', JSON.stringify({
          id: component.id,
          name: component.name,
          type: component.type
        }));
        e.dataTransfer.effectAllowed = 'copy';
      }
    }, /*#__PURE__*/react.createElement(icons_es/* DragOutlined */.duJ, null), /*#__PURE__*/react.createElement("span", null, component.name));
  }), components.length === 0 && /*#__PURE__*/react.createElement("div", {
    style: {
      color: theme.colors.neutral[500]
    }
  }, "No components available. Create components first.")), /*#__PURE__*/react.createElement(GridControls, null, /*#__PURE__*/react.createElement("div", {
    style: {
      display: 'flex',
      alignItems: 'center',
      gap: theme.spacing[2]
    }
  }, /*#__PURE__*/react.createElement(icons_es/* ColumnWidthOutlined */.x18, null), /*#__PURE__*/react.createElement(Input, {
    label: "Width",
    type: "number",
    min: 1,
    max: 12,
    value: itemWidth,
    onChange: function onChange(e) {
      return setItemWidth(parseInt(e.target.value, 10));
    },
    style: {
      width: '80px'
    }
  })), /*#__PURE__*/react.createElement("div", {
    style: {
      display: 'flex',
      alignItems: 'center',
      gap: theme.spacing[2]
    }
  }, /*#__PURE__*/react.createElement(icons_es/* ColumnHeightOutlined */.oco, null), /*#__PURE__*/react.createElement(Input, {
    label: "Height",
    type: "number",
    min: 1,
    max: 12,
    value: itemHeight,
    onChange: function onChange(e) {
      return setItemHeight(parseInt(e.target.value, 10));
    },
    style: {
      width: '80px'
    }
  })), selectedItem && /*#__PURE__*/react.createElement(Button, {
    variant: "primary",
    size: "small",
    onClick: handleUpdateItem
  }, "Apply")), /*#__PURE__*/react.createElement(DropTarget, {
    onDrop: handleDrop
  }, /*#__PURE__*/react.createElement(LayoutCanvas, null, layoutItems.map(function (item, index) {
    return /*#__PURE__*/react.createElement(DraggableComponent, {
      key: item.id,
      component: item,
      index: index,
      onSelect: function onSelect() {
        return handleSelectItem(item);
      },
      isSelected: selectedItem && selectedItem.id === item.id,
      onRemove: handleRemoveItem,
      onDragEnd: function onDragEnd(component, position) {
        // Update the component position in the layout
        var updatedItems = layoutItems.map(function (layoutItem) {
          if (layoutItem.id === component.id) {
            return _objectSpread(_objectSpread({}, layoutItem), {}, {
              x: position.x,
              y: position.y
            });
          }
          return layoutItem;
        });
        setLayoutItems(updatedItems);
      }
    });
  }))))), /*#__PURE__*/react.createElement(Card, null, /*#__PURE__*/react.createElement(Card.Header, null, /*#__PURE__*/react.createElement(Card.Title, null, "Saved Layouts")), /*#__PURE__*/react.createElement(Card.Content, null, layouts.length === 0 ? /*#__PURE__*/react.createElement(EmptyState, null, /*#__PURE__*/react.createElement("div", {
    style: {
      fontSize: '48px',
      color: theme.colors.neutral[400],
      marginBottom: theme.spacing[4]
    }
  }, /*#__PURE__*/react.createElement(icons_es/* BorderOutlined */.bnM, null)), /*#__PURE__*/react.createElement("h3", null, "No Layouts Yet"), /*#__PURE__*/react.createElement("p", null, "Create your first layout to get started")) : /*#__PURE__*/react.createElement(LayoutGrid, null, layouts.map(function (layout) {
    var _layout$items;
    return /*#__PURE__*/react.createElement(Card, {
      key: layout.id,
      elevation: "sm"
    }, /*#__PURE__*/react.createElement(Card.Header, null, /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement("div", {
      style: {
        fontWeight: theme.typography.fontWeight.semibold
      }
    }, layout.name), /*#__PURE__*/react.createElement("div", {
      style: {
        fontSize: theme.typography.fontSize.sm,
        color: theme.colors.neutral[500]
      }
    }, layout.type)), /*#__PURE__*/react.createElement("div", {
      style: {
        display: 'flex',
        gap: theme.spacing[1]
      }
    }, /*#__PURE__*/react.createElement(Button, {
      variant: "text",
      size: "small",
      onClick: function onClick() {
        return handleDuplicateLayout(layout);
      }
    }, /*#__PURE__*/react.createElement(icons_es/* CopyOutlined */.wq3, null)), /*#__PURE__*/react.createElement(Button, {
      variant: "text",
      size: "small",
      onClick: function onClick() {
        return handleSelectLayout(layout);
      }
    }, /*#__PURE__*/react.createElement(icons_es/* EditOutlined */.xjh, null)), /*#__PURE__*/react.createElement(Button, {
      variant: "text",
      size: "small",
      onClick: function onClick() {
        return handleRemoveLayout(layout.id);
      }
    }, /*#__PURE__*/react.createElement(icons_es/* DeleteOutlined */.SUY, null)))), /*#__PURE__*/react.createElement(Card.Content, {
      onClick: function onClick() {
        return handleSelectLayout(layout);
      }
    }, /*#__PURE__*/react.createElement("div", {
      style: {
        height: '150px',
        backgroundColor: theme.colors.neutral[100],
        borderRadius: theme.borderRadius.md,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        cursor: 'pointer'
      }
    }, /*#__PURE__*/react.createElement(icons_es/* EyeOutlined */.Om2, {
      style: {
        fontSize: '24px',
        color: theme.colors.neutral[400]
      }
    }))), /*#__PURE__*/react.createElement(Card.Footer, null, /*#__PURE__*/react.createElement("div", {
      style: {
        fontSize: theme.typography.fontSize.sm,
        color: theme.colors.neutral[500]
      }
    }, ((_layout$items = layout.items) === null || _layout$items === void 0 ? void 0 : _layout$items.length) || 0, " components")));
  })))));
};
/* harmony default export */ const enhanced_LayoutDesigner = (LayoutDesigner);

/***/ })

}]);