import React from 'react';
import { useSelector } from 'react-redux';

import DashboardLayout from '../layout/DashboardLayout';
import EnhancedComponentBuilder from './ComponentBuilder';
import EnhancedLayoutDesigner from './LayoutDesigner';
import EnhancedThemeManager from './ThemeManager';
import FixedWebSocketManager from './FixedWebSocketManager';
import { selectUICurrentViewAndPreview } from '../../redux/selectors';
import { styled } from '../../design-system';
import theme from '../../design-system/theme';

const ContentContainer = styled.div`
  padding: ${theme.spacing[4]};
`;

const EnhancedAppBuilderDashboard = () => {
  const { currentView, previewMode } = useSelector(selectUICurrentViewAndPreview);

  const renderContent = () => {
    if (previewMode) {
      return (
        <div style={{
          padding: theme.spacing[4],
          border: `1px solid ${theme.colors.neutral[200]}`,
          borderRadius: theme.borderRadius.md,
          backgroundColor: 'white'
        }}>
          <h2>Preview Mode</h2>
          <p>This is a preview of your application. In a real implementation, this would render your actual app with the components, layouts, and themes you've created.</p>
        </div>
      );
    }

    switch (currentView) {
      case 'components':
        return <EnhancedComponentBuilder />;
      case 'layouts':
        return <EnhancedLayoutDesigner />;
      case 'themes':
        return <EnhancedThemeManager />;
      case 'websocket':
        return <FixedWebSocketManager />;
      default:
        return <EnhancedComponentBuilder />;
    }
  };

  return (
    <DashboardLayout>
      <ContentContainer>
        {renderContent()}
      </ContentContainer>
    </DashboardLayout>
  );
};

export default EnhancedAppBuilderDashboard;
