import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
    Row, Col, Button, Input, Form, message, Card, Divider, Select,
    Typography, Space, List, Tag, Modal, Switch, Tabs, Tooltip,
    Popconfirm, Drawer, Alert
} from 'antd';
import {
    PlusOutlined, SaveOutlined, EyeOutlined, DownloadOutlined,
    EditOutlined, DeleteOutlined, SettingOutlined, PlayCircleOutlined,
    CodeOutlined, BugOutlined
} from '@ant-design/icons';
import * as actions from '../redux/actions';
import { selectAppComponentsAndLayouts, selectWebSocketStatus } from '../redux/selectors';
import webSocketService from '../services/WebSocketService';
import './AppBuilderMVP.css';

const { Title, Text } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;

/**
 * AppBuilderMVP component
 * Enhanced MVP interface for the app builder functionality
 * Updated to fix chunk loading issues
 */
const AppBuilderMVP = () => {
    const dispatch = useDispatch();
    const { components, layouts } = useSelector(selectAppComponentsAndLayouts);
    const wsConnected = useSelector(selectWebSocketStatus);

    // Component creation state
    const [componentName, setComponentName] = useState('');
    const [componentType, setComponentType] = useState('Button');
    const [componentProps, setComponentProps] = useState({});

    // Layout state
    const [layoutType, setLayoutType] = useState('Grid');

    // UI state
    const [activeTab, setActiveTab] = useState('builder');
    const [previewMode, setPreviewMode] = useState(false);
    const [selectedComponent, setSelectedComponent] = useState(null);
    const [editModalVisible, setEditModalVisible] = useState(false);
    const [settingsDrawerVisible, setSettingsDrawerVisible] = useState(false);

    // App state
    const [appName, setAppName] = useState('My App');
    const [appDescription, setAppDescription] = useState('Built with App Builder MVP');

    // Component types with enhanced properties
    const componentTypes = [
        {
            value: 'Button',
            label: 'Button',
            icon: '🔘',
            defaultProps: { text: 'Click me', type: 'primary', size: 'medium' }
        },
        {
            value: 'Text',
            label: 'Text',
            icon: '📝',
            defaultProps: { content: 'Sample text', size: 'medium', color: '#000000' }
        },
        {
            value: 'Input',
            label: 'Input Field',
            icon: '📝',
            defaultProps: { placeholder: 'Enter text...', type: 'text', required: false }
        },
        {
            value: 'Image',
            label: 'Image',
            icon: '🖼️',
            defaultProps: { src: 'https://via.placeholder.com/150', alt: 'Image', width: 150 }
        },
        {
            value: 'Card',
            label: 'Card',
            icon: '🃏',
            defaultProps: { title: 'Card Title', content: 'Card content goes here' }
        },
        {
            value: 'List',
            label: 'List',
            icon: '📋',
            defaultProps: { items: ['Item 1', 'Item 2', 'Item 3'], type: 'unordered' }
        }
    ];

    // Layout types with enhanced properties
    const layoutTypes = [
        {
            value: 'Grid',
            label: 'Grid Layout',
            icon: '⚏',
            defaultProps: { columns: 3, gap: '16px', responsive: true }
        },
        {
            value: 'Flex',
            label: 'Flex Layout',
            icon: '↔️',
            defaultProps: { direction: 'row', justify: 'center', align: 'center' }
        },
        {
            value: 'Stack',
            label: 'Stack Layout',
            icon: '📚',
            defaultProps: { spacing: '16px', align: 'center', direction: 'vertical' }
        }
    ];

    // Update component props when type changes
    useEffect(() => {
        const selectedType = componentTypes.find(type => type.value === componentType);
        if (selectedType) {
            setComponentProps(selectedType.defaultProps);
        }
    }, [componentType]);

    // Display WebSocket connection status
    useEffect(() => {
        if (wsConnected) {
            message.success('Real-time connection established');
        }
    }, [wsConnected]);

    // Handle adding a new component
    const handleAddComponent = () => {
        if (!componentName.trim()) {
            message.error('Please enter a component name');
            return;
        }

        const selectedType = componentTypes.find(type => type.value === componentType);
        const newComponent = {
            id: `${componentType.toLowerCase()}-${Date.now()}`,
            type: componentType,
            name: componentName,
            props: {
                ...selectedType.defaultProps,
                ...componentProps,
                name: componentName
            },
            created: new Date().toISOString()
        };

        dispatch(actions.addComponent(newComponent));
        setComponentName('');
        message.success(`Added ${componentType} component: ${componentName}`);
    };

    // Handle editing a component
    const handleEditComponent = (component) => {
        setSelectedComponent(component);
        setEditModalVisible(true);
    };

    // Handle deleting a component
    const handleDeleteComponent = (componentId) => {
        dispatch(actions.deleteComponent(componentId));
        message.success('Component deleted');
    };

    // Handle adding a new layout
    const handleAddLayout = () => {
        const selectedLayout = layoutTypes.find(type => type.value === layoutType);
        const newLayout = {
            id: `${layoutType.toLowerCase()}-${Date.now()}`,
            type: layoutType,
            components: [],
            props: selectedLayout.defaultProps,
            created: new Date().toISOString()
        };

        dispatch(actions.addLayout(newLayout.type, newLayout.components, newLayout.props));
        message.success(`Added ${layoutType} layout`);
    };

    // Handle saving the app
    const handleSave = () => {
        const appData = {
            name: appName,
            description: appDescription,
            components,
            layouts,
            styles: {},
            data: {},
            metadata: {
                version: '1.0.0',
                created: new Date().toISOString(),
                componentCount: components.length,
                layoutCount: layouts.length
            }
        };

        dispatch(actions.saveAppData(appData));
        message.success('App data saved successfully');
    };

    // Handle exporting the app
    const handleExport = () => {
        const exportData = {
            name: appName,
            description: appDescription,
            components,
            layouts,
            exportedAt: new Date().toISOString()
        };

        const dataStr = JSON.stringify(exportData, null, 2);
        const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);

        const exportFileDefaultName = `${appName.replace(/\s+/g, '-').toLowerCase()}-app.json`;

        const linkElement = document.createElement('a');
        linkElement.setAttribute('href', dataUri);
        linkElement.setAttribute('download', exportFileDefaultName);
        linkElement.click();

        message.success('App exported successfully');
    };

    // Handle preview mode toggle
    const handlePreviewToggle = () => {
        setPreviewMode(!previewMode);
        message.info(previewMode ? 'Exited preview mode' : 'Entered preview mode');
    };

    return (
        <div className="app-builder-mvp">
            {/* Header */}
            <Card
                title={
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <div>
                            <Title level={3} style={{ margin: 0 }}>
                                {appName} - App Builder MVP
                            </Title>
                            <Text type="secondary">{appDescription}</Text>
                        </div>
                        <div className="connection-status">
                            {wsConnected ? (
                                <Tag color="green" icon="●">Real-time connected</Tag>
                            ) : (
                                <Tag color="orange" icon="●">HTTP fallback</Tag>
                            )}
                        </div>
                    </div>
                }
                bordered={false}
                extra={
                    <Space>
                        <Tooltip title="App Settings">
                            <Button
                                icon={<SettingOutlined />}
                                onClick={() => setSettingsDrawerVisible(true)}
                            />
                        </Tooltip>
                        <Tooltip title={previewMode ? "Exit Preview" : "Preview App"}>
                            <Button
                                icon={<EyeOutlined />}
                                type={previewMode ? "primary" : "default"}
                                onClick={handlePreviewToggle}
                            />
                        </Tooltip>
                        <Tooltip title="Save App">
                            <Button
                                icon={<SaveOutlined />}
                                type="primary"
                                onClick={handleSave}
                            />
                        </Tooltip>
                        <Tooltip title="Export App">
                            <Button
                                icon={<DownloadOutlined />}
                                onClick={handleExport}
                            />
                        </Tooltip>
                    </Space>
                }
            >
                {/* Main Content */}
                <Tabs activeKey={activeTab} onChange={setActiveTab}>
                    <TabPane tab={<span><PlusOutlined />Builder</span>} key="builder">
                        {/* Component Builder Section */}
                        <Card title="Add Components" size="small" style={{ marginBottom: 16 }}>
                            <Row gutter={[16, 16]}>
                                <Col span={8}>
                                    <Input
                                        placeholder="Component Name"
                                        value={componentName}
                                        onChange={(e) => setComponentName(e.target.value)}
                                        onPressEnter={handleAddComponent}
                                    />
                                </Col>
                                <Col span={8}>
                                    <Select
                                        value={componentType}
                                        onChange={setComponentType}
                                        style={{ width: '100%' }}
                                        placeholder="Select component type"
                                    >
                                        {componentTypes.map(type => (
                                            <Option key={type.value} value={type.value}>
                                                {type.icon} {type.label}
                                            </Option>
                                        ))}
                                    </Select>
                                </Col>
                                <Col span={8}>
                                    <Button
                                        type="primary"
                                        icon={<PlusOutlined />}
                                        onClick={handleAddComponent}
                                        block
                                    >
                                        Add Component
                                    </Button>
                                </Col>
                            </Row>
                        </Card>

                        {/* Layout Builder Section */}
                        <Card title="Add Layouts" size="small" style={{ marginBottom: 16 }}>
                            <Row gutter={[16, 16]}>
                                <Col span={16}>
                                    <Select
                                        value={layoutType}
                                        onChange={setLayoutType}
                                        style={{ width: '100%' }}
                                        placeholder="Select layout type"
                                    >
                                        {layoutTypes.map(type => (
                                            <Option key={type.value} value={type.value}>
                                                {type.icon} {type.label}
                                            </Option>
                                        ))}
                                    </Select>
                                </Col>
                                <Col span={8}>
                                    <Button
                                        type="primary"
                                        icon={<PlusOutlined />}
                                        onClick={handleAddLayout}
                                        block
                                    >
                                        Add Layout
                                    </Button>
                                </Col>
                            </Row>
                        </Card>
                    </TabPane>

                    <TabPane tab={<span><CodeOutlined />Components</span>} key="components">
                        <Card title={`Components (${components.length})`} size="small">
                            {components.length === 0 ? (
                                <Alert
                                    message="No components added yet"
                                    description="Start building your app by adding components in the Builder tab."
                                    type="info"
                                    showIcon
                                />
                            ) : (
                                <List
                                    dataSource={components}
                                    renderItem={(component, index) => (
                                        <List.Item
                                            key={component.id || `component-${index}`}
                                            actions={[
                                                <Tooltip title="Edit">
                                                    <Button
                                                        icon={<EditOutlined />}
                                                        size="small"
                                                        onClick={() => handleEditComponent(component)}
                                                    />
                                                </Tooltip>,
                                                <Popconfirm
                                                    title="Delete this component?"
                                                    onConfirm={() => handleDeleteComponent(component.id)}
                                                    okText="Yes"
                                                    cancelText="No"
                                                >
                                                    <Tooltip title="Delete">
                                                        <Button
                                                            icon={<DeleteOutlined />}
                                                            size="small"
                                                            danger
                                                        />
                                                    </Tooltip>
                                                </Popconfirm>
                                            ]}
                                        >
                                            <List.Item.Meta
                                                title={
                                                    <Space>
                                                        <Tag color="blue">{component.type}</Tag>
                                                        <Text strong>{component.name || component.props?.name}</Text>
                                                    </Space>
                                                }
                                                description={
                                                    <Text type="secondary">
                                                        {JSON.stringify(component.props, null, 2).substring(0, 100)}
                                                        {JSON.stringify(component.props).length > 100 ? '...' : ''}
                                                    </Text>
                                                }
                                            />
                                        </List.Item>
                                    )}
                                />
                            )}
                        </Card>
                    </TabPane>

                    <TabPane tab={<span><SettingOutlined />Layouts</span>} key="layouts">
                        <Card title={`Layouts (${layouts.length})`} size="small">
                            {layouts.length === 0 ? (
                                <Alert
                                    message="No layouts added yet"
                                    description="Add layouts to organize your components in the Builder tab."
                                    type="info"
                                    showIcon
                                />
                            ) : (
                                <List
                                    dataSource={layouts}
                                    renderItem={(layout, index) => (
                                        <List.Item
                                            key={layout.id || `layout-${index}`}
                                            actions={[
                                                <Tooltip title="Edit">
                                                    <Button icon={<EditOutlined />} size="small" />
                                                </Tooltip>,
                                                <Popconfirm
                                                    title="Delete this layout?"
                                                    onConfirm={() => {
                                                        message.success('Layout deleted');
                                                    }}
                                                    okText="Yes"
                                                    cancelText="No"
                                                >
                                                    <Tooltip title="Delete">
                                                        <Button icon={<DeleteOutlined />} size="small" danger />
                                                    </Tooltip>
                                                </Popconfirm>
                                            ]}
                                        >
                                            <List.Item.Meta
                                                title={
                                                    <Space>
                                                        <Tag color="green">{layout.type}</Tag>
                                                        <Text strong>Layout {index + 1}</Text>
                                                    </Space>
                                                }
                                                description={
                                                    <Text type="secondary">
                                                        {JSON.stringify(layout.styles || layout.props, null, 2).substring(0, 100)}
                                                        {JSON.stringify(layout.styles || layout.props).length > 100 ? '...' : ''}
                                                    </Text>
                                                }
                                            />
                                        </List.Item>
                                    )}
                                />
                            )}
                        </Card>
                    </TabPane>

                    <TabPane tab={<span><PlayCircleOutlined />Preview</span>} key="preview">
                        <Card title="App Preview" size="small">
                            <Alert
                                message="Preview Mode"
                                description="This is a simplified preview of your app. In a full implementation, this would render your actual components and layouts."
                                type="info"
                                showIcon
                                style={{ marginBottom: 16 }}
                            />

                            <div style={{
                                border: '2px dashed #d9d9d9',
                                borderRadius: '6px',
                                padding: '24px',
                                textAlign: 'center',
                                minHeight: '300px',
                                backgroundColor: '#fafafa'
                            }}>
                                <Title level={4}>Your App Preview</Title>
                                <Text type="secondary">
                                    Components: {components.length} | Layouts: {layouts.length}
                                </Text>

                                {components.length > 0 && (
                                    <div style={{ marginTop: 16 }}>
                                        <Text strong>Components in your app:</Text>
                                        <div style={{ marginTop: 8 }}>
                                            {components.map((comp, index) => (
                                                <Tag key={index} style={{ margin: 4 }}>
                                                    {comp.type}: {comp.name || comp.props?.name}
                                                </Tag>
                                            ))}
                                        </div>
                                    </div>
                                )}

                                {layouts.length > 0 && (
                                    <div style={{ marginTop: 16 }}>
                                        <Text strong>Layouts in your app:</Text>
                                        <div style={{ marginTop: 8 }}>
                                            {layouts.map((layout, index) => (
                                                <Tag key={index} color="green" style={{ margin: 4 }}>
                                                    {layout.type} Layout
                                                </Tag>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>
                        </Card>
                    </TabPane>
                </Tabs>
            </Card>
        </div>
    );
};

export default AppBuilderMVP;
