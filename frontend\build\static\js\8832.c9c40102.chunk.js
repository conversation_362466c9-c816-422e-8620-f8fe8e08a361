"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[8832],{

/***/ 18832:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": () => (/* binding */ pages_AppBuilderMVP)
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(64467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(5544);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
// EXTERNAL MODULE: ./node_modules/react-redux/dist/react-redux.mjs
var react_redux = __webpack_require__(71468);
// EXTERNAL MODULE: ./node_modules/antd/es/index.js + 112 modules
var es = __webpack_require__(33966);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/index.js + 1565 modules
var icons_es = __webpack_require__(36031);
// EXTERNAL MODULE: ./src/redux/actions.js
var actions = __webpack_require__(81616);
// EXTERNAL MODULE: ./src/redux/selectors.js
var selectors = __webpack_require__(52725);
// EXTERNAL MODULE: ./src/services/WebSocketService.js
var WebSocketService = __webpack_require__(17053);
;// ./src/pages/AppBuilderMVP.css
// extracted by mini-css-extract-plugin

;// ./src/pages/AppBuilderMVP.js


function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }








var Title = es/* Typography */.o5.Title,
  Text = es/* Typography */.o5.Text;
var Option = es/* Select */.l6.Option;
var TabPane = es/* Tabs */.tU.TabPane;

/**
 * AppBuilderMVP component
 * Enhanced MVP interface for the app builder functionality
 * Updated to fix chunk loading issues
 */
var AppBuilderMVP = function AppBuilderMVP() {
  var dispatch = (0,react_redux/* useDispatch */.wA)();
  var _useSelector = (0,react_redux/* useSelector */.d4)(selectors/* selectAppComponentsAndLayouts */.Mp),
    components = _useSelector.components,
    layouts = _useSelector.layouts;
  var wsConnected = (0,react_redux/* useSelector */.d4)(selectors/* selectWebSocketStatus */.AU);

  // Component creation state
  var _useState = (0,react.useState)(''),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    componentName = _useState2[0],
    setComponentName = _useState2[1];
  var _useState3 = (0,react.useState)('Button'),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    componentType = _useState4[0],
    setComponentType = _useState4[1];
  var _useState5 = (0,react.useState)({}),
    _useState6 = (0,slicedToArray/* default */.A)(_useState5, 2),
    componentProps = _useState6[0],
    setComponentProps = _useState6[1];

  // Layout state
  var _useState7 = (0,react.useState)('Grid'),
    _useState8 = (0,slicedToArray/* default */.A)(_useState7, 2),
    layoutType = _useState8[0],
    setLayoutType = _useState8[1];

  // UI state
  var _useState9 = (0,react.useState)('builder'),
    _useState0 = (0,slicedToArray/* default */.A)(_useState9, 2),
    activeTab = _useState0[0],
    setActiveTab = _useState0[1];
  var _useState1 = (0,react.useState)(false),
    _useState10 = (0,slicedToArray/* default */.A)(_useState1, 2),
    previewMode = _useState10[0],
    setPreviewMode = _useState10[1];
  var _useState11 = (0,react.useState)(null),
    _useState12 = (0,slicedToArray/* default */.A)(_useState11, 2),
    selectedComponent = _useState12[0],
    setSelectedComponent = _useState12[1];
  var _useState13 = (0,react.useState)(false),
    _useState14 = (0,slicedToArray/* default */.A)(_useState13, 2),
    editModalVisible = _useState14[0],
    setEditModalVisible = _useState14[1];
  var _useState15 = (0,react.useState)(false),
    _useState16 = (0,slicedToArray/* default */.A)(_useState15, 2),
    settingsDrawerVisible = _useState16[0],
    setSettingsDrawerVisible = _useState16[1];

  // App state
  var _useState17 = (0,react.useState)('My App'),
    _useState18 = (0,slicedToArray/* default */.A)(_useState17, 2),
    appName = _useState18[0],
    setAppName = _useState18[1];
  var _useState19 = (0,react.useState)('Built with App Builder MVP'),
    _useState20 = (0,slicedToArray/* default */.A)(_useState19, 2),
    appDescription = _useState20[0],
    setAppDescription = _useState20[1];

  // Component types with enhanced properties
  var componentTypes = [{
    value: 'Button',
    label: 'Button',
    icon: '🔘',
    defaultProps: {
      text: 'Click me',
      type: 'primary',
      size: 'medium'
    }
  }, {
    value: 'Text',
    label: 'Text',
    icon: '📝',
    defaultProps: {
      content: 'Sample text',
      size: 'medium',
      color: '#000000'
    }
  }, {
    value: 'Input',
    label: 'Input Field',
    icon: '📝',
    defaultProps: {
      placeholder: 'Enter text...',
      type: 'text',
      required: false
    }
  }, {
    value: 'Image',
    label: 'Image',
    icon: '🖼️',
    defaultProps: {
      src: 'https://via.placeholder.com/150',
      alt: 'Image',
      width: 150
    }
  }, {
    value: 'Card',
    label: 'Card',
    icon: '🃏',
    defaultProps: {
      title: 'Card Title',
      content: 'Card content goes here'
    }
  }, {
    value: 'List',
    label: 'List',
    icon: '📋',
    defaultProps: {
      items: ['Item 1', 'Item 2', 'Item 3'],
      type: 'unordered'
    }
  }];

  // Layout types with enhanced properties
  var layoutTypes = [{
    value: 'Grid',
    label: 'Grid Layout',
    icon: '⚏',
    defaultProps: {
      columns: 3,
      gap: '16px',
      responsive: true
    }
  }, {
    value: 'Flex',
    label: 'Flex Layout',
    icon: '↔️',
    defaultProps: {
      direction: 'row',
      justify: 'center',
      align: 'center'
    }
  }, {
    value: 'Stack',
    label: 'Stack Layout',
    icon: '📚',
    defaultProps: {
      spacing: '16px',
      align: 'center',
      direction: 'vertical'
    }
  }];

  // Update component props when type changes
  (0,react.useEffect)(function () {
    var selectedType = componentTypes.find(function (type) {
      return type.value === componentType;
    });
    if (selectedType) {
      setComponentProps(selectedType.defaultProps);
    }
  }, [componentType]);

  // Display WebSocket connection status
  (0,react.useEffect)(function () {
    if (wsConnected) {
      es/* message */.iU.success('Real-time connection established');
    }
  }, [wsConnected]);

  // Handle adding a new component
  var handleAddComponent = function handleAddComponent() {
    if (!componentName.trim()) {
      es/* message */.iU.error('Please enter a component name');
      return;
    }
    var selectedType = componentTypes.find(function (type) {
      return type.value === componentType;
    });
    var newComponent = {
      id: "".concat(componentType.toLowerCase(), "-").concat(Date.now()),
      type: componentType,
      name: componentName,
      props: _objectSpread(_objectSpread(_objectSpread({}, selectedType.defaultProps), componentProps), {}, {
        name: componentName
      }),
      created: new Date().toISOString()
    };
    dispatch(actions/* addComponent */.X8(newComponent));
    setComponentName('');
    es/* message */.iU.success("Added ".concat(componentType, " component: ").concat(componentName));
  };

  // Handle editing a component
  var handleEditComponent = function handleEditComponent(component) {
    setSelectedComponent(component);
    setEditModalVisible(true);
  };

  // Handle deleting a component
  var handleDeleteComponent = function handleDeleteComponent(componentId) {
    dispatch(actions/* deleteComponent */.$5(componentId));
    es/* message */.iU.success('Component deleted');
  };

  // Handle adding a new layout
  var handleAddLayout = function handleAddLayout() {
    var selectedLayout = layoutTypes.find(function (type) {
      return type.value === layoutType;
    });
    var newLayout = {
      id: "".concat(layoutType.toLowerCase(), "-").concat(Date.now()),
      type: layoutType,
      components: [],
      props: selectedLayout.defaultProps,
      created: new Date().toISOString()
    };
    dispatch(actions/* addLayout */.S7(newLayout.type, newLayout.components, newLayout.props));
    es/* message */.iU.success("Added ".concat(layoutType, " layout"));
  };

  // Handle saving the app
  var handleSave = function handleSave() {
    var appData = {
      name: appName,
      description: appDescription,
      components: components,
      layouts: layouts,
      styles: {},
      data: {},
      metadata: {
        version: '1.0.0',
        created: new Date().toISOString(),
        componentCount: components.length,
        layoutCount: layouts.length
      }
    };
    dispatch(actions/* saveAppData */.ZL(appData));
    es/* message */.iU.success('App data saved successfully');
  };

  // Handle exporting the app
  var handleExport = function handleExport() {
    var exportData = {
      name: appName,
      description: appDescription,
      components: components,
      layouts: layouts,
      exportedAt: new Date().toISOString()
    };
    var dataStr = JSON.stringify(exportData, null, 2);
    var dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);
    var exportFileDefaultName = "".concat(appName.replace(/\s+/g, '-').toLowerCase(), "-app.json");
    var linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
    es/* message */.iU.success('App exported successfully');
  };

  // Handle preview mode toggle
  var handlePreviewToggle = function handlePreviewToggle() {
    setPreviewMode(!previewMode);
    es/* message */.iU.info(previewMode ? 'Exited preview mode' : 'Entered preview mode');
  };
  return /*#__PURE__*/react.createElement("div", {
    className: "app-builder-mvp"
  }, /*#__PURE__*/react.createElement(es/* Card */.Zp, {
    title: /*#__PURE__*/react.createElement("div", {
      style: {
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }
    }, /*#__PURE__*/react.createElement("div", null, /*#__PURE__*/react.createElement(Title, {
      level: 3,
      style: {
        margin: 0
      }
    }, appName, " - App Builder MVP"), /*#__PURE__*/react.createElement(Text, {
      type: "secondary"
    }, appDescription)), /*#__PURE__*/react.createElement("div", {
      className: "connection-status"
    }, wsConnected ? /*#__PURE__*/react.createElement(es/* Tag */.vw, {
      color: "green",
      icon: "\u25CF"
    }, "Real-time connected") : /*#__PURE__*/react.createElement(es/* Tag */.vw, {
      color: "orange",
      icon: "\u25CF"
    }, "HTTP fallback"))),
    bordered: false,
    extra: /*#__PURE__*/react.createElement(es/* Space */.$x, null, /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
      title: "App Settings"
    }, /*#__PURE__*/react.createElement(es/* Button */.$n, {
      icon: /*#__PURE__*/react.createElement(icons_es/* SettingOutlined */.JO7, null),
      onClick: function onClick() {
        return setSettingsDrawerVisible(true);
      }
    })), /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
      title: previewMode ? "Exit Preview" : "Preview App"
    }, /*#__PURE__*/react.createElement(es/* Button */.$n, {
      icon: /*#__PURE__*/react.createElement(icons_es/* EyeOutlined */.Om2, null),
      type: previewMode ? "primary" : "default",
      onClick: handlePreviewToggle
    })), /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
      title: "Save App"
    }, /*#__PURE__*/react.createElement(es/* Button */.$n, {
      icon: /*#__PURE__*/react.createElement(icons_es/* SaveOutlined */.ylI, null),
      type: "primary",
      onClick: handleSave
    })), /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
      title: "Export App"
    }, /*#__PURE__*/react.createElement(es/* Button */.$n, {
      icon: /*#__PURE__*/react.createElement(icons_es/* DownloadOutlined */.jsW, null),
      onClick: handleExport
    })))
  }, /*#__PURE__*/react.createElement(es/* Tabs */.tU, {
    activeKey: activeTab,
    onChange: setActiveTab
  }, /*#__PURE__*/react.createElement(TabPane, {
    tab: /*#__PURE__*/react.createElement("span", null, /*#__PURE__*/react.createElement(icons_es/* PlusOutlined */.bW0, null), "Builder"),
    key: "builder"
  }, /*#__PURE__*/react.createElement(es/* Card */.Zp, {
    title: "Add Components",
    size: "small",
    style: {
      marginBottom: 16
    }
  }, /*#__PURE__*/react.createElement(es/* Row */.fI, {
    gutter: [16, 16]
  }, /*#__PURE__*/react.createElement(es/* Col */.fv, {
    span: 8
  }, /*#__PURE__*/react.createElement(es/* Input */.pd, {
    placeholder: "Component Name",
    value: componentName,
    onChange: function onChange(e) {
      return setComponentName(e.target.value);
    },
    onPressEnter: handleAddComponent
  })), /*#__PURE__*/react.createElement(es/* Col */.fv, {
    span: 8
  }, /*#__PURE__*/react.createElement(es/* Select */.l6, {
    value: componentType,
    onChange: setComponentType,
    style: {
      width: '100%'
    },
    placeholder: "Select component type"
  }, componentTypes.map(function (type) {
    return /*#__PURE__*/react.createElement(Option, {
      key: type.value,
      value: type.value
    }, type.icon, " ", type.label);
  }))), /*#__PURE__*/react.createElement(es/* Col */.fv, {
    span: 8
  }, /*#__PURE__*/react.createElement(es/* Button */.$n, {
    type: "primary",
    icon: /*#__PURE__*/react.createElement(icons_es/* PlusOutlined */.bW0, null),
    onClick: handleAddComponent,
    block: true
  }, "Add Component")))), /*#__PURE__*/react.createElement(es/* Card */.Zp, {
    title: "Add Layouts",
    size: "small",
    style: {
      marginBottom: 16
    }
  }, /*#__PURE__*/react.createElement(es/* Row */.fI, {
    gutter: [16, 16]
  }, /*#__PURE__*/react.createElement(es/* Col */.fv, {
    span: 16
  }, /*#__PURE__*/react.createElement(es/* Select */.l6, {
    value: layoutType,
    onChange: setLayoutType,
    style: {
      width: '100%'
    },
    placeholder: "Select layout type"
  }, layoutTypes.map(function (type) {
    return /*#__PURE__*/react.createElement(Option, {
      key: type.value,
      value: type.value
    }, type.icon, " ", type.label);
  }))), /*#__PURE__*/react.createElement(es/* Col */.fv, {
    span: 8
  }, /*#__PURE__*/react.createElement(es/* Button */.$n, {
    type: "primary",
    icon: /*#__PURE__*/react.createElement(icons_es/* PlusOutlined */.bW0, null),
    onClick: handleAddLayout,
    block: true
  }, "Add Layout"))))), /*#__PURE__*/react.createElement(TabPane, {
    tab: /*#__PURE__*/react.createElement("span", null, /*#__PURE__*/react.createElement(icons_es/* CodeOutlined */.C$o, null), "Components"),
    key: "components"
  }, /*#__PURE__*/react.createElement(es/* Card */.Zp, {
    title: "Components (".concat(components.length, ")"),
    size: "small"
  }, components.length === 0 ? /*#__PURE__*/react.createElement(es/* Alert */.Fc, {
    message: "No components added yet",
    description: "Start building your app by adding components in the Builder tab.",
    type: "info",
    showIcon: true
  }) : /*#__PURE__*/react.createElement(es/* List */.B8, {
    dataSource: components,
    renderItem: function renderItem(component, index) {
      var _component$props;
      return /*#__PURE__*/react.createElement(es/* List */.B8.Item, {
        key: component.id || "component-".concat(index),
        actions: [/*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
          title: "Edit"
        }, /*#__PURE__*/react.createElement(es/* Button */.$n, {
          icon: /*#__PURE__*/react.createElement(icons_es/* EditOutlined */.xjh, null),
          size: "small",
          onClick: function onClick() {
            return handleEditComponent(component);
          }
        })), /*#__PURE__*/react.createElement(es/* Popconfirm */.iS, {
          title: "Delete this component?",
          onConfirm: function onConfirm() {
            return handleDeleteComponent(component.id);
          },
          okText: "Yes",
          cancelText: "No"
        }, /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
          title: "Delete"
        }, /*#__PURE__*/react.createElement(es/* Button */.$n, {
          icon: /*#__PURE__*/react.createElement(icons_es/* DeleteOutlined */.SUY, null),
          size: "small",
          danger: true
        })))]
      }, /*#__PURE__*/react.createElement(es/* List */.B8.Item.Meta, {
        title: /*#__PURE__*/react.createElement(es/* Space */.$x, null, /*#__PURE__*/react.createElement(es/* Tag */.vw, {
          color: "blue"
        }, component.type), /*#__PURE__*/react.createElement(Text, {
          strong: true
        }, component.name || ((_component$props = component.props) === null || _component$props === void 0 ? void 0 : _component$props.name))),
        description: /*#__PURE__*/react.createElement(Text, {
          type: "secondary"
        }, JSON.stringify(component.props, null, 2).substring(0, 100), JSON.stringify(component.props).length > 100 ? '...' : '')
      }));
    }
  }))), /*#__PURE__*/react.createElement(TabPane, {
    tab: /*#__PURE__*/react.createElement("span", null, /*#__PURE__*/react.createElement(icons_es/* SettingOutlined */.JO7, null), "Layouts"),
    key: "layouts"
  }, /*#__PURE__*/react.createElement(es/* Card */.Zp, {
    title: "Layouts (".concat(layouts.length, ")"),
    size: "small"
  }, layouts.length === 0 ? /*#__PURE__*/react.createElement(es/* Alert */.Fc, {
    message: "No layouts added yet",
    description: "Add layouts to organize your components in the Builder tab.",
    type: "info",
    showIcon: true
  }) : /*#__PURE__*/react.createElement(es/* List */.B8, {
    dataSource: layouts,
    renderItem: function renderItem(layout, index) {
      return /*#__PURE__*/react.createElement(es/* List */.B8.Item, {
        key: layout.id || "layout-".concat(index),
        actions: [/*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
          title: "Edit"
        }, /*#__PURE__*/react.createElement(es/* Button */.$n, {
          icon: /*#__PURE__*/react.createElement(icons_es/* EditOutlined */.xjh, null),
          size: "small"
        })), /*#__PURE__*/react.createElement(es/* Popconfirm */.iS, {
          title: "Delete this layout?",
          onConfirm: function onConfirm() {
            es/* message */.iU.success('Layout deleted');
          },
          okText: "Yes",
          cancelText: "No"
        }, /*#__PURE__*/react.createElement(es/* Tooltip */.m_, {
          title: "Delete"
        }, /*#__PURE__*/react.createElement(es/* Button */.$n, {
          icon: /*#__PURE__*/react.createElement(icons_es/* DeleteOutlined */.SUY, null),
          size: "small",
          danger: true
        })))]
      }, /*#__PURE__*/react.createElement(es/* List */.B8.Item.Meta, {
        title: /*#__PURE__*/react.createElement(es/* Space */.$x, null, /*#__PURE__*/react.createElement(es/* Tag */.vw, {
          color: "green"
        }, layout.type), /*#__PURE__*/react.createElement(Text, {
          strong: true
        }, "Layout ", index + 1)),
        description: /*#__PURE__*/react.createElement(Text, {
          type: "secondary"
        }, JSON.stringify(layout.styles || layout.props, null, 2).substring(0, 100), JSON.stringify(layout.styles || layout.props).length > 100 ? '...' : '')
      }));
    }
  }))), /*#__PURE__*/react.createElement(TabPane, {
    tab: /*#__PURE__*/react.createElement("span", null, /*#__PURE__*/react.createElement(icons_es/* PlayCircleOutlined */.VgC, null), "Preview"),
    key: "preview"
  }, /*#__PURE__*/react.createElement(es/* Card */.Zp, {
    title: "App Preview",
    size: "small"
  }, /*#__PURE__*/react.createElement(es/* Alert */.Fc, {
    message: "Preview Mode",
    description: "This is a simplified preview of your app. In a full implementation, this would render your actual components and layouts.",
    type: "info",
    showIcon: true,
    style: {
      marginBottom: 16
    }
  }), /*#__PURE__*/react.createElement("div", {
    style: {
      border: '2px dashed #d9d9d9',
      borderRadius: '6px',
      padding: '24px',
      textAlign: 'center',
      minHeight: '300px',
      backgroundColor: '#fafafa'
    }
  }, /*#__PURE__*/react.createElement(Title, {
    level: 4
  }, "Your App Preview"), /*#__PURE__*/react.createElement(Text, {
    type: "secondary"
  }, "Components: ", components.length, " | Layouts: ", layouts.length), components.length > 0 && /*#__PURE__*/react.createElement("div", {
    style: {
      marginTop: 16
    }
  }, /*#__PURE__*/react.createElement(Text, {
    strong: true
  }, "Components in your app:"), /*#__PURE__*/react.createElement("div", {
    style: {
      marginTop: 8
    }
  }, components.map(function (comp, index) {
    var _comp$props;
    return /*#__PURE__*/react.createElement(es/* Tag */.vw, {
      key: index,
      style: {
        margin: 4
      }
    }, comp.type, ": ", comp.name || ((_comp$props = comp.props) === null || _comp$props === void 0 ? void 0 : _comp$props.name));
  }))), layouts.length > 0 && /*#__PURE__*/react.createElement("div", {
    style: {
      marginTop: 16
    }
  }, /*#__PURE__*/react.createElement(Text, {
    strong: true
  }, "Layouts in your app:"), /*#__PURE__*/react.createElement("div", {
    style: {
      marginTop: 8
    }
  }, layouts.map(function (layout, index) {
    return /*#__PURE__*/react.createElement(es/* Tag */.vw, {
      key: index,
      color: "green",
      style: {
        margin: 4
      }
    }, layout.type, " Layout");
  })))))))));
};
/* harmony default export */ const pages_AppBuilderMVP = (AppBuilderMVP);

/***/ }),

/***/ 52725:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AU: () => (/* binding */ selectWebSocketStatus),
/* harmony export */   Md: () => (/* binding */ selectWebSocketConnectionData),
/* harmony export */   Mp: () => (/* binding */ selectAppComponentsAndLayouts),
/* harmony export */   c9: () => (/* binding */ selectAppComponents)
/* harmony export */ });
/* unused harmony exports selectNetworkStatus, selectAPIStatus, selectWebSocketMessages, selectLastWebSocketMessage, selectUIState, selectCurrentView, selectPreviewMode, selectSidebarOpen, selectAppLoading, selectAILoading, selectAppLayouts, selectAppStyles, selectAppData, selectComponentById, selectLayoutById, selectStyleBySelector, selectAPIKeys, selectAPIKeyValidationStatus, selectGlobalError, selectAppFullState, selectApplicationState, selectPerformanceMetrics, selectUICurrentViewAndPreview, selectUISidebarAndView, selectWebSocketFullState, selectAppBuilderState, selectAIState */
/* harmony import */ var reselect__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(22325);
/**
 * Redux Selectors
 *
 * This file contains memoized selectors for the Redux store.
 * Using memoized selectors prevents unnecessary re-renders when the state hasn't changed.
 *
 * Performance benefits:
 * 1. Prevents unnecessary re-renders by returning the same reference when inputs haven't changed
 * 2. Enables component-specific optimizations by selecting only needed data
 * 3. Reduces computation by caching derived data
 */



/**
 * Basic selectors
 * These selectors directly access state properties without transformation
 * They serve as input selectors for memoized selectors
 */
var getNetworkState = function getNetworkState(state) {
  return state.network || {};
};
var getUIState = function getUIState(state) {
  return state.ui || {};
};
var getAppState = function getAppState(state) {
  return state.app || {};
};
var getAppDataState = function getAppDataState(state) {
  return state.appData || {};
};
var getWebSocketState = function getWebSocketState(state) {
  return state.websocket || {};
};
var getAPIKeysState = function getAPIKeysState(state) {
  return state.apiKeys || {};
};
var getErrorState = function getErrorState(state) {
  return state.error || null;
};

/**
 * Memoized selectors
 * These selectors use createSelector to memoize results
 * They only recompute when their input selectors return new values
 */

// Network selectors
var selectNetworkStatus = (0,reselect__WEBPACK_IMPORTED_MODULE_0__/* .createSelector */ .Mz)([getNetworkState], function (network) {
  return {
    isOnline: network.isOnline !== undefined ? network.isOnline : navigator.onLine,
    lastOnline: network.lastOnline || null,
    connectionType: network.connectionType || null,
    effectiveType: network.effectiveType || null,
    downlink: network.downlink || null,
    rtt: network.rtt || null
  };
});
var selectAPIStatus = (0,reselect__WEBPACK_IMPORTED_MODULE_0__/* .createSelector */ .Mz)([getNetworkState], function (network) {
  return network.apiStatus || {
    isAvailable: true,
    lastChecked: null,
    error: null
  };
});

// WebSocket selectors
var selectWebSocketStatus = (0,reselect__WEBPACK_IMPORTED_MODULE_0__/* .createSelector */ .Mz)([getWebSocketState], function (websocket) {
  return {
    connected: websocket.connected || false,
    connecting: websocket.connecting || false,
    error: websocket.error || null
  };
});
var selectWebSocketMessages = (0,reselect__WEBPACK_IMPORTED_MODULE_0__/* .createSelector */ .Mz)([getWebSocketState], function (websocket) {
  return websocket.messages || [];
});
var selectLastWebSocketMessage = (0,reselect__WEBPACK_IMPORTED_MODULE_0__/* .createSelector */ .Mz)([selectWebSocketMessages], function (messages) {
  return messages.length > 0 ? messages[messages.length - 1] : null;
});

// UI selectors
var selectUIState = (0,reselect__WEBPACK_IMPORTED_MODULE_0__/* .createSelector */ .Mz)([getUIState], function (ui) {
  return {
    sidebarOpen: ui.sidebarOpen !== undefined ? ui.sidebarOpen : true,
    currentView: ui.currentView || 'components',
    previewMode: ui.previewMode || false,
    loading: ui.loading || false
  };
});
var selectCurrentView = (0,reselect__WEBPACK_IMPORTED_MODULE_0__/* .createSelector */ .Mz)([getUIState], function (ui) {
  return ui.currentView || 'components';
});
var selectPreviewMode = (0,reselect__WEBPACK_IMPORTED_MODULE_0__/* .createSelector */ .Mz)([getUIState], function (ui) {
  return ui.previewMode || false;
});
var selectSidebarOpen = (0,reselect__WEBPACK_IMPORTED_MODULE_0__/* .createSelector */ .Mz)([getUIState], function (ui) {
  return ui.sidebarOpen !== undefined ? ui.sidebarOpen : true;
});

// Loading state selectors
var selectAppLoading = (0,reselect__WEBPACK_IMPORTED_MODULE_0__/* .createSelector */ .Mz)([getAppState, getUIState], function (app, ui) {
  return app.loading || ui.loading || false;
});
var selectAILoading = (0,reselect__WEBPACK_IMPORTED_MODULE_0__/* .createSelector */ .Mz)([getAppState], function (app) {
  return app.aiLoading || false;
});

// App data selectors
var selectAppComponents = (0,reselect__WEBPACK_IMPORTED_MODULE_0__/* .createSelector */ .Mz)([getAppDataState], function (appData) {
  return appData.components || [];
});
var selectAppLayouts = (0,reselect__WEBPACK_IMPORTED_MODULE_0__/* .createSelector */ .Mz)([getAppDataState], function (appData) {
  return appData.layouts || [];
});
var selectAppStyles = (0,reselect__WEBPACK_IMPORTED_MODULE_0__/* .createSelector */ .Mz)([getAppDataState], function (appData) {
  return appData.styles || {};
});
var selectAppData = (0,reselect__WEBPACK_IMPORTED_MODULE_0__/* .createSelector */ .Mz)([getAppDataState], function (appData) {
  return appData.data || {};
});
var selectComponentById = function selectComponentById(id) {
  return createSelector([selectAppComponents], function (components) {
    return components.find(function (component) {
      return component.id === id;
    }) || null;
  });
};
var selectLayoutById = function selectLayoutById(id) {
  return createSelector([selectAppLayouts], function (layouts) {
    return layouts.find(function (layout) {
      return layout.id === id;
    }) || null;
  });
};
var selectStyleBySelector = function selectStyleBySelector(selector) {
  return createSelector([selectAppStyles], function (styles) {
    return styles[selector] || {};
  });
};

// API Keys selectors
var selectAPIKeys = (0,reselect__WEBPACK_IMPORTED_MODULE_0__/* .createSelector */ .Mz)([getAPIKeysState], function (apiKeys) {
  return apiKeys.keys || {};
});
var selectAPIKeyValidationStatus = (0,reselect__WEBPACK_IMPORTED_MODULE_0__/* .createSelector */ .Mz)([getAPIKeysState], function (apiKeys) {
  return apiKeys.validationStatus || {};
});

// Error selectors
var selectGlobalError = (0,reselect__WEBPACK_IMPORTED_MODULE_0__/* .createSelector */ .Mz)([getErrorState], function (error) {
  return error;
});

/**
 * Combined selectors
 * These selectors combine multiple selectors to create derived data
 */
var selectAppFullState = (0,reselect__WEBPACK_IMPORTED_MODULE_0__/* .createSelector */ .Mz)([selectAppComponents, selectAppLayouts, selectAppStyles, selectAppData], function (components, layouts, styles, data) {
  return {
    components: components,
    layouts: layouts,
    styles: styles,
    data: data
  };
});
var selectApplicationState = (0,reselect__WEBPACK_IMPORTED_MODULE_0__/* .createSelector */ .Mz)([selectAppFullState, selectUIState, selectWebSocketStatus, selectNetworkStatus], function (appData, ui, websocket, network) {
  return {
    appData: appData,
    ui: ui,
    websocket: websocket,
    network: network
  };
});

// Performance-related selectors
var selectPerformanceMetrics = (0,reselect__WEBPACK_IMPORTED_MODULE_0__/* .createSelector */ .Mz)([getAppState], function (app) {
  return app.performance || {
    renderCounts: {},
    actionDurations: [],
    memoryUsage: null,
    fps: null
  };
});

/**
 * UI-specific memoized selectors
 * These prevent unnecessary re-renders by maintaining reference equality
 */
var selectUICurrentViewAndPreview = (0,reselect__WEBPACK_IMPORTED_MODULE_0__/* .createSelector */ .Mz)([getUIState], function (ui) {
  return {
    currentView: ui.currentView || 'components',
    previewMode: ui.previewMode || false
  };
});
var selectUISidebarAndView = (0,reselect__WEBPACK_IMPORTED_MODULE_0__/* .createSelector */ .Mz)([getUIState], function (ui) {
  return {
    sidebarOpen: ui.sidebarOpen !== undefined ? ui.sidebarOpen : true,
    currentView: ui.currentView || 'components'
  };
});

/**
 * WebSocket-specific memoized selectors
 * These prevent object recreation on every render
 */
var selectWebSocketConnectionData = (0,reselect__WEBPACK_IMPORTED_MODULE_0__/* .createSelector */ .Mz)([getWebSocketState], function (websocket) {
  return {
    connected: websocket.connected || false,
    messages: websocket.messages || [],
    url: websocket.url || null
  };
});
var selectWebSocketFullState = (0,reselect__WEBPACK_IMPORTED_MODULE_0__/* .createSelector */ .Mz)([getWebSocketState], function (websocket) {
  return {
    connected: websocket.connected || false,
    connecting: websocket.connecting || false,
    error: websocket.error || null,
    messages: websocket.messages || [],
    url: websocket.url || null,
    status: websocket.status || 'disconnected'
  };
});

/**
 * App data memoized selectors for components that need multiple state slices
 */
var selectAppComponentsAndLayouts = (0,reselect__WEBPACK_IMPORTED_MODULE_0__/* .createSelector */ .Mz)([getAppState, getAppDataState], function (app, appData) {
  return {
    components: app.components || appData.components || [],
    layouts: app.layouts || appData.layouts || []
  };
});
var selectAppBuilderState = (0,reselect__WEBPACK_IMPORTED_MODULE_0__/* .createSelector */ .Mz)([getAppState, getAppDataState], function (app, appData) {
  return {
    components: app.components || appData.components || [],
    layouts: app.layouts || appData.layouts || [],
    styles: app.styles || appData.styles || {},
    data: app.data || appData.data || {}
  };
});

/**
 * AI-specific selectors
 */
var selectAIState = (0,reselect__WEBPACK_IMPORTED_MODULE_0__/* .createSelector */ .Mz)([getAppState], function (app) {
  return {
    aiSuggestions: app.aiSuggestions || [],
    aiLoading: app.aiLoading || false,
    aiError: app.aiError || null,
    generatedImage: app.generatedImage || null
  };
});

/***/ })

}]);