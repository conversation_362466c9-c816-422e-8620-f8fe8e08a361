"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[4788],{

/***/ 48860:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(5544);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96540);
/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(2543);
/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_3__);


function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }



/**
 * Custom hook for optimizing preview performance
 * Handles virtual rendering, component caching, and performance monitoring
 */
var usePreviewPerformance = function usePreviewPerformance(_ref) {
  var _ref$components = _ref.components,
    components = _ref$components === void 0 ? [] : _ref$components,
    _ref$containerHeight = _ref.containerHeight,
    containerHeight = _ref$containerHeight === void 0 ? 600 : _ref$containerHeight,
    _ref$itemHeight = _ref.itemHeight,
    itemHeight = _ref$itemHeight === void 0 ? 100 : _ref$itemHeight,
    _ref$overscan = _ref.overscan,
    overscan = _ref$overscan === void 0 ? 5 : _ref$overscan,
    _ref$enableVirtualiza = _ref.enableVirtualization,
    enableVirtualization = _ref$enableVirtualiza === void 0 ? true : _ref$enableVirtualiza,
    _ref$enablePerformanc = _ref.enablePerformanceMonitoring,
    enablePerformanceMonitoring = _ref$enablePerformanc === void 0 ? true : _ref$enablePerformanc;
  // State for virtualization
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState, 2),
    scrollTop = _useState2[0],
    setScrollTop = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState3, 2),
    containerRef = _useState4[0],
    setContainerRef = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({
      start: 0,
      end: 0
    }),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState5, 2),
    visibleRange = _useState6[0],
    setVisibleRange = _useState6[1];

  // Performance monitoring state
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState7, 2),
    renderTime = _useState8[0],
    setRenderTime = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(60),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState9, 2),
    frameRate = _useState0[0],
    setFrameRate = _useState0[1];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(_useState1, 2),
    memoryUsage = _useState10[0],
    setMemoryUsage = _useState10[1];

  // Refs for performance tracking
  var renderStartTime = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(0);
  var frameCount = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(0);
  var lastFrameTime = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(performance.now());
  var componentCache = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(new Map());
  var intersectionObserver = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);

  // Calculate visible items for virtualization
  var calculateVisibleRange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if (!enableVirtualization || !containerRef || components.length === 0) {
      return {
        start: 0,
        end: components.length
      };
    }
    var startIndex = Math.floor(scrollTop / itemHeight);
    var endIndex = Math.min(startIndex + Math.ceil(containerHeight / itemHeight) + overscan, components.length);
    return {
      start: Math.max(0, startIndex - overscan),
      end: endIndex
    };
  }, [scrollTop, itemHeight, containerHeight, overscan, components.length, enableVirtualization, containerRef]);

  // Update visible range when scroll changes
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    var newRange = calculateVisibleRange();
    setVisibleRange(newRange);
  }, [calculateVisibleRange]);

  // Throttled scroll handler
  var handleScroll = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((0,lodash__WEBPACK_IMPORTED_MODULE_3__.throttle)(function (event) {
    if (event.target) {
      setScrollTop(event.target.scrollTop);
    }
  }, 16),
  // ~60fps
  []);

  // Get visible components for rendering
  var visibleComponents = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(function () {
    if (!enableVirtualization) {
      return components.map(function (component, index) {
        return {
          component: component,
          index: index
        };
      });
    }
    return components.slice(visibleRange.start, visibleRange.end).map(function (component, relativeIndex) {
      return {
        component: component,
        index: visibleRange.start + relativeIndex
      };
    });
  }, [components, visibleRange, enableVirtualization]);

  // Component caching for performance
  var getCachedComponent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (componentId, renderFunction) {
    var cacheKey = "".concat(componentId, "_").concat(JSON.stringify(components.find(function (c) {
      return c.id === componentId;
    })));
    if (componentCache.current.has(cacheKey)) {
      return componentCache.current.get(cacheKey);
    }
    var renderedComponent = renderFunction();
    componentCache.current.set(cacheKey, renderedComponent);

    // Limit cache size to prevent memory leaks
    if (componentCache.current.size > 100) {
      var firstKey = componentCache.current.keys().next().value;
      componentCache.current["delete"](firstKey);
    }
    return renderedComponent;
  }, [components]);

  // Performance monitoring
  var startRenderMeasurement = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if (enablePerformanceMonitoring) {
      renderStartTime.current = performance.now();
    }
  }, [enablePerformanceMonitoring]);
  var endRenderMeasurement = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if (enablePerformanceMonitoring && renderStartTime.current > 0) {
      var renderDuration = performance.now() - renderStartTime.current;
      setRenderTime(renderDuration);
      renderStartTime.current = 0;
    }
  }, [enablePerformanceMonitoring]);

  // Frame rate monitoring
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    if (!enablePerformanceMonitoring) return;
    var animationId;
    var _measureFrameRate = function measureFrameRate() {
      var now = performance.now();
      var delta = now - lastFrameTime.current;
      if (delta >= 1000) {
        var fps = Math.round(frameCount.current * 1000 / delta);
        setFrameRate(fps);
        frameCount.current = 0;
        lastFrameTime.current = now;
      } else {
        frameCount.current++;
      }
      animationId = requestAnimationFrame(_measureFrameRate);
    };
    animationId = requestAnimationFrame(_measureFrameRate);
    return function () {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [enablePerformanceMonitoring]);

  // Memory usage monitoring
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    if (!enablePerformanceMonitoring || !performance.memory) return;
    var measureMemory = function measureMemory() {
      var memoryInfo = performance.memory;
      var usedMB = Math.round(memoryInfo.usedJSHeapSize / 1024 / 1024);
      setMemoryUsage(usedMB);
    };
    var interval = setInterval(measureMemory, 5000);
    measureMemory(); // Initial measurement

    return function () {
      return clearInterval(interval);
    };
  }, [enablePerformanceMonitoring]);

  // Intersection Observer for lazy loading
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    if (!enableVirtualization) return;
    intersectionObserver.current = new IntersectionObserver(function (entries) {
      entries.forEach(function (entry) {
        if (entry.isIntersecting) {
          // Component is visible, ensure it's rendered
          var componentId = entry.target.dataset.componentId;
          if (componentId) {
            // Trigger re-render if needed
          }
        }
      });
    }, {
      root: containerRef,
      rootMargin: "".concat(overscan * itemHeight, "px"),
      threshold: 0.1
    });
    return function () {
      if (intersectionObserver.current) {
        intersectionObserver.current.disconnect();
      }
    };
  }, [containerRef, overscan, itemHeight, enableVirtualization]);

  // Clear cache when components change significantly
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    var componentIds = new Set(components.map(function (c) {
      return c.id;
    }));
    var cachedIds = new Set(Array.from(componentCache.current.keys()).map(function (key) {
      return key.split('_')[0];
    }));

    // Remove cached components that no longer exist
    cachedIds.forEach(function (cachedId) {
      if (!componentIds.has(cachedId)) {
        Array.from(componentCache.current.keys()).filter(function (key) {
          return key.startsWith(cachedId);
        }).forEach(function (key) {
          return componentCache.current["delete"](key);
        });
      }
    });
  }, [components]);

  // Get container props for virtualization
  var getContainerProps = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if (!enableVirtualization) {
      return {};
    }
    return {
      ref: setContainerRef,
      onScroll: handleScroll,
      style: {
        height: containerHeight,
        overflow: 'auto',
        position: 'relative'
      }
    };
  }, [enableVirtualization, containerHeight, handleScroll]);

  // Get spacer props for virtual scrolling
  var getSpacerProps = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function () {
    if (!enableVirtualization) {
      return {
        before: {},
        after: {}
      };
    }
    var totalHeight = components.length * itemHeight;
    var beforeHeight = visibleRange.start * itemHeight;
    var afterHeight = totalHeight - visibleRange.end * itemHeight;
    return {
      before: {
        style: {
          height: beforeHeight,
          width: '100%'
        }
      },
      after: {
        style: {
          height: afterHeight,
          width: '100%'
        }
      }
    };
  }, [enableVirtualization, components.length, itemHeight, visibleRange]);

  // Performance optimization utilities
  var optimizationUtils = {
    clearCache: function clearCache() {
      return componentCache.current.clear();
    },
    getCacheSize: function getCacheSize() {
      return componentCache.current.size;
    },
    getPerformanceMetrics: function getPerformanceMetrics() {
      return {
        renderTime: renderTime,
        frameRate: frameRate,
        memoryUsage: memoryUsage,
        cacheSize: componentCache.current.size,
        visibleComponents: visibleComponents.length,
        totalComponents: components.length
      };
    },
    shouldRender: function shouldRender(componentId) {
      // Check if component should be rendered based on visibility
      if (!enableVirtualization) return true;
      var componentIndex = components.findIndex(function (c) {
        return c.id === componentId;
      });
      return componentIndex >= visibleRange.start && componentIndex < visibleRange.end;
    }
  };
  return _objectSpread({
    // Virtualization
    visibleComponents: visibleComponents,
    visibleRange: visibleRange,
    getContainerProps: getContainerProps,
    getSpacerProps: getSpacerProps,
    // Performance monitoring
    renderTime: renderTime,
    frameRate: frameRate,
    memoryUsage: memoryUsage,
    startRenderMeasurement: startRenderMeasurement,
    endRenderMeasurement: endRenderMeasurement,
    // Caching
    getCachedComponent: getCachedComponent
  }, optimizationUtils);
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (usePreviewPerformance);

/***/ }),

/***/ 87169:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* unused harmony export useAIDesignSuggestions */
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(96540);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(71468);
/* harmony import */ var _redux_selectors__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(52725);
/* harmony import */ var _services_aiDesignService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(86329);
/* harmony import */ var _redux_actions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(81616);



function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }







/**
 * Custom hook for managing AI design suggestions
 * Provides layout suggestions, component combinations, and app analysis
 */
var useAIDesignSuggestions = function useAIDesignSuggestions() {
  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
  var _options$autoRefresh = options.autoRefresh,
    autoRefresh = _options$autoRefresh === void 0 ? true : _options$autoRefresh,
    _options$refreshInter = options.refreshInterval,
    refreshInterval = _options$refreshInter === void 0 ? 30000 : _options$refreshInter,
    _options$enableCache = options.enableCache,
    enableCache = _options$enableCache === void 0 ? true : _options$enableCache,
    _options$context = options.context,
    context = _options$context === void 0 ? {} : _options$context;
  var dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_5__/* .useDispatch */ .wA)();

  // Get app state from Redux using memoized selectors
  var _useSelector = (0,react_redux__WEBPACK_IMPORTED_MODULE_5__/* .useSelector */ .d4)(_redux_selectors__WEBPACK_IMPORTED_MODULE_6__/* .selectAppComponentsAndLayouts */ .Mp),
    components = _useSelector.components,
    layouts = _useSelector.layouts;
  var selectedComponent = (0,react_redux__WEBPACK_IMPORTED_MODULE_5__/* .useSelector */ .d4)(function (state) {
    var _state$ui;
    return ((_state$ui = state.ui) === null || _state$ui === void 0 ? void 0 : _state$ui.selectedComponent) || null;
  });

  // Local state
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({
      layout: [],
      combinations: [],
      analysis: null
    }),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState, 2),
    suggestions = _useState2[0],
    setSuggestions = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)({
      layout: false,
      combinations: false,
      analysis: false
    }),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState3, 2),
    loading = _useState4[0],
    setLoading = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState5, 2),
    error = _useState6[0],
    setError = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(null),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_useState7, 2),
    lastRefresh = _useState8[0],
    setLastRefresh = _useState8[1];

  // Refs for cleanup
  var refreshIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);
  var abortControllerRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);

  // Load all suggestions
  var loadSuggestions = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(/*#__PURE__*/(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee() {
    var force,
      _yield$Promise$allSet,
      _yield$Promise$allSet2,
      layoutResponse,
      combinationsResponse,
      analysisResponse,
      layoutSuggestions,
      combinationSuggestions,
      analysis,
      _args = arguments,
      _t;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function (_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          force = _args.length > 0 && _args[0] !== undefined ? _args[0] : false;
          if (!(!components || components.length === 0)) {
            _context.next = 1;
            break;
          }
          setSuggestions({
            layout: [],
            combinations: [],
            analysis: null
          });
          return _context.abrupt("return");
        case 1:
          // Abort previous requests
          if (abortControllerRef.current) {
            abortControllerRef.current.abort();
          }
          abortControllerRef.current = new AbortController();
          setError(null);
          setLoading({
            layout: true,
            combinations: true,
            analysis: true
          });
          _context.prev = 2;
          // Clear cache if force refresh
          if (force && enableCache) {
            _services_aiDesignService__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .A.clearCache();
          }

          // Load all suggestions in parallel
          _context.next = 3;
          return Promise.allSettled([_services_aiDesignService__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .A.generateLayoutSuggestions(components, layouts, context), _services_aiDesignService__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .A.generateComponentCombinations(components, selectedComponent, context), _services_aiDesignService__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .A.analyzeAppStructure(components, layouts)]);
        case 3:
          _yield$Promise$allSet = _context.sent;
          _yield$Promise$allSet2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(_yield$Promise$allSet, 3);
          layoutResponse = _yield$Promise$allSet2[0];
          combinationsResponse = _yield$Promise$allSet2[1];
          analysisResponse = _yield$Promise$allSet2[2];
          // Process layout suggestions
          layoutSuggestions = layoutResponse.status === 'fulfilled' ? layoutResponse.value.suggestions || [] : []; // Process combination suggestions
          combinationSuggestions = combinationsResponse.status === 'fulfilled' ? combinationsResponse.value.suggestions || [] : []; // Process analysis
          analysis = analysisResponse.status === 'fulfilled' ? analysisResponse.value.analysis || null : null;
          setSuggestions({
            layout: layoutSuggestions,
            combinations: combinationSuggestions,
            analysis: analysis
          });
          setLastRefresh(new Date());

          // Log any errors
          [layoutResponse, combinationsResponse, analysisResponse].forEach(function (response, index) {
            if (response.status === 'rejected') {
              var names = ['layout', 'combinations', 'analysis'];
              console.warn("Failed to load ".concat(names[index], " suggestions:"), response.reason);
            }
          });
          _context.next = 5;
          break;
        case 4:
          _context.prev = 4;
          _t = _context["catch"](2);
          if (_t.name !== 'AbortError') {
            setError("Failed to load suggestions: ".concat(_t.message));
          }
        case 5:
          _context.prev = 5;
          setLoading({
            layout: false,
            combinations: false,
            analysis: false
          });
          return _context.finish(5);
        case 6:
        case "end":
          return _context.stop();
      }
    }, _callee, null, [[2, 4, 5, 6]]);
  })), [components, layouts, selectedComponent, context, enableCache]);

  // Load specific suggestion type
  var loadLayoutSuggestions = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(/*#__PURE__*/(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee2() {
    var response, _t2;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function (_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          if (!(!components || components.length === 0)) {
            _context2.next = 1;
            break;
          }
          return _context2.abrupt("return");
        case 1:
          setLoading(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              layout: true
            });
          });
          setError(null);
          _context2.prev = 2;
          _context2.next = 3;
          return _services_aiDesignService__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .A.generateLayoutSuggestions(components, layouts, context);
        case 3:
          response = _context2.sent;
          setSuggestions(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              layout: response.suggestions || []
            });
          });
          _context2.next = 5;
          break;
        case 4:
          _context2.prev = 4;
          _t2 = _context2["catch"](2);
          setError("Failed to load layout suggestions: ".concat(_t2.message));
        case 5:
          _context2.prev = 5;
          setLoading(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              layout: false
            });
          });
          return _context2.finish(5);
        case 6:
        case "end":
          return _context2.stop();
      }
    }, _callee2, null, [[2, 4, 5, 6]]);
  })), [components, layouts, context]);
  var loadCombinationSuggestions = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(/*#__PURE__*/(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().mark(function _callee3() {
    var response, _t3;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_3___default().wrap(function (_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          if (!(!components || components.length === 0)) {
            _context3.next = 1;
            break;
          }
          return _context3.abrupt("return");
        case 1:
          setLoading(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              combinations: true
            });
          });
          setError(null);
          _context3.prev = 2;
          _context3.next = 3;
          return _services_aiDesignService__WEBPACK_IMPORTED_MODULE_7__/* ["default"] */ .A.generateComponentCombinations(components, selectedComponent, context);
        case 3:
          response = _context3.sent;
          setSuggestions(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              combinations: response.suggestions || []
            });
          });
          _context3.next = 5;
          break;
        case 4:
          _context3.prev = 4;
          _t3 = _context3["catch"](2);
          setError("Failed to load combination suggestions: ".concat(_t3.message));
        case 5:
          _context3.prev = 5;
          setLoading(function (prev) {
            return _objectSpread(_objectSpread({}, prev), {}, {
              combinations: false
            });
          });
          return _context3.finish(5);
        case 6:
        case "end":
          return _context3.stop();
      }
    }, _callee3, null, [[2, 4, 5, 6]]);
  })), [components, selectedComponent, context]);

  // Apply layout suggestion
  var applyLayoutSuggestion = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (suggestion) {
    try {
      // This would integrate with your layout system
      // For now, we'll dispatch a generic action
      console.log('Applying layout suggestion:', suggestion);

      // You could dispatch a specific action here
      // dispatch(applyLayout(suggestion));

      return true;
    } catch (err) {
      setError("Failed to apply layout suggestion: ".concat(err.message));
      return false;
    }
  }, []);

  // Apply component combination suggestion
  var applyComponentCombination = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (suggestion) {
    try {
      if (suggestion.missing_components && suggestion.missing_components.length > 0) {
        // Add missing components
        suggestion.missing_components.forEach(function (componentType) {
          var newComponent = {
            type: componentType,
            props: {},
            id: "".concat(componentType, "-").concat(Date.now(), "-").concat(Math.random().toString(36).substr(2, 9))
          };
          dispatch((0,_redux_actions__WEBPACK_IMPORTED_MODULE_8__/* .addComponent */ .X8)(newComponent.type, newComponent.props));
        });
      }
      console.log('Applied component combination:', suggestion);
      return true;
    } catch (err) {
      setError("Failed to apply component combination: ".concat(err.message));
      return false;
    }
  }, [dispatch]);

  // Refresh suggestions
  var refresh = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function () {
    loadSuggestions(true);
  }, [loadSuggestions]);

  // Clear error
  var clearError = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function () {
    setError(null);
  }, []);

  // Setup auto-refresh
  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {
    if (autoRefresh && refreshInterval > 0) {
      refreshIntervalRef.current = setInterval(function () {
        loadSuggestions();
      }, refreshInterval);
      return function () {
        if (refreshIntervalRef.current) {
          clearInterval(refreshIntervalRef.current);
        }
      };
    }
  }, [autoRefresh, refreshInterval]); // Removed loadSuggestions to prevent infinite re-renders

  // Load suggestions when components change (but not on every loadSuggestions change)
  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {
    loadSuggestions();
  }, [components.length, selectedComponent === null || selectedComponent === void 0 ? void 0 : selectedComponent.id]); // Only depend on specific values that should trigger reload

  // Cleanup on unmount
  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {
    return function () {
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);
  return {
    // Data
    suggestions: suggestions,
    loading: loading,
    error: error,
    lastRefresh: lastRefresh,
    // Actions
    loadSuggestions: loadSuggestions,
    loadLayoutSuggestions: loadLayoutSuggestions,
    loadCombinationSuggestions: loadCombinationSuggestions,
    applyLayoutSuggestion: applyLayoutSuggestion,
    applyComponentCombination: applyComponentCombination,
    refresh: refresh,
    clearError: clearError,
    // Computed values
    hasLayoutSuggestions: suggestions.layout.length > 0,
    hasCombinationSuggestions: suggestions.combinations.length > 0,
    hasAnalysis: suggestions.analysis !== null,
    isLoading: loading.layout || loading.combinations || loading.analysis,
    // Component counts for display
    componentCount: components.length,
    layoutCount: layouts.length,
    selectedComponentType: (selectedComponent === null || selectedComponent === void 0 ? void 0 : selectedComponent.type) || null
  };
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useAIDesignSuggestions);

/***/ }),

/***/ 87177:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BY: () => (/* binding */ aiConfig),
/* harmony export */   Ot: () => (/* binding */ shouldShowWarnings),
/* harmony export */   UD: () => (/* binding */ isAIEnabled)
/* harmony export */ });
/* unused harmony exports isFeatureEnabled, checkAIServiceAvailability, errorConfig, devConfig */
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_1__);


/**
 * AI Configuration
 * Centralized configuration for AI features and services
 */

// Environment variables
var API_URL = {"ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_3096_UFFFAUNZEJEBLCJL","COLOR":"1","COLORTERM":"truecolor","CommonProgramFiles":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","ComSpec":"C:\\WINDOWS\\system32\\cmd.exe","DISABLE_ESLINT_PLUGIN":"false","DJANGO_SETTINGS_MODULE":"app_builder_201.settings","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_11988_1592913036":"1","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"","HOME":"C:\\Users\\<USER>\\Users\\danie","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","Path":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","ProgramFiles":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.2","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","VSCODE_INJECTION":"1","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","windir":"C:\\WINDOWS"}.REACT_APP_API_URL || 'http://localhost:8000';
var AI_ENABLED = {"ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_3096_UFFFAUNZEJEBLCJL","COLOR":"1","COLORTERM":"truecolor","CommonProgramFiles":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","ComSpec":"C:\\WINDOWS\\system32\\cmd.exe","DISABLE_ESLINT_PLUGIN":"false","DJANGO_SETTINGS_MODULE":"app_builder_201.settings","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_11988_1592913036":"1","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"","HOME":"C:\\Users\\<USER>\\Users\\danie","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","Path":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","ProgramFiles":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.2","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","VSCODE_INJECTION":"1","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","windir":"C:\\WINDOWS"}.REACT_APP_AI_ENABLED !== 'false'; // Default to true
var AI_FALLBACK_ENABLED = {"ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_3096_UFFFAUNZEJEBLCJL","COLOR":"1","COLORTERM":"truecolor","CommonProgramFiles":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","ComSpec":"C:\\WINDOWS\\system32\\cmd.exe","DISABLE_ESLINT_PLUGIN":"false","DJANGO_SETTINGS_MODULE":"app_builder_201.settings","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_11988_1592913036":"1","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"","HOME":"C:\\Users\\<USER>\\Users\\danie","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","Path":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","ProgramFiles":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.2","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","VSCODE_INJECTION":"1","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","windir":"C:\\WINDOWS"}.REACT_APP_AI_FALLBACK_ENABLED !== 'false'; // Default to true

// AI Service Configuration
var aiConfig = {
  // Core settings
  enabled: AI_ENABLED,
  fallbackEnabled: AI_FALLBACK_ENABLED,
  // API settings
  baseUrl: "".concat(API_URL, "/api/ai"),
  timeout: 10000,
  // 10 seconds
  retryAttempts: 2,
  retryDelay: 1000,
  // 1 second

  // Cache settings
  cacheEnabled: true,
  cacheTimeout: 5 * 60 * 1000,
  // 5 minutes

  // WebSocket settings
  websocketEnabled: true,
  websocketTimeout: 10000,
  // 10 seconds

  // Feature flags
  features: {
    layoutSuggestions: true,
    componentCombinations: true,
    appAnalysis: true,
    realTimeUpdates: true,
    collaborativeAI: false // Experimental
  },
  // Fallback behavior
  fallback: {
    showWarnings: false,
    // Set to false to reduce console noise
    useBasicAnalysis: true,
    provideFallbackSuggestions: true,
    gracefulDegradation: true
  },
  // Performance settings
  performance: {
    debounceDelay: 500,
    // Debounce AI requests
    maxConcurrentRequests: 3,
    backgroundRefresh: true,
    lazyLoading: true
  }
};

// Helper functions
var isAIEnabled = function isAIEnabled() {
  return aiConfig.enabled;
};
var isFeatureEnabled = function isFeatureEnabled(feature) {
  return aiConfig.enabled && aiConfig.features[feature];
};
var shouldShowWarnings = function shouldShowWarnings() {
  return aiConfig.fallback.showWarnings;
};

// Service availability checker
var checkAIServiceAvailability = /*#__PURE__*/(/* unused pure expression or super */ null && (function () {
  var _ref = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime.mark(function _callee() {
    var controller, timeoutId, response, _t;
    return _regeneratorRuntime.wrap(function (_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          if (aiConfig.enabled) {
            _context.next = 1;
            break;
          }
          return _context.abrupt("return", {
            available: false,
            reason: 'AI features disabled'
          });
        case 1:
          _context.prev = 1;
          controller = new AbortController();
          timeoutId = setTimeout(function () {
            return controller.abort();
          }, aiConfig.timeout);
          _context.next = 2;
          return fetch("".concat(aiConfig.baseUrl, "/health/"), {
            method: 'GET',
            signal: controller.signal,
            headers: {
              'Content-Type': 'application/json'
            }
          });
        case 2:
          response = _context.sent;
          clearTimeout(timeoutId);
          if (!response.ok) {
            _context.next = 3;
            break;
          }
          return _context.abrupt("return", {
            available: true,
            status: response.status
          });
        case 3:
          return _context.abrupt("return", {
            available: false,
            reason: "HTTP ".concat(response.status)
          });
        case 4:
          _context.next = 7;
          break;
        case 5:
          _context.prev = 5;
          _t = _context["catch"](1);
          if (!(_t.name === 'AbortError')) {
            _context.next = 6;
            break;
          }
          return _context.abrupt("return", {
            available: false,
            reason: 'Timeout'
          });
        case 6:
          return _context.abrupt("return", {
            available: false,
            reason: _t.message
          });
        case 7:
        case "end":
          return _context.stop();
      }
    }, _callee, null, [[1, 5]]);
  }));
  return function checkAIServiceAvailability() {
    return _ref.apply(this, arguments);
  };
}()));

// Error handling configuration
var errorConfig = {
  // Log levels: 'error', 'warn', 'info', 'debug'
  logLevel:  false ? 0 : 'warn',
  // Error reporting
  reportErrors: false,
  // Set to true to enable error reporting

  // User-facing messages
  messages: {
    serviceUnavailable: 'AI suggestions are temporarily unavailable. Using basic recommendations.',
    networkError: 'Unable to connect to AI service. Check your internet connection.',
    timeout: 'AI service is taking too long to respond. Using cached results.',
    fallback: 'Using offline AI suggestions.'
  }
};

// Development helpers
var devConfig = {
  mockResponses: "production" === 'development',
  debugMode: {"ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_3096_UFFFAUNZEJEBLCJL","COLOR":"1","COLORTERM":"truecolor","CommonProgramFiles":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","ComSpec":"C:\\WINDOWS\\system32\\cmd.exe","DISABLE_ESLINT_PLUGIN":"false","DJANGO_SETTINGS_MODULE":"app_builder_201.settings","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_11988_1592913036":"1","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"","HOME":"C:\\Users\\<USER>\\Users\\danie","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","Path":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","ProgramFiles":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.2","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","VSCODE_INJECTION":"1","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","windir":"C:\\WINDOWS"}.REACT_APP_AI_DEBUG === 'true',
  verboseLogging: {"ALLUSERSPROFILE":"C:\\ProgramData","API_TARGET":"http://localhost:8000","APPDATA":"C:\\Users\\<USER>\\AppData\\Roaming","ChocolateyInstall":"C:\\ProgramData\\chocolatey","ChocolateyLastPathUpdate":"133841632699909501","CHOKIDAR_USEPOLLING":"false","CHROME_CRASHPAD_PIPE_NAME":"\\\\.\\pipe\\crashpad_3096_UFFFAUNZEJEBLCJL","COLOR":"1","COLORTERM":"truecolor","CommonProgramFiles":"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files","CommonProgramW6432":"C:\\Program Files\\Common Files","COMPUTERNAME":"LAPTOP-E9FOD0GS","ComSpec":"C:\\WINDOWS\\system32\\cmd.exe","DISABLE_ESLINT_PLUGIN":"false","DJANGO_SETTINGS_MODULE":"app_builder_201.settings","DriverData":"C:\\Windows\\System32\\Drivers\\DriverData","EDITOR":"C:\\WINDOWS\\notepad.exe","EFC_11988_1592913036":"1","FAST_REFRESH":"true","GENERATE_SOURCEMAP":"true","GIT_ASKPASS":"c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh","GIT_PAGER":"","HOME":"C:\\Users\\<USER>\\Users\\danie","INIT_CWD":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","LANG":"en_US.UTF-8","LOCALAPPDATA":"C:\\Users\\<USER>\\AppData\\Local","LOGONSERVER":"\\\\LAPTOP-E9FOD0GS","NODE":"C:\\Program Files\\nodejs\\node.exe","NODE_ENV":"development","npm_command":"run-script","npm_config_audit":"","npm_config_auto_install_peers":"true","npm_config_cache":"C:\\Users\\<USER>\\AppData\\Local\\npm-cache","npm_config_fund":"","npm_config_globalconfig":"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc","npm_config_global_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_init_module":"C:\\Users\\<USER>\\.npm-init.js","npm_config_legacy_peer_deps":"true","npm_config_local_prefix":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend","npm_config_loglevel":"warn","npm_config_node_gyp":"C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js","npm_config_node_version":"18.x","npm_config_noproxy":"","npm_config_npm_version":"10.9.2","npm_config_prefer_offline":"true","npm_config_prefix":"C:\\Users\\<USER>\\AppData\\Roaming\\npm","npm_config_progress":"","npm_config_save_exact":"true","npm_config_strict_peer_dependencies":"","npm_config_userconfig":"C:\\Users\\<USER>\\.npmrc","npm_config_user_agent":"npm/10.9.2 node/v22.14.0 win32 x64 workspaces/false","npm_execpath":"C:\\Program Files\\nodejs\\node_modules\\npm\\bin\\npm-cli.js","npm_lifecycle_event":"build","npm_lifecycle_script":"webpack --mode production","npm_node_execpath":"C:\\Program Files\\nodejs\\node.exe","npm_package_json":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\package.json","npm_package_name":"frontend","npm_package_version":"1.0.0","NUMBER_OF_PROCESSORS":"12","OneDrive":"C:\\Users\\<USER>\\OneDrive","OneDriveConsumer":"C:\\Users\\<USER>\\OneDrive","ORIGINAL_XDG_CURRENT_DESKTOP":"undefined","OS":"Windows_NT","Path":"C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\frontend\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\app-builder-201\\node_modules\\.bin;C:\\Users\\<USER>\\New folder (2)\\node_modules\\.bin;C:\\Users\\<USER>\\node_modules\\.bin;C:\\Users\\<USER>\\.bin;C:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Python313\\Scripts\\;C:\\Python313\\;C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Testcontainers Desktop\\;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\Scripts;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL","PROCESSOR_ARCHITECTURE":"AMD64","PROCESSOR_IDENTIFIER":"Intel64 Family 6 Model 165 Stepping 2, GenuineIntel","PROCESSOR_LEVEL":"6","PROCESSOR_REVISION":"a502","ProgramData":"C:\\ProgramData","ProgramFiles":"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)","ProgramW6432":"C:\\Program Files","PROMPT":"$P$G","PSModulePath":"C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules","PUBLIC":"C:\\Users\\<USER>\\WINDOWS","TEMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","TERM_PROGRAM":"vscode","TERM_PROGRAM_VERSION":"1.101.2","TMP":"C:\\Users\\<USER>\\AppData\\Local\\Temp","USERDOMAIN":"LAPTOP-E9FOD0GS","USERDOMAIN_ROAMINGPROFILE":"LAPTOP-E9FOD0GS","USERNAME":"danie","USERPROFILE":"C:\\Users\\<USER>\\Users\\danie\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js","VSCODE_GIT_ASKPASS_NODE":"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe","VSCODE_GIT_IPC_HANDLE":"\\\\.\\pipe\\vscode-git-898c7e110b-sock","VSCODE_INJECTION":"1","WATCHPACK_POLLING":"false","WDS_SOCKET_HOST":"localhost","WDS_SOCKET_PATH":"/sockjs-node","WDS_SOCKET_PORT":"3000","WEBSOCKET_TARGET":"ws://localhost:8000","windir":"C:\\WINDOWS"}.REACT_APP_AI_VERBOSE === 'true'
};
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = ((/* unused pure expression or super */ null && (aiConfig)));

/***/ })

}]);