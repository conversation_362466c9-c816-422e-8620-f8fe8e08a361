/**
 * Tests for Redux selectors
 */
import {
  selectNetworkStatus,
  selectWebSocketStatus,
  selectUIState,
  selectAppLoading,
  selectAppComponents,
  selectAppLayouts,
  selectAppStyles,
  selectAppData,
  selectAppFullState,
  selectComponentById,
  selectLayoutById,
  selectStyleBySelector,
  selectApplicationState,
  selectPerformanceMetrics,
  selectUICurrentViewAndPreview,
  selectUISidebarAndView,
  selectWebSocketConnectionData,
  selectWebSocketFullState,
  selectAppComponentsAndLayouts,
  selectAppBuilderState
} from './selectors';

describe('Redux Selectors', () => {
  // Test state
  const mockState = {
    network: {
      isOnline: true,
      lastOnline: '2023-01-01T00:00:00.000Z',
      connectionType: 'wifi',
      effectiveType: '4g',
      downlink: 10,
      rtt: 50,
      apiStatus: {
        isAvailable: true,
        lastChecked: '2023-01-01T00:00:00.000Z',
        error: null
      }
    },
    ui: {
      sidebarOpen: true,
      currentView: 'components',
      previewMode: false,
      loading: false
    },
    app: {
      loading: false,
      aiLoading: false,
      performance: {
        renderCounts: {
          App: 1,
          AppLoader: 2
        },
        actionDurations: [
          { type: 'FETCH_DATA', duration: 100 }
        ],
        memoryUsage: {
          usedJSHeapSize: 50,
          totalJSHeapSize: 100
        },
        fps: 60
      }
    },
    appData: {
      components: [
        { id: 'comp1', name: 'Component 1' },
        { id: 'comp2', name: 'Component 2' }
      ],
      layouts: [
        { id: 'layout1', name: 'Layout 1' },
        { id: 'layout2', name: 'Layout 2' }
      ],
      styles: {
        '.container': { display: 'flex' },
        '.header': { fontWeight: 'bold' }
      },
      data: {
        users: [{ id: 1, name: 'User 1' }]
      }
    },
    websocket: {
      connected: true,
      connecting: false,
      error: null,
      messages: [
        { id: 'msg1', text: 'Hello' },
        { id: 'msg2', text: 'World' }
      ]
    },
    apiKeys: {
      keys: {
        openai: 'sk-123456'
      },
      validationStatus: {
        openai: { valid: true, lastChecked: '2023-01-01T00:00:00.000Z' }
      }
    },
    error: null
  };

  describe('Network selectors', () => {
    it('should select network status', () => {
      const result = selectNetworkStatus(mockState);
      expect(result).toEqual({
        isOnline: true,
        lastOnline: '2023-01-01T00:00:00.000Z',
        connectionType: 'wifi',
        effectiveType: '4g',
        downlink: 10,
        rtt: 50
      });
    });

    it('should handle missing network state', () => {
      const result = selectNetworkStatus({});
      expect(result.isOnline).toBeDefined();
    });
  });

  describe('WebSocket selectors', () => {
    it('should select websocket status', () => {
      const result = selectWebSocketStatus(mockState);
      expect(result).toEqual({
        connected: true,
        connecting: false,
        error: null
      });
    });

    it('should handle missing websocket state', () => {
      const result = selectWebSocketStatus({});
      expect(result.connected).toBe(false);
    });
  });

  describe('UI selectors', () => {
    it('should select UI state', () => {
      const result = selectUIState(mockState);
      expect(result).toEqual({
        sidebarOpen: true,
        currentView: 'components',
        previewMode: false,
        loading: false
      });
    });

    it('should handle missing UI state', () => {
      const result = selectUIState({});
      expect(result.sidebarOpen).toBe(true); // Default value
    });
  });

  describe('Loading selectors', () => {
    it('should select app loading state', () => {
      const result = selectAppLoading(mockState);
      expect(result).toBe(false);
    });

    it('should handle missing app state', () => {
      const result = selectAppLoading({});
      expect(result).toBe(false); // Default value
    });
  });

  describe('App data selectors', () => {
    it('should select app components', () => {
      const result = selectAppComponents(mockState);
      expect(result).toEqual([
        { id: 'comp1', name: 'Component 1' },
        { id: 'comp2', name: 'Component 2' }
      ]);
    });

    it('should select app layouts', () => {
      const result = selectAppLayouts(mockState);
      expect(result).toEqual([
        { id: 'layout1', name: 'Layout 1' },
        { id: 'layout2', name: 'Layout 2' }
      ]);
    });

    it('should select app styles', () => {
      const result = selectAppStyles(mockState);
      expect(result).toEqual({
        '.container': { display: 'flex' },
        '.header': { fontWeight: 'bold' }
      });
    });

    it('should select app data', () => {
      const result = selectAppData(mockState);
      expect(result).toEqual({
        users: [{ id: 1, name: 'User 1' }]
      });
    });

    it('should handle missing app data state', () => {
      const result = selectAppComponents({});
      expect(result).toEqual([]);
    });
  });

  describe('Parameterized selectors', () => {
    it('should select component by ID', () => {
      const selector = selectComponentById('comp1');
      const result = selector(mockState);
      expect(result).toEqual({ id: 'comp1', name: 'Component 1' });
    });

    it('should return null for non-existent component ID', () => {
      const selector = selectComponentById('non-existent');
      const result = selector(mockState);
      expect(result).toBeNull();
    });

    it('should select layout by ID', () => {
      const selector = selectLayoutById('layout1');
      const result = selector(mockState);
      expect(result).toEqual({ id: 'layout1', name: 'Layout 1' });
    });

    it('should select style by selector', () => {
      const selector = selectStyleBySelector('.container');
      const result = selector(mockState);
      expect(result).toEqual({ display: 'flex' });
    });
  });

  describe('Combined selectors', () => {
    it('should select app full state', () => {
      const result = selectAppFullState(mockState);
      expect(result).toEqual({
        components: [
          { id: 'comp1', name: 'Component 1' },
          { id: 'comp2', name: 'Component 2' }
        ],
        layouts: [
          { id: 'layout1', name: 'Layout 1' },
          { id: 'layout2', name: 'Layout 2' }
        ],
        styles: {
          '.container': { display: 'flex' },
          '.header': { fontWeight: 'bold' }
        },
        data: {
          users: [{ id: 1, name: 'User 1' }]
        }
      });
    });

    it('should select application state', () => {
      const result = selectApplicationState(mockState);
      expect(result).toHaveProperty('appData');
      expect(result).toHaveProperty('ui');
      expect(result).toHaveProperty('websocket');
      expect(result).toHaveProperty('network');
    });
  });

  describe('Performance selectors', () => {
    it('should select performance metrics', () => {
      const result = selectPerformanceMetrics(mockState);
      expect(result).toEqual({
        renderCounts: {
          App: 1,
          AppLoader: 2
        },
        actionDurations: [
          { type: 'FETCH_DATA', duration: 100 }
        ],
        memoryUsage: {
          usedJSHeapSize: 50,
          totalJSHeapSize: 100
        },
        fps: 60
      });
    });

    it('should handle missing performance metrics', () => {
      const result = selectPerformanceMetrics({});
      expect(result).toHaveProperty('renderCounts');
      expect(result).toHaveProperty('actionDurations');
      expect(result).toHaveProperty('memoryUsage');
      expect(result).toHaveProperty('fps');
    });
  });

  describe('Memoization', () => {
    it('should return the same reference for the same input', () => {
      const result1 = selectNetworkStatus(mockState);
      const result2 = selectNetworkStatus(mockState);
      expect(result1).toBe(result2); // Same reference
    });

    it('should return a new reference for different input', () => {
      const result1 = selectNetworkStatus(mockState);
      const result2 = selectNetworkStatus({
        ...mockState,
        network: { ...mockState.network, isOnline: false }
      });
      expect(result1).not.toBe(result2); // Different reference
    });

    it('should not recompute when input selectors return the same value', () => {
      // Create a spy on console.log
      const spy = jest.spyOn(console, 'log').mockImplementation();

      // Log when selector is recomputed
      console.log('Computing selectNetworkStatus');

      // Call selector multiple times with the same state
      selectNetworkStatus(mockState);
      selectNetworkStatus(mockState);
      selectNetworkStatus(mockState);

      // Should only compute once
      expect(spy).toHaveBeenCalledWith('Computing selectNetworkStatus');
      expect(spy).toHaveBeenCalledTimes(1);

      // Restore the spy
      spy.mockRestore();
    });
  });

  describe('New Memoized UI selectors', () => {
    it('should maintain reference equality when state unchanged', () => {
      const result1 = selectUICurrentViewAndPreview(mockState);
      const result2 = selectUICurrentViewAndPreview(mockState);
      expect(result1).toBe(result2);
    });

    it('should select current view and preview mode', () => {
      const result = selectUICurrentViewAndPreview(mockState);
      expect(result).toEqual({
        currentView: 'components',
        previewMode: false
      });
    });

    it('should select sidebar and view state', () => {
      const result = selectUISidebarAndView(mockState);
      expect(result).toEqual({
        sidebarOpen: true,
        currentView: 'components'
      });
    });
  });

  describe('New Memoized WebSocket selectors', () => {
    it('should maintain reference equality when state unchanged', () => {
      const result1 = selectWebSocketConnectionData(mockState);
      const result2 = selectWebSocketConnectionData(mockState);
      expect(result1).toBe(result2);
    });

    it('should select WebSocket connection data', () => {
      const result = selectWebSocketConnectionData(mockState);
      expect(result).toEqual({
        connected: true,
        messages: [
          { id: 'msg1', text: 'Hello' },
          { id: 'msg2', text: 'World' }
        ],
        url: null
      });
    });

    it('should select full WebSocket state', () => {
      const result = selectWebSocketFullState(mockState);
      expect(result).toEqual({
        connected: true,
        connecting: false,
        error: null,
        messages: [
          { id: 'msg1', text: 'Hello' },
          { id: 'msg2', text: 'World' }
        ],
        url: null,
        status: 'disconnected'
      });
    });
  });

  describe('New Memoized App Builder selectors', () => {
    it('should maintain reference equality when state unchanged', () => {
      const result1 = selectAppComponentsAndLayouts(mockState);
      const result2 = selectAppComponentsAndLayouts(mockState);
      expect(result1).toBe(result2);
    });

    it('should select components and layouts', () => {
      const result = selectAppComponentsAndLayouts(mockState);
      expect(result).toEqual({
        components: [
          { id: 'comp1', name: 'Component 1' },
          { id: 'comp2', name: 'Component 2' }
        ],
        layouts: [
          { id: 'layout1', name: 'Layout 1' },
          { id: 'layout2', name: 'Layout 2' }
        ]
      });
    });

    it('should select full app builder state', () => {
      const result = selectAppBuilderState(mockState);
      expect(result).toEqual({
        components: [
          { id: 'comp1', name: 'Component 1' },
          { id: 'comp2', name: 'Component 2' }
        ],
        layouts: [
          { id: 'layout1', name: 'Layout 1' },
          { id: 'layout2', name: 'Layout 2' }
        ],
        styles: {
          '.container': { display: 'flex' },
          '.header': { fontWeight: 'bold' }
        },
        data: {
          users: [{ id: 1, name: 'User 1' }]
        }
      });
    });
  });

  describe('Selector performance optimization tests', () => {
    it('should not create new objects when state is unchanged', () => {
      // Test multiple calls with same state
      const results = [];
      for (let i = 0; i < 5; i++) {
        results.push(selectUICurrentViewAndPreview(mockState));
      }

      // All results should be the same reference
      results.forEach(result => {
        expect(result).toBe(results[0]);
      });
    });

    it('should create new objects only when relevant state changes', () => {
      const result1 = selectUICurrentViewAndPreview(mockState);

      // Change unrelated state
      const modifiedState = {
        ...mockState,
        appData: {
          ...mockState.appData,
          components: [...mockState.appData.components, { id: 'new', name: 'New' }]
        }
      };

      const result2 = selectUICurrentViewAndPreview(modifiedState);

      // Should still be the same reference since UI state didn't change
      expect(result1).toBe(result2);

      // Now change UI state
      const uiChangedState = {
        ...mockState,
        ui: {
          ...mockState.ui,
          currentView: 'layouts'
        }
      };

      const result3 = selectUICurrentViewAndPreview(uiChangedState);

      // Should be different reference now
      expect(result1).not.toBe(result3);
      expect(result3.currentView).toBe('layouts');
    });
  });
});
