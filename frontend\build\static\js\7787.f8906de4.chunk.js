"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[7787],{

/***/ 58865:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {


// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  K: () => (/* binding */ ConnectionState),
  A: () => (/* binding */ services_WebSocketClient)
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/typeof.js
var esm_typeof = __webpack_require__(82284);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(60436);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/classCallCheck.js
var classCallCheck = __webpack_require__(23029);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/createClass.js
var createClass = __webpack_require__(92901);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(64467);
// EXTERNAL MODULE: ./src/config/env.js
var env = __webpack_require__(26390);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(10467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/regenerator/index.js
var regenerator = __webpack_require__(54756);
var regenerator_default = /*#__PURE__*/__webpack_require__.n(regenerator);
// EXTERNAL MODULE: ./src/serviceWorkerRegistration.js + 1 modules
var serviceWorkerRegistration = __webpack_require__(55600);
;// ./src/utils/webSocketErrorHandler.js


/**
 * WebSocket Error Handler
 *
 * This utility provides functions to detect and fix WebSocket connection issues,
 * particularly those related to service worker interference.
 */



/**
 * Error codes that might indicate service worker interference
 */
var SERVICE_WORKER_RELATED_ERRORS = ['NS_ERROR_CORRUPTED_CONTENT', 'NS_ERROR_NET_INADEQUATE_SECURITY', 'SECURITY_ERR', 'NetworkError', 'network error response', 'fetch event', 'resulted in network error', 'promise was rejected'];

/**
 * Check if an error is likely caused by service worker interference
 * @param {Error|string} error - The error object or message
 * @returns {boolean} - True if the error is likely caused by service worker
 */
function isServiceWorkerRelatedError(error) {
  var errorMessage = error instanceof Error ? error.message : String(error);
  return SERVICE_WORKER_RELATED_ERRORS.some(function (code) {
    return errorMessage.includes(code) || errorMessage.toLowerCase().includes('serviceworker') || errorMessage.toLowerCase().includes('service worker');
  });
}

/**
 * Handle WebSocket connection errors
 * @param {Error|string} error - The error that occurred
 * @returns {Promise<boolean>} - True if the error was handled
 */
function handleWebSocketError(_x) {
  return _handleWebSocketError.apply(this, arguments);
}

/**
 * Add global error handler for WebSocket issues
 */
function _handleWebSocketError() {
  _handleWebSocketError = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee(error) {
    var shouldFix;
    return regenerator_default().wrap(function (_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          console.error('WebSocket error occurred:', error);
          if (!isServiceWorkerRelatedError(error)) {
            _context.next = 1;
            break;
          }
          console.log('Detected service worker related WebSocket error');

          // Show a user-friendly error message
          shouldFix = window.confirm('A service worker is interfering with the WebSocket connection. ' + 'Would you like to fix this issue? This will refresh the page.');
          if (!shouldFix) {
            _context.next = 1;
            break;
          }
          return _context.abrupt("return", (0,serviceWorkerRegistration/* fixWebSocketIssues */.jY)());
        case 1:
          return _context.abrupt("return", false);
        case 2:
        case "end":
          return _context.stop();
      }
    }, _callee);
  }));
  return _handleWebSocketError.apply(this, arguments);
}
function setupGlobalWebSocketErrorHandler() {
  // Listen for unhandled errors that might be WebSocket related
  window.addEventListener('error', function (event) {
    if (event.message && (event.message.includes('WebSocket') || isServiceWorkerRelatedError(event.message))) {
      console.log('Caught global WebSocket error:', event.message);
      handleWebSocketError(event.message);
    }
  });

  // Listen for unhandled promise rejections
  window.addEventListener('unhandledrejection', function (event) {
    var error = event.reason;
    if (error && (String(error).includes('WebSocket') || isServiceWorkerRelatedError(error))) {
      console.log('Caught unhandled WebSocket promise rejection:', error);
      handleWebSocketError(error);
    }
  });

  // Patch the WebSocket constructor to catch errors
  var OriginalWebSocket = window.WebSocket;
  window.WebSocket = function (url, protocols) {
    try {
      return new OriginalWebSocket(url, protocols);
    } catch (error) {
      console.error('Error creating WebSocket:', error);
      if (isServiceWorkerRelatedError(error)) {
        handleWebSocketError(error);
      }
      throw error;
    }
  };

  // Copy all properties from the original WebSocket
  for (var prop in OriginalWebSocket) {
    if (OriginalWebSocket.hasOwnProperty(prop)) {
      window.WebSocket[prop] = OriginalWebSocket[prop];
    }
  }
  window.WebSocket.prototype = OriginalWebSocket.prototype;
  console.log('Global WebSocket error handler set up');
}

/**
 * Specifically handle the "fetch event resulted in network error" issue
 * @param {string} url - The URL that caused the error
 * @returns {Promise<boolean>} - True if the error was handled
 */
function handleFetchEventNetworkError(_x2) {
  return _handleFetchEventNetworkError.apply(this, arguments);
}
function _handleFetchEventNetworkError() {
  _handleFetchEventNetworkError = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee2(url) {
    var isWebSocketUrl, shouldFix;
    return regenerator_default().wrap(function (_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          console.error("Fetch event for \"".concat(url, "\" resulted in network error response, the promise was rejected"));

          // Check if this is a WebSocket URL
          isWebSocketUrl = url.startsWith('ws:') || url.startsWith('wss:') || url.includes('/ws/') || url.includes('/socket');
          if (!isWebSocketUrl) {
            _context2.next = 2;
            break;
          }
          console.log('Detected WebSocket URL with fetch event network error');

          // Show a user-friendly error message
          shouldFix = window.confirm('A service worker is preventing WebSocket connections. ' + 'Would you like to fix this issue? This will refresh the page.');
          if (!shouldFix) {
            _context2.next = 2;
            break;
          }
          _context2.next = 1;
          return (0,serviceWorkerRegistration/* forceCleanServiceWorkers */.hl)();
        case 1:
          // If that doesn't work, try to fix WebSocket issues
          setTimeout(function () {
            (0,serviceWorkerRegistration/* fixWebSocketIssues */.jY)();
          }, 1000);
          return _context2.abrupt("return", true);
        case 2:
          return _context2.abrupt("return", false);
        case 3:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  }));
  return _handleFetchEventNetworkError.apply(this, arguments);
}
/* harmony default export */ const webSocketErrorHandler = ({
  handleWebSocketError: handleWebSocketError,
  isServiceWorkerRelatedError: isServiceWorkerRelatedError,
  setupGlobalWebSocketErrorHandler: setupGlobalWebSocketErrorHandler,
  handleFetchEventNetworkError: handleFetchEventNetworkError
});
;// ./src/services/WebSocketClient.js





function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t["return"] || t["return"](); } finally { if (u) throw o; } } }; }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,defineProperty/* default */.A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * WebSocketClient
 *
 * A robust WebSocket client implementation with automatic reconnection,
 * event handling, and message queuing.
 */



// WebSocket connection states
var ConnectionState = {
  CONNECTING: 0,
  OPEN: 1,
  CLOSING: 2,
  CLOSED: 3,
  RECONNECTING: 4
};

/**
 * Utility function to capture and format a stack trace
 * @returns {string} Formatted stack trace
 */
function captureStackTrace() {
  var error = new Error();
  Error.captureStackTrace(error, captureStackTrace);
  return error.stack;
}
var WebSocketClient = /*#__PURE__*/function () {
  /**
   * Create a new WebSocketClient
   * @param {Object} options - Configuration options
   * @param {string} options.url - WebSocket URL (optional, can be set later)
   * @param {number} options.reconnectInterval - Initial reconnect interval in ms (default: 1000)
   * @param {number} options.maxReconnectInterval - Maximum reconnect interval in ms (default: 30000)
   * @param {number} options.reconnectDecay - Exponential backoff factor (default: 1.5)
   * @param {number} options.maxReconnectAttempts - Maximum reconnect attempts (default: 10)
   * @param {boolean} options.debug - Enable debug logging (default: false)
   * @param {boolean} options.automaticOpen - Automatically open connection (default: true)
   */
  function WebSocketClient() {
    var _this = this;
    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    (0,classCallCheck/* default */.A)(this, WebSocketClient);
    /**
     * Enhanced logging system
     * @private
     */
    (0,defineProperty/* default */.A)(this, "logger", {
      _formatMessage: function _formatMessage(level) {
        var timestamp = new Date().toISOString();
        for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
          args[_key - 1] = arguments[_key];
        }
        return ["[".concat(timestamp, "][WebSocketClient:").concat(_this.connectionId, "][").concat(level, "]")].concat(args);
      },
      debug: function debug() {
        if (_this.currentLogLevel <= _this.logLevels.DEBUG) {
          var _console, _this$logger;
          for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
            args[_key2] = arguments[_key2];
          }
          (_console = console).debug.apply(_console, (0,toConsumableArray/* default */.A)((_this$logger = _this.logger)._formatMessage.apply(_this$logger, ['DEBUG'].concat(args))));
        }
      },
      info: function info() {
        if (_this.currentLogLevel <= _this.logLevels.INFO) {
          var _console2, _this$logger2;
          for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {
            args[_key3] = arguments[_key3];
          }
          (_console2 = console).info.apply(_console2, (0,toConsumableArray/* default */.A)((_this$logger2 = _this.logger)._formatMessage.apply(_this$logger2, ['INFO'].concat(args))));
        }
      },
      warn: function warn() {
        if (_this.currentLogLevel <= _this.logLevels.WARN) {
          var _console3, _this$logger3;
          for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {
            args[_key4] = arguments[_key4];
          }
          (_console3 = console).warn.apply(_console3, (0,toConsumableArray/* default */.A)((_this$logger3 = _this.logger)._formatMessage.apply(_this$logger3, ['WARN'].concat(args))));
        }
      },
      error: function error() {
        if (_this.currentLogLevel <= _this.logLevels.ERROR) {
          var _console4, _this$logger4;
          for (var _len5 = arguments.length, args = new Array(_len5), _key5 = 0; _key5 < _len5; _key5++) {
            args[_key5] = arguments[_key5];
          }
          (_console4 = console).error.apply(_console4, (0,toConsumableArray/* default */.A)((_this$logger4 = _this.logger)._formatMessage.apply(_this$logger4, ['ERROR'].concat(args))));
        }
      },
      group: function group(label) {
        if (_this.debug) {
          console.group("[WebSocketClient:".concat(_this.connectionId, "] ").concat(label));
        }
      },
      groupEnd: function groupEnd() {
        if (_this.debug) {
          console.groupEnd();
        }
      }
    });
    /**
     * Set the connection state and dispatch state change event
     * @private
     * @param {number} state - New connection state
     */
    (0,defineProperty/* default */.A)(this, "setConnectionState", function (state) {
      var previousState = _this.connectionState;
      _this.connectionState = state;
      _this.dispatchEvent('state_change', {
        previousState: previousState,
        currentState: state
      });
    });
    /**
     * Open the WebSocket connection
     * @param {string} url - WebSocket URL (optional if already set)
     * @returns {WebSocketClient} this instance for chaining
     */
    (0,defineProperty/* default */.A)(this, "open", function (url) {
      console.log('open method called with url:', url);
      console.log('this in open method before any operations:', _this);

      // Update URL if provided
      if (url) {
        _this.url = url;
      }

      // Validate URL
      if (!_this.url) {
        console.error('WebSocketClient: URL not set in open method');
        throw new Error('WebSocket URL not set');
      }
      console.log('URL after validation:', _this.url);

      // Close existing connection if any
      if (_this.socket && (_this.socket.readyState === WebSocket.OPEN || _this.socket.readyState === WebSocket.CONNECTING)) {
        _this.log('Closing existing connection before opening a new one');
        _this.socket.close();
      }

      // Log the URL we're connecting to for debugging
      _this.log("Opening connection to ".concat(_this.url));
      console.log("WebSocketClient: Connecting to ".concat(_this.url));

      // Log network status
      console.log("WebSocketClient: Network status - Online: ".concat(navigator.onLine));

      // Check if the URL is valid
      try {
        var urlObj = new URL(_this.url);
        console.log("WebSocketClient: URL parsed successfully - Protocol: ".concat(urlObj.protocol, ", Host: ").concat(urlObj.host, ", Path: ").concat(urlObj.pathname));
      } catch (error) {
        console.error("WebSocketClient: Invalid URL - ".concat(_this.url), error);
      }
      _this.setConnectionState(ConnectionState.CONNECTING);
      try {
        console.log('Creating new WebSocket object with URL:', _this.url);
        console.log('this before WebSocket creation:', _this);

        // Create new WebSocket without explicit protocols to improve compatibility
        _this.socket = new WebSocket(_this.url);

        // Set binary type to arraybuffer for more reliable binary data handling
        _this.socket.binaryType = 'arraybuffer';
        console.log('WebSocket object created:', _this.socket);
        console.log('WebSocket readyState:', _this.socket.readyState);

        // Set up connection timeout
        console.log('Setting up connection timeout of', _this.connectionTimeout, 'ms');
        _this.connectionTimeoutId = setTimeout(function () {
          console.log('Connection timeout callback triggered');
          console.log('this in timeout callback:', _this);
          if (_this.socket && _this.socket.readyState !== WebSocket.OPEN) {
            console.warn("WebSocketClient: Connection timeout after ".concat(_this.connectionTimeout, "ms"));
            _this.socket.close(4000, 'Connection timeout');
            _this.dispatchEvent('error', new Error('Connection timeout'));
          }
        }, _this.connectionTimeout);

        // Log event handlers before binding
        console.log('Event handlers before binding:');
        console.log('onOpen:', _this.onOpen);
        console.log('onMessage:', _this.onMessage);
        console.log('onClose:', _this.onClose);
        console.log('onError:', _this.onError);

        // Set up event handlers (already bound in constructor)
        _this.socket.onopen = _this.onOpen;
        _this.socket.onmessage = _this.onMessage;
        _this.socket.onclose = _this.onClose;
        _this.socket.onerror = _this.onError;
        console.log('Event handlers after binding:');
        console.log('socket.onopen:', _this.socket.onopen);
        console.log('socket.onmessage:', _this.socket.onmessage);
        console.log('socket.onclose:', _this.socket.onclose);
        console.log('socket.onerror:', _this.socket.onerror);
        return _this;
      } catch (error) {
        _this.log('Error creating WebSocket:', error);

        // Capture stack trace for debugging
        var stack = captureStackTrace();
        console.error('WebSocketClient: Error creating connection:', error);
        console.error('WebSocketClient: Connection error stack trace:', stack);

        // Add stack trace to error object
        if (error && (0,esm_typeof/* default */.A)(error) === 'object') {
          error.stack = error.stack || stack;
        }

        // Check if this is a service worker related error
        if (isServiceWorkerRelatedError(error)) {
          console.warn('WebSocketClient: Detected service worker related error');
          // Handle the service worker error
          handleWebSocketError(error).then(function (fixed) {
            if (!fixed) {
              // If not fixed by the handler, dispatch error and try to reconnect
              _this.dispatchEvent('error', error);
              _this.reconnect();
            }
          });
        } else {
          // Regular error handling
          _this.dispatchEvent('error', error);
          _this.reconnect();
        }
        return _this;
      }
    });
    /**
     * Close the WebSocket connection
     * @param {number} code - Close code (default: 1000)
     * @param {string} reason - Close reason
     * @returns {WebSocketClient} this instance for chaining
     */
    (0,defineProperty/* default */.A)(this, "close", function () {
      var code = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 1000;
      var reason = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'Normal closure';
      if (_this.socket) {
        _this.log("Closing connection with code ".concat(code, ": ").concat(reason));

        // Stop heartbeat before closing
        _this.log('Stopping heartbeat');
        _this.stopHeartbeat();
        _this.setConnectionState(ConnectionState.CLOSING);
        _this.socket.close(code, reason);
      }
      return _this;
    });
    /**
     * Clean up resources and event listeners
     * @private
     */
    (0,defineProperty/* default */.A)(this, "cleanup", function () {
      // Remove the beforeunload event listener
      window.removeEventListener('beforeunload', _this.handleBeforeUnload);

      // Clear any pending timeouts
      if (_this.connectionTimeoutId) {
        clearTimeout(_this.connectionTimeoutId);
        _this.connectionTimeoutId = null;
      }
      if (_this.reconnectTimeoutId) {
        clearTimeout(_this.reconnectTimeoutId);
        _this.reconnectTimeoutId = null;
      }

      // Stop heartbeat
      _this.stopHeartbeat();
    });
    /**
     * Start sending heartbeat messages to keep the connection alive
     * @private
     */
    (0,defineProperty/* default */.A)(this, "startHeartbeat", function () {
      // Only start heartbeat if enabled and connection is open
      if (!_this.enableHeartbeat || _this.connectionState !== ConnectionState.OPEN) {
        return;
      }

      // Clear any existing heartbeat timeout
      if (_this.heartbeatTimeoutId) {
        clearTimeout(_this.heartbeatTimeoutId);
      }

      // Schedule the next heartbeat
      _this.heartbeatTimeoutId = setTimeout(function () {
        // Only send if connection is still open
        if (_this.connectionState === ConnectionState.OPEN) {
          try {
            // Update timestamp in heartbeat message
            var heartbeat = (0,esm_typeof/* default */.A)(_this.heartbeatMessage) === 'object' ? _objectSpread(_objectSpread({}, _this.heartbeatMessage), {}, {
              timestamp: new Date().toISOString()
            }) : _this.heartbeatMessage;

            // Send the heartbeat
            _this.send(heartbeat);
            _this.logger.debug('Heartbeat sent', {
              timestamp: new Date().toISOString()
            });

            // Schedule the next heartbeat
            _this.startHeartbeat();
          } catch (error) {
            _this.logger.error('Failed to send heartbeat', {
              error: error
            });
            // Try to reconnect if sending heartbeat fails
            _this.reconnect();
          }
        }
      }, _this.heartbeatInterval);
    });
    /**
     * Stop sending heartbeat messages
     * @private
     */
    (0,defineProperty/* default */.A)(this, "stopHeartbeat", function () {
      if (_this.heartbeatTimeoutId) {
        clearTimeout(_this.heartbeatTimeoutId);
        _this.heartbeatTimeoutId = null;
      }
    });
    /**
     * Send a message through the WebSocket
     * @param {Object|string|ArrayBuffer|Blob} data - Data to send
     * @returns {boolean} true if sent, false if queued or failed
     */
    (0,defineProperty/* default */.A)(this, "send", function (data) {
      _this.logger.group('Sending Message');
      try {
        // Validate the data before sending
        if (data === undefined || data === null) {
          _this.logger.error('Cannot send undefined or null data');
          return false;
        }

        // Prepare the message based on data type
        var message;
        if (data instanceof ArrayBuffer || data instanceof Blob) {
          // Binary data can be sent directly
          message = data;
          _this.logger.debug('Sending binary data', {
            type: data instanceof ArrayBuffer ? 'ArrayBuffer' : 'Blob',
            size: data instanceof ArrayBuffer ? data.byteLength : data.size
          });
        } else if ((0,esm_typeof/* default */.A)(data) === 'object') {
          // Convert objects to JSON strings
          try {
            message = JSON.stringify(data);
          } catch (jsonError) {
            _this.logger.error('Failed to stringify object', {
              error: jsonError
            });
            return false;
          }
        } else {
          // Use data as is for strings and other primitives
          message = data;
        }

        // Check connection state before sending
        if (_this.connectionState === ConnectionState.OPEN) {
          // Check if the socket is actually open
          if (_this.socket && _this.socket.readyState === WebSocket.OPEN) {
            // Send the message
            _this.socket.send(message);

            // Log success with appropriate details
            if (typeof message === 'string') {
              _this.logger.debug('Message sent successfully', {
                dataType: (0,esm_typeof/* default */.A)(data),
                dataLength: message.length,
                preview: message.length > 100 ? message.substring(0, 97) + '...' : message
              });
            } else {
              _this.logger.debug('Binary message sent successfully', {
                dataType: message instanceof ArrayBuffer ? 'ArrayBuffer' : 'Blob',
                dataSize: message instanceof ArrayBuffer ? message.byteLength : message.size
              });
            }
            return true;
          } else {
            // Socket is not actually open despite the connection state
            _this.logger.warn('Socket not open despite connection state', {
              connectionState: _this.connectionState,
              socketReadyState: _this.socket ? _this.socket.readyState : 'no socket',
              messageQueued: true
            });

            // Queue the message
            _this.messageQueue.push(message);
            return false;
          }
        } else {
          // Connection is not open
          _this.logger.warn('Cannot send - connection not open', {
            connectionState: _this.connectionState,
            messageQueued: true
          });

          // Queue the message
          _this.messageQueue.push(message);
          return false;
        }
      } catch (error) {
        _this.logger.error('Failed to send message', {
          error: error,
          errorMessage: error.message,
          errorStack: error.stack,
          data: (0,esm_typeof/* default */.A)(data) === 'object' ? 'object' : data,
          connectionState: _this.connectionState
        });
        return false;
      } finally {
        _this.logger.groupEnd();
      }
    });
    /**
     * Attempt to reconnect
     * @private
     */
    (0,defineProperty/* default */.A)(this, "reconnect", function () {
      // Skip if already reconnecting or closing
      if (_this.connectionState === ConnectionState.RECONNECTING || _this.connectionState === ConnectionState.CLOSING) {
        return;
      }

      // Check if max reconnect attempts reached
      if (_this.reconnectAttempts >= _this.maxReconnectAttempts) {
        _this.log('Max reconnect attempts reached');
        _this.dispatchEvent('reconnect_failed');
        return;
      }
      _this.reconnectAttempts++;
      _this.setConnectionState(ConnectionState.RECONNECTING);

      // Calculate reconnect delay with exponential backoff
      var delay = Math.min(_this.reconnectInterval * Math.pow(_this.reconnectDecay, _this.reconnectAttempts - 1), _this.maxReconnectInterval);
      _this.log("Reconnecting in ".concat(delay, "ms (attempt ").concat(_this.reconnectAttempts, "/").concat(_this.maxReconnectAttempts, ")"));
      _this.dispatchEvent('reconnect_attempt', {
        attempt: _this.reconnectAttempts,
        delay: delay
      });

      // Schedule reconnect
      setTimeout(function () {
        _this.log("Attempting reconnect ".concat(_this.reconnectAttempts, "/").concat(_this.maxReconnectAttempts));
        try {
          _this.open();
        } catch (error) {
          _this.log('Reconnect error:', error);
          _this.dispatchEvent('reconnect_error', error);
          _this.reconnect();
        }
      }, delay);
    });
    /**
     * Handle WebSocket open event
     * @private
     * @param {Event} event - WebSocket event
     */
    (0,defineProperty/* default */.A)(this, "onOpen", function (event) {
      console.log('onOpen event handler called with event:', event);
      console.log('this in onOpen handler:', _this);
      console.log('Current socket:', _this.socket);
      console.log('Current connection state:', _this.connectionState);
      _this.log('Connection opened');

      // Clear connection timeout
      if (_this.connectionTimeoutId) {
        console.log('Clearing connection timeout:', _this.connectionTimeoutId);
        clearTimeout(_this.connectionTimeoutId);
        _this.connectionTimeoutId = null;
      }
      console.log('Setting connection state to OPEN');
      _this.setConnectionState(ConnectionState.OPEN);
      _this.reconnectAttempts = 0;

      // Process queued messages
      if (_this.messageQueue.length > 0) {
        _this.log("Processing ".concat(_this.messageQueue.length, " queued messages"));

        // Create a copy of the queue to avoid issues if new messages are queued during processing
        var queueCopy = (0,toConsumableArray/* default */.A)(_this.messageQueue);
        _this.messageQueue = [];

        // Process each message
        var _iterator = _createForOfIteratorHelper(queueCopy),
          _step;
        try {
          for (_iterator.s(); !(_step = _iterator.n()).done;) {
            var message = _step.value;
            try {
              if (_this.socket && _this.socket.readyState === WebSocket.OPEN) {
                _this.socket.send(message);
                _this.log('Queued message sent successfully');
              } else {
                _this.log('Socket not open while processing queue, re-queuing message');
                _this.messageQueue.push(message);
              }
            } catch (error) {
              _this.log('Error sending queued message:', error);
              // Re-queue the message if it's a temporary error
              if (error.name !== 'SyntaxError' && error.name !== 'TypeError') {
                _this.messageQueue.push(message);
              }
            }
          }
        } catch (err) {
          _iterator.e(err);
        } finally {
          _iterator.f();
        }
        _this.log("Queue processing complete. ".concat(_this.messageQueue.length, " messages re-queued"));
      }

      // Start heartbeat to keep connection alive
      if (_this.enableHeartbeat) {
        _this.log('Starting heartbeat');
        _this.startHeartbeat();
      }

      // Dispatch event
      _this.dispatchEvent('open', event);
    });
    /**
     * Handle WebSocket message event
     * @private
     * @param {MessageEvent} event - WebSocket message event
     */
    (0,defineProperty/* default */.A)(this, "onMessage", function (event) {
      try {
        // Check if the data is a Blob or ArrayBuffer (binary data)
        if (event.data instanceof Blob || event.data instanceof ArrayBuffer) {
          _this.log('Binary message received');
          // Handle binary data - convert to text if needed
          _this.handleBinaryMessage(event);
          return;
        }

        // Log the received message (only for text data)
        _this.log('Message received:', typeof event.data === 'string' ? event.data.substring(0, 100) + (event.data.length > 100 ? '...' : '') : event.data);

        // Validate that the data is not corrupted
        if (typeof event.data !== 'string') {
          _this.log('Non-string data received:', (0,esm_typeof/* default */.A)(event.data));
          _this.dispatchEvent('message', {
            originalEvent: event,
            data: event.data
          });
          return;
        }

        // Check for empty or invalid data
        if (!event.data || event.data.trim() === '') {
          _this.log('Empty message received');
          _this.dispatchEvent('message', {
            originalEvent: event,
            data: ''
          });
          return;
        }
        try {
          // Parse JSON if possible
          var data = JSON.parse(event.data);
          _this.dispatchEvent('message', {
            originalEvent: event,
            data: data
          });
        } catch (error) {
          _this.log('Error parsing JSON message:', error);
          // Dispatch raw data if not JSON
          _this.dispatchEvent('message', {
            originalEvent: event,
            data: event.data,
            parseError: error.message
          });
        }
      } catch (error) {
        _this.log('Error in onMessage handler:', error);
        // Dispatch an error event
        _this.dispatchEvent('error', {
          message: 'Error processing WebSocket message',
          error: error.message,
          originalEvent: event
        });
      }
    });
    /**
     * Handle binary WebSocket messages
     * @private
     * @param {MessageEvent} event - WebSocket message event with binary data
     */
    (0,defineProperty/* default */.A)(this, "handleBinaryMessage", function (event) {
      var reader = new FileReader();
      reader.onload = function () {
        try {
          var text = reader.result;
          _this.log('Binary message converted to text:', text.substring(0, 100) + (text.length > 100 ? '...' : ''));
          try {
            // Try to parse as JSON
            var data = JSON.parse(text);
            _this.dispatchEvent('message', {
              originalEvent: event,
              data: data,
              binary: true
            });
          } catch (error) {
            // Dispatch raw text if not JSON
            _this.dispatchEvent('message', {
              originalEvent: event,
              data: text,
              binary: true,
              parseError: error.message
            });
          }
        } catch (error) {
          _this.log('Error processing binary message:', error);
          _this.dispatchEvent('error', {
            message: 'Error processing binary WebSocket message',
            error: error.message,
            originalEvent: event
          });
        }
      };
      reader.onerror = function (error) {
        _this.log('Error reading binary message:', error);
        _this.dispatchEvent('error', {
          message: 'Error reading binary WebSocket message',
          error: error.message,
          originalEvent: event
        });
      };

      // Read the binary data as text
      if (event.data instanceof Blob) {
        reader.readAsText(event.data);
      } else if (event.data instanceof ArrayBuffer) {
        var text = new TextDecoder().decode(event.data);
        try {
          var data = JSON.parse(text);
          _this.dispatchEvent('message', {
            originalEvent: event,
            data: data,
            binary: true
          });
        } catch (error) {
          _this.dispatchEvent('message', {
            originalEvent: event,
            data: text,
            binary: true,
            parseError: error.message
          });
        }
      }
    });
    /**
     * Handle WebSocket close event
     * @private
     * @param {CloseEvent} event - WebSocket close event
     */
    (0,defineProperty/* default */.A)(this, "onClose", function (event) {
      // Create a more detailed close information object
      var closeInfo = {
        code: event.code,
        reason: event.reason || 'No reason provided',
        wasClean: event.wasClean,
        timestamp: new Date().toISOString(),
        url: _this.url,
        networkOnline: navigator.onLine,
        connectionState: _this.connectionState
      };

      // Clear connection timeout
      if (_this.connectionTimeoutId) {
        clearTimeout(_this.connectionTimeoutId);
        _this.connectionTimeoutId = null;
      }
      _this.log("Connection closed: ".concat(closeInfo.code, " ").concat(closeInfo.reason, " (clean: ").concat(closeInfo.wasClean, ")"));
      _this.setConnectionState(ConnectionState.CLOSED);

      // Dispatch event with detailed information
      _this.dispatchEvent('close', closeInfo);

      // Clean up resources
      _this.cleanup();

      // Determine if we should reconnect
      var isNormalClosure = event.code === 1000 || event.code === 1001;
      var isAbnormalClosure = event.code === 1006;
      var isServerError = event.code >= 1011 && event.code <= 1015;

      // Enhanced handling for code 1006 (abnormal closure)
      if (isAbnormalClosure) {
        console.warn('WebSocketClient: Abnormal closure (code 1006) detected');
        console.log('WebSocketClient: Network status:', navigator.onLine ? 'Online' : 'Offline');

        // Check if we're online
        if (!navigator.onLine) {
          console.warn('WebSocketClient: Network appears to be offline, waiting for online status');

          // Add event listener for when we come back online
          var _onlineHandler = function onlineHandler() {
            console.log('WebSocketClient: Network is back online, attempting to reconnect');
            window.removeEventListener('online', _onlineHandler);
            setTimeout(function () {
              return _this.reconnect();
            }, 1000); // Wait a second for network to stabilize
          };
          window.addEventListener('online', _onlineHandler);
          return; // Don't attempt to reconnect now
        }

        // If we're online, try to reconnect with a short delay
        console.log('WebSocketClient: Network is online, attempting immediate reconnect');
        _this.reconnectInterval = 1000; // Use a short interval for first attempt
        setTimeout(function () {
          return _this.reconnect();
        }, 250); // Try almost immediately
        return;
      }

      // Handle server errors with exponential backoff
      if (isServerError) {
        console.warn("WebSocketClient: Server error (code ".concat(event.code, ") detected, using exponential backoff"));
        // Use current reconnect interval (will increase with each attempt)
        _this.reconnect();
        return;
      }

      // Handle other non-normal closures
      if (!isNormalClosure) {
        console.log("WebSocketClient: Non-normal closure (code ".concat(event.code, "), scheduling reconnect"));
        _this.reconnect();
      } else {
        console.log("WebSocketClient: Not reconnecting after clean close (".concat(event.code, ")"));
      }
    });
    /**
     * Handle WebSocket error event
     * @private
     * @param {Event} event - WebSocket error event
     */
    (0,defineProperty/* default */.A)(this, "onError", function (event) {
      // Safely log error details
      try {
        console.log('WebSocket error occurred:', event);

        // Clear connection timeout
        if (_this.connectionTimeoutId) {
          console.log('Clearing connection timeout in onError:', _this.connectionTimeoutId);
          clearTimeout(_this.connectionTimeoutId);
          _this.connectionTimeoutId = null;
        }

        // Check if we're online
        var isOnline = navigator && typeof navigator.onLine === 'boolean' ? navigator.onLine : true;

        // Create a more detailed error object
        var errorDetails = {
          originalEvent: event,
          message: 'WebSocket connection error',
          timestamp: new Date().toISOString(),
          connectionState: _this.connectionState,
          url: _this.url,
          reconnectAttempts: _this.reconnectAttempts,
          maxReconnectAttempts: _this.maxReconnectAttempts,
          browser: {
            online: isOnline
          }
        };

        // Log detailed error information
        console.error('WebSocketClient error details:', errorDetails);

        // Dispatch the error event with detailed information
        _this.dispatchEvent('error', errorDetails);

        // Attempt to reconnect if not already reconnecting
        if (_this.connectionState !== ConnectionState.RECONNECTING) {
          _this.reconnect();
        }
      } catch (handlerError) {
        // Last resort error handling
        console.error('Error in WebSocket error handler:', handlerError);
      }
    });
    /**
     * Handle beforeunload event to close the WebSocket connection cleanly
     * @private
     * @param {Event} _event - BeforeUnload event (unused)
     */
    (0,defineProperty/* default */.A)(this, "handleBeforeUnload", function (_event) {
      if (_this.socket && _this.connectionState === ConnectionState.OPEN) {
        _this.log('Page unloading, closing WebSocket connection');
        _this.close(1000, 'Page unloaded');
      }
    });
    console.log('WebSocketClient constructor called with options:', options);
    console.log('this in constructor:', this);

    // Validate URL if provided
    if (options.url) {
      try {
        // Validate URL format
        var urlObj = new URL(options.url);

        // Validate protocol
        if (urlObj.protocol !== 'ws:' && urlObj.protocol !== 'wss:') {
          console.warn("WebSocketClient: Invalid protocol - ".concat(urlObj.protocol, ". URL should use ws:// or wss://"));
          // Auto-correct the protocol if possible
          if (urlObj.protocol === 'http:') {
            options.url = options.url.replace('http:', 'ws:');
            console.log("WebSocketClient: Corrected URL to ".concat(options.url));
          } else if (urlObj.protocol === 'https:') {
            options.url = options.url.replace('https:', 'wss:');
            console.log("WebSocketClient: Corrected URL to ".concat(options.url));
          }
        }
      } catch (error) {
        console.error("WebSocketClient: Invalid URL - ".concat(options.url), error);
        // Don't throw here, just log the error and continue with null URL
        // The open method will validate again before connecting
        options.url = null;
      }
    }
    this.url = options.url || null;
    this.reconnectInterval = options.reconnectInterval || 1000;
    this.maxReconnectInterval = options.maxReconnectInterval || 30000;
    this.reconnectDecay = options.reconnectDecay || 1.5;
    this.maxReconnectAttempts = options.maxReconnectAttempts || 10;
    this.debug = options.debug || false;
    this.automaticOpen = options.automaticOpen !== false;
    this.connectionTimeout = options.connectionTimeout || 5000; // Add connection timeout

    // Heartbeat configuration
    this.enableHeartbeat = options.enableHeartbeat !== false;
    this.heartbeatInterval = options.heartbeatInterval || 30000; // 30 seconds by default
    this.heartbeatMessage = options.heartbeatMessage || {
      type: 'ping',
      timestamp: new Date().toISOString()
    };
    this.heartbeatTimeoutId = null;

    // Enhanced logging configuration
    this.connectionId = Math.random().toString(36).substring(2, 11);
    this.logLevels = {
      DEBUG: 0,
      INFO: 1,
      WARN: 2,
      ERROR: 3
    };
    this.currentLogLevel = this.debug ? this.logLevels.DEBUG : this.logLevels.INFO;
    this.socket = null;
    this.connectionState = ConnectionState.CLOSED;
    this.reconnectAttempts = 0;
    this.messageQueue = [];
    this.eventListeners = {
      open: [],
      message: [],
      close: [],
      error: [],
      reconnect: [],
      reconnect_attempt: [],
      reconnect_error: [],
      reconnect_failed: [],
      state_change: []
    };

    // No need to bind methods when using arrow functions as class properties

    // Add beforeunload event listener to close the connection when the page is unloaded
    window.addEventListener('beforeunload', this.handleBeforeUnload);

    // Automatically open connection if URL is provided
    if (this.url && this.automaticOpen) {
      this.open();
    }
  }
  return (0,createClass/* default */.A)(WebSocketClient, [{
    key: "addEventListener",
    value:
    /**
     * Add an event listener
     * @param {string} type - Event type
     * @param {Function} listener - Event listener
     * @param {Object} options - Listener options
     * @param {boolean} options.once - Remove listener after first call
     * @returns {Function} Function to remove the listener
     */
    function addEventListener(type, listener) {
      var _this2 = this;
      var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
      if (!this.eventListeners[type]) {
        this.eventListeners[type] = [];
      }
      var listenerObj = {
        callback: listener,
        once: options.once || false
      };
      this.eventListeners[type].push(listenerObj);

      // Return function to remove the listener
      return function () {
        return _this2.removeEventListener(type, listener);
      };
    }

    /**
     * Remove an event listener
     * @param {string} type - Event type
     * @param {Function} listener - Event listener
     * @returns {boolean} true if removed, false if not found
     */
  }, {
    key: "removeEventListener",
    value: function removeEventListener(type, listener) {
      if (!this.eventListeners[type]) {
        return false;
      }
      var initialLength = this.eventListeners[type].length;
      this.eventListeners[type] = this.eventListeners[type].filter(function (l) {
        return l.callback !== listener;
      });
      return initialLength !== this.eventListeners[type].length;
    }

    /**
     * Dispatch an event to all listeners
     * @private
     * @param {string} type - Event type
     * @param {*} data - Event data
     */
  }, {
    key: "dispatchEvent",
    value: function dispatchEvent(type, data) {
      if (!this.eventListeners[type]) {
        return;
      }

      // Create a copy of the listeners array to avoid issues if listeners are removed during iteration
      var listeners = (0,toConsumableArray/* default */.A)(this.eventListeners[type]);

      // Call each listener
      listeners.forEach(function (listener) {
        try {
          listener.callback(data);
        } catch (error) {
          console.error("Error in ".concat(type, " listener:"), error);
        }
      });

      // Remove one-time listeners
      this.eventListeners[type] = this.eventListeners[type].filter(function (listener) {
        return !listener.once;
      });
    }

    /**
     * Get the current connection state
     * @returns {number} Connection state
     */
  }, {
    key: "getState",
    value: function getState() {
      return this.connectionState;
    }

    /**
     * Check if the connection is open
     * @returns {boolean} true if open
     */
  }, {
    key: "isOpen",
    value: function isOpen() {
      return this.connectionState === ConnectionState.OPEN;
    }

    /**
     * Check if the connection is connecting
     * @returns {boolean} true if connecting
     */
  }, {
    key: "isConnecting",
    value: function isConnecting() {
      return this.connectionState === ConnectionState.CONNECTING || this.connectionState === ConnectionState.RECONNECTING;
    }

    /**
     * Check if the connection is closed
     * @returns {boolean} true if closed
     */
  }, {
    key: "isClosed",
    value: function isClosed() {
      return this.connectionState === ConnectionState.CLOSED || this.connectionState === ConnectionState.CLOSING;
    }
  }], [{
    key: "forEndpoint",
    value:
    /**
     * Create a WebSocketClient for a specific endpoint
     * @static
     * @param {string} endpoint - WebSocket endpoint (e.g., 'app_builder', 'test')
     * @param {Object} options - Client options
     * @returns {WebSocketClient} WebSocketClient instance
     */
    function forEndpoint(endpoint) {
      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
      var url = (0,env/* getWebSocketUrl */.$0)(endpoint);
      console.log("Creating WebSocketClient for endpoint '".concat(endpoint, "' with URL: ").concat(url));
      return new WebSocketClient(_objectSpread(_objectSpread({}, options), {}, {
        url: url,
        debug: true
      }));
    }
  }]);
}();
/* harmony default export */ const services_WebSocketClient = (WebSocketClient);

/***/ }),

/***/ 97787:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   A: () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5544);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(96540);
/* harmony import */ var _services_WebSocketClient__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(58865);

/**
 * useWebSocket Hook
 *
 * A React hook for using WebSockets in components.
 */



/**
 * Hook for using WebSockets in React components
 * @param {string|Object} options - WebSocket URL or options object
 * @param {string} options.url - WebSocket URL
 * @param {string} options.endpoint - WebSocket endpoint (alternative to URL)
 * @param {boolean} options.autoConnect - Automatically connect on mount (default: true)
 * @param {boolean} options.reconnect - Automatically reconnect on close (default: true)
 * @param {number} options.reconnectInterval - Initial reconnect interval in ms (default: 1000)
 * @param {number} options.maxReconnectAttempts - Maximum reconnect attempts (default: 10)
 * @param {boolean} options.debug - Enable debug logging (default: false)
 * @param {Function} options.onOpen - Callback when connection opens
 * @param {Function} options.onMessage - Callback when message is received
 * @param {Function} options.onClose - Callback when connection closes
 * @param {Function} options.onError - Callback when error occurs
 * @returns {Object} WebSocket hook API
 */
var useWebSocket = function useWebSocket(options) {
  // Handle string URL as options
  var config = typeof options === 'string' ? {
    url: options
  } : options;

  // State for connection status and last message
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_services_WebSocketClient__WEBPACK_IMPORTED_MODULE_2__/* .ConnectionState */ .K.CLOSED),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState, 2),
    connectionState = _useState2[0],
    setConnectionState = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState3, 2),
    lastMessage = _useState4[0],
    setLastMessage = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(_useState5, 2),
    lastError = _useState6[0],
    setLastError = _useState6[1];

  // Refs for WebSocketClient and callbacks
  var clientRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);
  var callbacksRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({
    onOpen: config.onOpen,
    onMessage: config.onMessage,
    onClose: config.onClose,
    onError: config.onError
  });

  // Update callbacks ref when props change
  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {
    callbacksRef.current = {
      onOpen: config.onOpen,
      onMessage: config.onMessage,
      onClose: config.onClose,
      onError: config.onError
    };
  }, [config.onOpen, config.onMessage, config.onClose, config.onError]);

  // Initialize WebSocketClient
  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {
    // Create client options
    var clientOptions = {
      url: config.url,
      reconnectInterval: config.reconnectInterval,
      maxReconnectAttempts: config.maxReconnectAttempts,
      debug: config.debug,
      automaticOpen: false // We'll handle connection manually
    };

    // Create client
    var client;
    if (config.endpoint) {
      client = _services_WebSocketClient__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A.forEndpoint(config.endpoint, clientOptions);
    } else {
      client = new _services_WebSocketClient__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A(clientOptions);
    }
    clientRef.current = client;

    // Set up event listeners
    var stateChangeListener = function stateChangeListener(_ref) {
      var currentState = _ref.currentState;
      setConnectionState(currentState);
    };
    var openListener = function openListener(event) {
      if (callbacksRef.current.onOpen) {
        callbacksRef.current.onOpen(event);
      }
    };
    var messageListener = function messageListener(_ref2) {
      var data = _ref2.data;
      setLastMessage(data);
      if (callbacksRef.current.onMessage) {
        callbacksRef.current.onMessage(data);
      }
    };
    var closeListener = function closeListener(event) {
      if (callbacksRef.current.onClose) {
        callbacksRef.current.onClose(event);
      }
    };
    var errorListener = function errorListener(event) {
      // Create a more detailed error object
      var errorDetails = {
        timestamp: new Date().toISOString(),
        type: event.type || 'unknown',
        message: event.message || 'WebSocket connection error',
        code: event.code,
        reason: event.reason,
        wasClean: event.wasClean,
        originalEvent: event
      };

      // Log detailed error information
      if (config.debug) {
        console.error('WebSocket Error:', errorDetails);
      }

      // Update error state
      setLastError(errorDetails);

      // Call error callback with enhanced error information
      if (callbacksRef.current.onError) {
        callbacksRef.current.onError(errorDetails);
      }
    };

    // Add event listeners
    client.addEventListener('state_change', stateChangeListener);
    client.addEventListener('open', openListener);
    client.addEventListener('message', messageListener);
    client.addEventListener('close', closeListener);
    client.addEventListener('error', errorListener);

    // Connect if autoConnect is true
    if (config.autoConnect !== false) {
      client.open();
    }

    // Cleanup function
    return function () {
      // Remove event listeners
      client.removeEventListener('state_change', stateChangeListener);
      client.removeEventListener('open', openListener);
      client.removeEventListener('message', messageListener);
      client.removeEventListener('close', closeListener);
      client.removeEventListener('error', errorListener);

      // Close connection
      client.close();
    };
  }, [config.url, config.endpoint]); // Only recreate client if URL or endpoint changes

  // Connect function with retry logic
  var connect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function () {
    if (clientRef.current) {
      try {
        // Reset error state when attempting to connect
        setLastError(null);

        // Attempt to open the connection
        clientRef.current.open();

        // Log connection attempt if debug is enabled
        if (config.debug) {
          console.log("WebSocket: Attempting to connect to ".concat(config.url || config.endpoint));
        }
      } catch (error) {
        // Handle any synchronous errors during connection attempt
        var errorDetails = {
          timestamp: new Date().toISOString(),
          type: 'connection_error',
          message: error.message || 'Failed to initiate WebSocket connection',
          originalError: error
        };
        setLastError(errorDetails);
        if (callbacksRef.current.onError) {
          callbacksRef.current.onError(errorDetails);
        }
      }
    }
  }, [config.debug, config.url, config.endpoint]);

  // Disconnect function
  var disconnect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (code, reason) {
    if (clientRef.current) {
      clientRef.current.close(code, reason);
    }
  }, []);

  // Send function
  var send = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (data) {
    if (clientRef.current) {
      return clientRef.current.send(data);
    }
    return false;
  }, []);

  // Add event listener function
  var addEventListener = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (type, listener, options) {
    if (clientRef.current) {
      return clientRef.current.addEventListener(type, listener, options);
    }
    return function () {};
  }, []);

  // Remove event listener function
  var removeEventListener = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (type, listener) {
    if (clientRef.current) {
      return clientRef.current.removeEventListener(type, listener);
    }
    return false;
  }, []);

  // Return hook API
  return {
    // State
    connectionState: connectionState,
    lastMessage: lastMessage,
    lastError: lastError,
    // Connection status helpers
    isConnecting: connectionState === _services_WebSocketClient__WEBPACK_IMPORTED_MODULE_2__/* .ConnectionState */ .K.CONNECTING || connectionState === _services_WebSocketClient__WEBPACK_IMPORTED_MODULE_2__/* .ConnectionState */ .K.RECONNECTING,
    isOpen: connectionState === _services_WebSocketClient__WEBPACK_IMPORTED_MODULE_2__/* .ConnectionState */ .K.OPEN,
    isClosed: connectionState === _services_WebSocketClient__WEBPACK_IMPORTED_MODULE_2__/* .ConnectionState */ .K.CLOSED || connectionState === _services_WebSocketClient__WEBPACK_IMPORTED_MODULE_2__/* .ConnectionState */ .K.CLOSING,
    hasError: lastError !== null,
    // Error helpers
    errorMessage: lastError ? lastError.message : null,
    errorType: lastError ? lastError.type : null,
    errorTimestamp: lastError ? lastError.timestamp : null,
    // Methods
    connect: connect,
    disconnect: disconnect,
    send: send,
    addEventListener: addEventListener,
    removeEventListener: removeEventListener,
    // Reset error state
    clearError: function clearError() {
      return setLastError(null);
    },
    // Raw client (for advanced usage)
    client: clientRef.current
  };
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useWebSocket);

/***/ })

}]);