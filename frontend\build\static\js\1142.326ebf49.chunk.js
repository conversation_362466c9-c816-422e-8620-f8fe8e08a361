"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[1142],{

/***/ 71142:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(60436);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(64467);
/* harmony import */ var _babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(10467);
/* harmony import */ var _babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(5544);
/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(57528);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(54756);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(96540);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(71468);
/* harmony import */ var antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(33966);
/* harmony import */ var _ant_design_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(36031);
/* harmony import */ var _design_system__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(79146);
/* harmony import */ var _design_system_theme__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(86020);
/* harmony import */ var _utils_code_generator__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(5669);





var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5, _templateObject6;
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }








var Title = antd__WEBPACK_IMPORTED_MODULE_8__/* .Typography */ .o5.Title,
  Text = antd__WEBPACK_IMPORTED_MODULE_8__/* .Typography */ .o5.Text,
  Paragraph = antd__WEBPACK_IMPORTED_MODULE_8__/* .Typography */ .o5.Paragraph;
var TabPane = antd__WEBPACK_IMPORTED_MODULE_8__/* .Tabs */ .tU.TabPane;
var Option = antd__WEBPACK_IMPORTED_MODULE_8__/* .Select */ .l6.Option;
var Panel = antd__WEBPACK_IMPORTED_MODULE_8__/* .Collapse */ .SD.Panel;
var ExporterContainer = _design_system__WEBPACK_IMPORTED_MODULE_10__.styled.div(_templateObject || (_templateObject = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  padding: ", ";\n  max-width: 1200px;\n  margin: 0 auto;\n"])), _design_system_theme__WEBPACK_IMPORTED_MODULE_11__/* ["default"].spacing */ .Ay.spacing[4]);
var PreviewContainer = _design_system__WEBPACK_IMPORTED_MODULE_10__.styled.div(_templateObject2 || (_templateObject2 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  background-color: ", ";\n  border: 1px solid ", ";\n  border-radius: ", ";\n  padding: ", ";\n  margin: ", " 0;\n  max-height: 600px;\n  overflow: auto;\n"])), _design_system_theme__WEBPACK_IMPORTED_MODULE_11__/* ["default"].colors */ .Ay.colors.neutral[50], _design_system_theme__WEBPACK_IMPORTED_MODULE_11__/* ["default"].colors */ .Ay.colors.neutral[300], _design_system_theme__WEBPACK_IMPORTED_MODULE_11__/* ["default"].borderRadius */ .Ay.borderRadius.md, _design_system_theme__WEBPACK_IMPORTED_MODULE_11__/* ["default"].spacing */ .Ay.spacing[3], _design_system_theme__WEBPACK_IMPORTED_MODULE_11__/* ["default"].spacing */ .Ay.spacing[3]);
var CodePreview = _design_system__WEBPACK_IMPORTED_MODULE_10__.styled.pre(_templateObject3 || (_templateObject3 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  background-color: ", ";\n  color: ", ";\n  border-radius: ", ";\n  padding: ", ";\n  overflow: auto;\n  max-height: 500px;\n  font-family: ", ";\n  font-size: ", ";\n  line-height: 1.5;\n  margin: 0;\n"])), _design_system_theme__WEBPACK_IMPORTED_MODULE_11__/* ["default"].colors */ .Ay.colors.neutral[900], _design_system_theme__WEBPACK_IMPORTED_MODULE_11__/* ["default"].colors */ .Ay.colors.neutral[100], _design_system_theme__WEBPACK_IMPORTED_MODULE_11__/* ["default"].borderRadius */ .Ay.borderRadius.md, _design_system_theme__WEBPACK_IMPORTED_MODULE_11__/* ["default"].spacing */ .Ay.spacing[3], _design_system_theme__WEBPACK_IMPORTED_MODULE_11__/* ["default"].typography */ .Ay.typography.fontFamily.code, _design_system_theme__WEBPACK_IMPORTED_MODULE_11__/* ["default"].typography */ .Ay.typography.fontSize.sm);
var OptionsGrid = _design_system__WEBPACK_IMPORTED_MODULE_10__.styled.div(_templateObject4 || (_templateObject4 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: ", ";\n  margin: ", " 0;\n"])), _design_system_theme__WEBPACK_IMPORTED_MODULE_11__/* ["default"].spacing */ .Ay.spacing[4], _design_system_theme__WEBPACK_IMPORTED_MODULE_11__/* ["default"].spacing */ .Ay.spacing[4]);
var OptionCard = (0,_design_system__WEBPACK_IMPORTED_MODULE_10__.styled)(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp)(_templateObject5 || (_templateObject5 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  .ant-card-body {\n    padding: ", ";\n  }\n"])), _design_system_theme__WEBPACK_IMPORTED_MODULE_11__/* ["default"].spacing */ .Ay.spacing[3]);
var ExportHistoryItem = _design_system__WEBPACK_IMPORTED_MODULE_10__.styled.div(_templateObject6 || (_templateObject6 = (0,_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .A)(["\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: ", ";\n  border: 1px solid ", ";\n  border-radius: ", ";\n  margin-bottom: ", ";\n"])), _design_system_theme__WEBPACK_IMPORTED_MODULE_11__/* ["default"].spacing */ .Ay.spacing[2], _design_system_theme__WEBPACK_IMPORTED_MODULE_11__/* ["default"].colors */ .Ay.colors.neutral[200], _design_system_theme__WEBPACK_IMPORTED_MODULE_11__/* ["default"].borderRadius */ .Ay.borderRadius.sm, _design_system_theme__WEBPACK_IMPORTED_MODULE_11__/* ["default"].spacing */ .Ay.spacing[2]);

/**
 * Enhanced Code Exporter Component
 * Provides comprehensive export functionality with multiple frameworks, preview, and advanced options
 */
var EnhancedCodeExporter = function EnhancedCodeExporter(_ref) {
  var propComponents = _ref.components,
    propLayouts = _ref.layouts,
    propTheme = _ref.theme,
    onExport = _ref.onExport,
    onPreview = _ref.onPreview;
  var dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_7__/* .useDispatch */ .wA)();
  var reduxComponents = (0,react_redux__WEBPACK_IMPORTED_MODULE_7__/* .useSelector */ .d4)(function (state) {
    var _state$app;
    return (state === null || state === void 0 || (_state$app = state.app) === null || _state$app === void 0 ? void 0 : _state$app.components) || [];
  });
  var reduxLayouts = (0,react_redux__WEBPACK_IMPORTED_MODULE_7__/* .useSelector */ .d4)(function (state) {
    var _state$app2;
    return (state === null || state === void 0 || (_state$app2 = state.app) === null || _state$app2 === void 0 ? void 0 : _state$app2.layouts) || [];
  });
  var activeTheme = (0,react_redux__WEBPACK_IMPORTED_MODULE_7__/* .useSelector */ .d4)(function (state) {
    var _state$themes;
    return (state === null || state === void 0 || (_state$themes = state.themes) === null || _state$themes === void 0 ? void 0 : _state$themes.activeTheme) || 'default';
  });
  var themes = (0,react_redux__WEBPACK_IMPORTED_MODULE_7__/* .useSelector */ .d4)(function (state) {
    var _state$themes2;
    return (state === null || state === void 0 || (_state$themes2 = state.themes) === null || _state$themes2 === void 0 ? void 0 : _state$themes2.themes) || [];
  });

  // Use props if provided, otherwise fall back to Redux state
  var components = propComponents || reduxComponents;
  var layouts = propLayouts || reduxLayouts;
  var currentTheme = propTheme || activeTheme;
  var websocketConnected = (0,react_redux__WEBPACK_IMPORTED_MODULE_7__/* .useSelector */ .d4)(function (state) {
    var _state$websocket;
    return (state === null || state === void 0 || (_state$websocket = state.websocket) === null || _state$websocket === void 0 ? void 0 : _state$websocket.connected) || false;
  });
  var templates = (0,react_redux__WEBPACK_IMPORTED_MODULE_7__/* .useSelector */ .d4)(function (state) {
    return (state === null || state === void 0 ? void 0 : state.templates) || {};
  });

  // Export configuration state
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)('react'),
    _useState2 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState, 2),
    exportFormat = _useState2[0],
    setExportFormat = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)({
      typescript: false,
      includeAccessibility: true,
      includeTests: false,
      includeStorybook: false,
      styleFramework: 'styled-components',
      stateManagement: 'useState',
      projectStructure: 'single-file',
      bundler: 'vite',
      packageManager: 'npm',
      includeDocker: false,
      includeCiCd: false
    }),
    _useState4 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState3, 2),
    exportOptions = _useState4[0],
    setExportOptions = _useState4[1];

  // UI state
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)('configure'),
    _useState6 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState5, 2),
    activeTab = _useState6[0],
    setActiveTab = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(''),
    _useState8 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState7, 2),
    codePreview = _useState8[0],
    setCodePreview = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(false),
    _useState0 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState9, 2),
    previewLoading = _useState0[0],
    setPreviewLoading = _useState0[1];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(false),
    _useState10 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState1, 2),
    exportLoading = _useState10[0],
    setExportLoading = _useState10[1];
  var _useState11 = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)([]),
    _useState12 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState11, 2),
    exportHistory = _useState12[0],
    setExportHistory = _useState12[1];
  var _useState13 = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)([]),
    _useState14 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState13, 2),
    selectedComponents = _useState14[0],
    setSelectedComponents = _useState14[1];
  var _useState15 = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)([]),
    _useState16 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState15, 2),
    selectedLayouts = _useState16[0],
    setSelectedLayouts = _useState16[1];
  var _useState17 = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(false),
    _useState18 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState17, 2),
    showAdvancedOptions = _useState18[0],
    setShowAdvancedOptions = _useState18[1];
  var _useState19 = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(0),
    _useState20 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState19, 2),
    exportProgress = _useState20[0],
    setExportProgress = _useState20[1];
  var _useState21 = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(false),
    _useState22 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState21, 2),
    previewModalVisible = _useState22[0],
    setPreviewModalVisible = _useState22[1];
  var _useState23 = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(false),
    _useState24 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState23, 2),
    exportModalVisible = _useState24[0],
    setExportModalVisible = _useState24[1];
  var _useState25 = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(false),
    _useState26 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState25, 2),
    batchExportMode = _useState26[0],
    setBatchExportMode = _useState26[1];
  var _useState27 = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)([]),
    _useState28 = (0,_babel_runtime_helpers_slicedToArray__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .A)(_useState27, 2),
    selectedExports = _useState28[0],
    setSelectedExports = _useState28[1];

  // Available export formats
  var exportFormats = [{
    value: 'react',
    label: 'React',
    icon: '⚛️',
    description: 'Modern React with hooks'
  }, {
    value: 'react-ts',
    label: 'React + TypeScript',
    icon: '⚛️',
    description: 'React with TypeScript support'
  }, {
    value: 'vue',
    label: 'Vue.js',
    icon: '🟢',
    description: 'Vue 3 with Composition API'
  }, {
    value: 'vue-ts',
    label: 'Vue + TypeScript',
    icon: '🟢',
    description: 'Vue 3 with TypeScript'
  }, {
    value: 'angular',
    label: 'Angular',
    icon: '🔴',
    description: 'Angular with TypeScript'
  }, {
    value: 'svelte',
    label: 'Svelte',
    icon: '🧡',
    description: 'Svelte with modern features'
  }, {
    value: 'next',
    label: 'Next.js',
    icon: '⚫',
    description: 'Next.js with SSR support'
  }, {
    value: 'nuxt',
    label: 'Nuxt.js',
    icon: '🟢',
    description: 'Nuxt.js for Vue'
  }, {
    value: 'html',
    label: 'HTML/CSS/JS',
    icon: '🌐',
    description: 'Vanilla web technologies'
  }, {
    value: 'react-native',
    label: 'React Native',
    icon: '📱',
    description: 'Mobile app with React Native'
  }, {
    value: 'flutter',
    label: 'Flutter',
    icon: '🐦',
    description: 'Cross-platform with Flutter'
  }, {
    value: 'ionic',
    label: 'Ionic',
    icon: '⚡',
    description: 'Hybrid mobile apps'
  }];

  // Style frameworks
  var styleFrameworks = [{
    value: 'styled-components',
    label: 'Styled Components',
    description: 'CSS-in-JS with styled-components'
  }, {
    value: 'emotion',
    label: 'Emotion',
    description: 'CSS-in-JS with Emotion'
  }, {
    value: 'tailwind',
    label: 'Tailwind CSS',
    description: 'Utility-first CSS framework'
  }, {
    value: 'css-modules',
    label: 'CSS Modules',
    description: 'Scoped CSS with modules'
  }, {
    value: 'material-ui',
    label: 'Material-UI',
    description: 'React Material Design components'
  }, {
    value: 'chakra-ui',
    label: 'Chakra UI',
    description: 'Simple and modular React components'
  }, {
    value: 'bootstrap',
    label: 'Bootstrap',
    description: 'Popular CSS framework'
  }];

  // State management options
  var stateManagementOptions = [{
    value: 'useState',
    label: 'React Hooks (useState)',
    description: 'Built-in React state management'
  }, {
    value: 'redux',
    label: 'Redux Toolkit',
    description: 'Predictable state container'
  }, {
    value: 'zustand',
    label: 'Zustand',
    description: 'Lightweight state management'
  }, {
    value: 'context',
    label: 'React Context',
    description: 'Built-in context API'
  }];

  // Project structure options
  var projectStructures = [{
    value: 'single-file',
    label: 'Single File',
    description: 'All code in one file'
  }, {
    value: 'multi-file',
    label: 'Multiple Files',
    description: 'Organized file structure'
  }, {
    value: 'full-project',
    label: 'Full Project',
    description: 'Complete project with configs'
  }];

  // Initialize selected components and layouts
  (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(function () {
    if (components.length > 0 && selectedComponents.length === 0) {
      setSelectedComponents(components.map(function (c) {
        return c.id;
      }));
    }
    if (layouts.length > 0 && selectedLayouts.length === 0) {
      setSelectedLayouts(layouts.map(function (l) {
        return l.id;
      }));
    }
  }, [components, layouts, selectedComponents.length, selectedLayouts.length]);

  // Load export history from localStorage
  (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(function () {
    var savedHistory = localStorage.getItem('exportHistory');
    if (savedHistory) {
      try {
        setExportHistory(JSON.parse(savedHistory));
      } catch (error) {
        console.error('Failed to load export history:', error);
      }
    }
  }, []);

  // Generate code preview
  var generatePreview = (0,react__WEBPACK_IMPORTED_MODULE_6__.useCallback)(/*#__PURE__*/(0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default().mark(function _callee() {
    var appData, code, codeString;
    return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default().wrap(function (_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          setPreviewLoading(true);
          try {
            appData = {
              components: components.filter(function (c) {
                return selectedComponents.includes(c.id);
              }),
              layouts: layouts.filter(function (l) {
                return selectedLayouts.includes(l.id);
              }),
              styles: {},
              data: {}
            };
            code = (0,_utils_code_generator__WEBPACK_IMPORTED_MODULE_12__/* .generateCode */ .b)(appData, exportFormat, exportOptions);
            codeString = typeof code === 'string' ? code : JSON.stringify(code, null, 2);
            setCodePreview(codeString);

            // Call the onPreview callback if provided
            if (onPreview) {
              onPreview(codeString);
            }
          } catch (error) {
            console.error('Preview generation failed:', error);
            antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.error('Failed to generate preview');
            setCodePreview('// Preview generation failed');
          } finally {
            setPreviewLoading(false);
          }
        case 1:
        case "end":
          return _context.stop();
      }
    }, _callee);
  })), [components, layouts, selectedComponents, selectedLayouts, exportFormat, exportOptions]);

  // Auto-generate preview when options change
  (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(function () {
    if (activeTab === 'preview') {
      generatePreview();
    }
  }, [activeTab, generatePreview]);

  // WebSocket integration for real-time updates
  (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(function () {
    if (websocketConnected && activeTab === 'preview') {
      // Send export configuration to WebSocket for real-time updates
      var wsMessage = {
        type: 'export_config_update',
        data: {
          format: exportFormat,
          options: exportOptions,
          components: selectedComponents,
          layouts: selectedLayouts
        }
      };

      // This would be handled by the WebSocket service
      console.log('WebSocket export config update:', wsMessage);
    }
  }, [websocketConnected, activeTab, exportFormat, exportOptions, selectedComponents, selectedLayouts]);

  // Handle export option changes
  var handleOptionChange = function handleOptionChange(key, value) {
    setExportOptions(function (prev) {
      return _objectSpread(_objectSpread({}, prev), {}, (0,_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .A)({}, key, value));
    });
  };

  // Handle enhanced export
  var handleEnhancedExport = /*#__PURE__*/function () {
    var _ref3 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default().mark(function _callee2() {
      var appData, progressInterval, response, result, exportRecord, newHistory, _exportRecord, _newHistory, _t;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default().wrap(function (_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            setExportLoading(true);
            setExportProgress(0);
            _context2.prev = 1;
            appData = {
              components: components.filter(function (c) {
                return selectedComponents.includes(c.id);
              }),
              layouts: layouts.filter(function (l) {
                return selectedLayouts.includes(l.id);
              }),
              styles: {},
              data: {},
              // Include template information for enhanced export
              layout_templates: templates.layoutTemplates || [],
              app_templates: templates.appTemplates || [],
              active_theme: activeTheme,
              theme_data: themes.find(function (t) {
                return t.id === activeTheme;
              }) || {}
            }; // Call the onExport callback if provided
            if (onExport) {
              onExport({
                framework: exportFormat,
                components: appData.components,
                layouts: appData.layouts,
                options: exportOptions,
                settings: _objectSpread({
                  format: exportFormat
                }, exportOptions)
              });
            }

            // Simulate progress
            progressInterval = setInterval(function () {
              setExportProgress(function (prev) {
                return Math.min(prev + 10, 90);
              });
            }, 200);
            _context2.next = 2;
            return fetch('/api/enhanced-export/', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': "Bearer ".concat(localStorage.getItem('token'))
              },
              body: JSON.stringify({
                app_id: 1,
                // Replace with actual app ID
                format: exportFormat,
                options: exportOptions
              })
            });
          case 2:
            response = _context2.sent;
            clearInterval(progressInterval);
            setExportProgress(100);
            if (response.ok) {
              _context2.next = 3;
              break;
            }
            throw new Error('Export failed');
          case 3:
            _context2.next = 4;
            return response.json();
          case 4:
            result = _context2.sent;
            // Save to export history
            exportRecord = {
              id: Date.now(),
              format: exportFormat,
              options: exportOptions,
              timestamp: new Date().toISOString(),
              status: 'success',
              type: result.type,
              size: result.code ? result.code.length : Object.keys(result.files || {}).length
            };
            newHistory = [exportRecord].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(exportHistory.slice(0, 9))); // Keep last 10
            setExportHistory(newHistory);
            localStorage.setItem('exportHistory', JSON.stringify(newHistory));

            // Handle download based on result type
            if (result.type === 'single-file') {
              downloadFile(result.code, "app.".concat(getFileExtension(exportFormat)));
            } else if (result.type === 'multi-file') {
              downloadMultipleFiles(result.files);
            } else if (result.type === 'zip') {
              downloadZipFile(result.zip_data, 'app-export.zip');
            }
            antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success('Export completed successfully!');
            _context2.next = 6;
            break;
          case 5:
            _context2.prev = 5;
            _t = _context2["catch"](1);
            console.error('Export failed:', _t);
            antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.error('Export failed. Please try again.');

            // Save failed export to history
            _exportRecord = {
              id: Date.now(),
              format: exportFormat,
              options: exportOptions,
              timestamp: new Date().toISOString(),
              status: 'failed',
              error: _t.message
            };
            _newHistory = [_exportRecord].concat((0,_babel_runtime_helpers_toConsumableArray__WEBPACK_IMPORTED_MODULE_0__/* ["default"] */ .A)(exportHistory.slice(0, 9)));
            setExportHistory(_newHistory);
            localStorage.setItem('exportHistory', JSON.stringify(_newHistory));
          case 6:
            _context2.prev = 6;
            setExportLoading(false);
            setExportProgress(0);
            return _context2.finish(6);
          case 7:
          case "end":
            return _context2.stop();
        }
      }, _callee2, null, [[1, 5, 6, 7]]);
    }));
    return function handleEnhancedExport() {
      return _ref3.apply(this, arguments);
    };
  }();

  // Download single file
  var downloadFile = function downloadFile(content, filename) {
    var blob = new Blob([content], {
      type: 'text/plain'
    });
    var url = URL.createObjectURL(blob);
    var a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Download multiple files as zip
  var downloadMultipleFiles = /*#__PURE__*/function () {
    var _ref4 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default().mark(function _callee3(files) {
      var mainFile;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default().wrap(function (_context3) {
        while (1) switch (_context3.prev = _context3.next) {
          case 0:
            // This would require a zip library like JSZip
            // For now, download the main file
            mainFile = files['App.jsx'] || files['App.tsx'] || files['index.html'] || Object.values(files)[0];
            if (mainFile) {
              downloadFile(mainFile, 'App.jsx');
            }
          case 1:
          case "end":
            return _context3.stop();
        }
      }, _callee3);
    }));
    return function downloadMultipleFiles(_x) {
      return _ref4.apply(this, arguments);
    };
  }();

  // Download zip file
  var downloadZipFile = function downloadZipFile(base64Data, filename) {
    var binaryString = atob(base64Data);
    var bytes = new Uint8Array(binaryString.length);
    for (var i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    var blob = new Blob([bytes], {
      type: 'application/zip'
    });
    var url = URL.createObjectURL(blob);
    var a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Get file extension for format
  var getFileExtension = function getFileExtension(format) {
    var extensions = {
      'react': 'jsx',
      'react-ts': 'tsx',
      'vue': 'vue',
      'vue-ts': 'vue',
      'angular': 'ts',
      'svelte': 'svelte',
      'html': 'html',
      'react-native': 'jsx',
      'flutter': 'dart',
      'ionic': 'ts'
    };
    return extensions[format] || 'js';
  };

  // Copy code to clipboard
  var handleCopyCode = function handleCopyCode() {
    navigator.clipboard.writeText(codePreview).then(function () {
      antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success('Code copied to clipboard');
    })["catch"](function (error) {
      console.error('Failed to copy code:', error);
      antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.error('Failed to copy code');
    });
  };

  // Handle template export
  var handleTemplateExport = /*#__PURE__*/function () {
    var _ref5 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default().mark(function _callee4(templateId, templateType) {
      var response, result, _t2;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default().wrap(function (_context4) {
        while (1) switch (_context4.prev = _context4.next) {
          case 0:
            setExportLoading(true);
            setExportProgress(0);
            _context4.prev = 1;
            _context4.next = 2;
            return fetch('/api/export-template/', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': "Bearer ".concat(localStorage.getItem('token'))
              },
              body: JSON.stringify({
                template_id: templateId,
                template_type: templateType,
                format: exportFormat,
                options: exportOptions
              })
            });
          case 2:
            response = _context4.sent;
            if (response.ok) {
              _context4.next = 3;
              break;
            }
            throw new Error('Template export failed');
          case 3:
            _context4.next = 4;
            return response.json();
          case 4:
            result = _context4.sent;
            if (!(result.type === 'project')) {
              _context4.next = 6;
              break;
            }
            _context4.next = 5;
            return downloadMultipleFiles(result.files);
          case 5:
            antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success('Template exported successfully!');
          case 6:
            _context4.next = 8;
            break;
          case 7:
            _context4.prev = 7;
            _t2 = _context4["catch"](1);
            console.error('Template export failed:', _t2);
            antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.error('Template export failed. Please try again.');
          case 8:
            _context4.prev = 8;
            setExportLoading(false);
            setExportProgress(0);
            return _context4.finish(8);
          case 9:
          case "end":
            return _context4.stop();
        }
      }, _callee4, null, [[1, 7, 8, 9]]);
    }));
    return function handleTemplateExport(_x2, _x3) {
      return _ref5.apply(this, arguments);
    };
  }();

  // Handle batch export
  var handleBatchExport = /*#__PURE__*/function () {
    var _ref6 = (0,_babel_runtime_helpers_asyncToGenerator__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .A)(/*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default().mark(function _callee5(appIds) {
      var response, blob, url, a, _t3;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_5___default().wrap(function (_context5) {
        while (1) switch (_context5.prev = _context5.next) {
          case 0:
            setExportLoading(true);
            setExportProgress(0);
            _context5.prev = 1;
            _context5.next = 2;
            return fetch('/api/batch-export/', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': "Bearer ".concat(localStorage.getItem('token'))
              },
              body: JSON.stringify({
                app_ids: appIds,
                format: exportFormat,
                options: exportOptions
              })
            });
          case 2:
            response = _context5.sent;
            if (response.ok) {
              _context5.next = 3;
              break;
            }
            throw new Error('Batch export failed');
          case 3:
            _context5.next = 4;
            return response.blob();
          case 4:
            blob = _context5.sent;
            url = URL.createObjectURL(blob);
            a = document.createElement('a');
            a.href = url;
            a.download = "batch-export-".concat(exportFormat, ".zip");
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success('Batch export completed successfully!');
            _context5.next = 6;
            break;
          case 5:
            _context5.prev = 5;
            _t3 = _context5["catch"](1);
            console.error('Batch export failed:', _t3);
            antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.error('Batch export failed. Please try again.');
          case 6:
            _context5.prev = 6;
            setExportLoading(false);
            setExportProgress(0);
            return _context5.finish(6);
          case 7:
          case "end":
            return _context5.stop();
        }
      }, _callee5, null, [[1, 5, 6, 7]]);
    }));
    return function handleBatchExport(_x4) {
      return _ref6.apply(this, arguments);
    };
  }();
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(ExporterContainer, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Title, {
    level: 2
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .CodeOutlined */ .C$o, null), " Enhanced Code Exporter"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Paragraph, null, "Export your application to multiple frameworks with advanced configuration options, preview functionality, and project management features."), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Tabs */ .tU, {
    activeKey: activeTab,
    onChange: setActiveTab,
    size: "large"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(TabPane, {
    tab: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("span", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .SettingOutlined */ .JO7, null), "Configure"),
    key: "configure"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(OptionsGrid, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(OptionCard, {
    title: "Export Format",
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Select */ .l6, {
    value: exportFormat,
    onChange: setExportFormat,
    style: {
      width: '100%'
    },
    size: "large"
  }, exportFormats.map(function (format) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Option, {
      key: format.value,
      value: format.value
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("span", null, format.icon), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", null, format.label), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, {
      type: "secondary",
      style: {
        fontSize: '12px'
      }
    }, format.description))));
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(OptionCard, {
    title: "Project Structure",
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Radio */ .sx.Group, {
    value: exportOptions.projectStructure,
    onChange: function onChange(e) {
      return handleOptionChange('projectStructure', e.target.value);
    },
    style: {
      width: '100%'
    }
  }, projectStructures.map(function (structure) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Radio */ .sx, {
      key: structure.value,
      value: structure.value,
      style: {
        display: 'block',
        marginBottom: 8
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", null, structure.label), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, {
      type: "secondary",
      style: {
        fontSize: '12px'
      }
    }, structure.description)));
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(OptionCard, {
    title: "Style Framework",
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Select */ .l6, {
    value: exportOptions.styleFramework,
    onChange: function onChange(value) {
      return handleOptionChange('styleFramework', value);
    },
    style: {
      width: '100%'
    }
  }, styleFrameworks.map(function (framework) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Option, {
      key: framework.value,
      value: framework.value
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", null, framework.label), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, {
      type: "secondary",
      style: {
        fontSize: '12px'
      }
    }, framework.description)));
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(OptionCard, {
    title: "State Management",
    size: "small"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Select */ .l6, {
    value: exportOptions.stateManagement,
    onChange: function onChange(value) {
      return handleOptionChange('stateManagement', value);
    },
    style: {
      width: '100%'
    }
  }, stateManagementOptions.map(function (option) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Option, {
      key: option.value,
      value: option.value
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", null, option.label), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, {
      type: "secondary",
      style: {
        fontSize: '12px'
      }
    }, option.description)));
  })))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Card */ .Zp, {
    title: "Basic Options",
    size: "small",
    style: {
      marginBottom: 16
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Space */ .$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Checkbox */ .Sc, {
    checked: exportOptions.typescript,
    onChange: function onChange(e) {
      return handleOptionChange('typescript', e.target.checked);
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("span", null, "TypeScript Support"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Tooltip */ .m_, {
    title: "Generate TypeScript code with type definitions"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .InfoCircleOutlined */ .rUN, null)))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Checkbox */ .Sc, {
    checked: exportOptions.includeAccessibility,
    onChange: function onChange(e) {
      return handleOptionChange('includeAccessibility', e.target.checked);
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("span", null, "Accessibility Features"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Tooltip */ .m_, {
    title: "Include ARIA labels, roles, and other accessibility attributes"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .InfoCircleOutlined */ .rUN, null)))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Checkbox */ .Sc, {
    checked: exportOptions.includeTests,
    onChange: function onChange(e) {
      return handleOptionChange('includeTests', e.target.checked);
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("span", null, "Include Tests"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Tooltip */ .m_, {
    title: "Generate test files for components"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .InfoCircleOutlined */ .rUN, null)))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Checkbox */ .Sc, {
    checked: exportOptions.includeStorybook,
    onChange: function onChange(e) {
      return handleOptionChange('includeStorybook', e.target.checked);
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("span", null, "Storybook Stories"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Tooltip */ .m_, {
    title: "Generate Storybook stories for components"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .InfoCircleOutlined */ .rUN, null)))))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Collapse */ .SD, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Panel, {
    header: "Advanced Options",
    key: "advanced"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Space */ .$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, {
    strong: true
  }, "Package Manager:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Radio */ .sx.Group, {
    value: exportOptions.packageManager,
    onChange: function onChange(e) {
      return handleOptionChange('packageManager', e.target.value);
    },
    style: {
      marginLeft: 16
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Radio */ .sx, {
    value: "npm"
  }, "npm"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Radio */ .sx, {
    value: "yarn"
  }, "yarn"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Radio */ .sx, {
    value: "pnpm"
  }, "pnpm"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, {
    strong: true
  }, "Bundler:"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Radio */ .sx.Group, {
    value: exportOptions.bundler,
    onChange: function onChange(e) {
      return handleOptionChange('bundler', e.target.value);
    },
    style: {
      marginLeft: 16
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Radio */ .sx, {
    value: "vite"
  }, "Vite"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Radio */ .sx, {
    value: "webpack"
  }, "Webpack"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Radio */ .sx, {
    value: "parcel"
  }, "Parcel"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Checkbox */ .Sc, {
    checked: exportOptions.includeDocker,
    onChange: function onChange(e) {
      return handleOptionChange('includeDocker', e.target.checked);
    }
  }, "Include Docker Configuration"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Checkbox */ .Sc, {
    checked: exportOptions.includeCiCd,
    onChange: function onChange(e) {
      return handleOptionChange('includeCiCd', e.target.checked);
    }
  }, "Include CI/CD Pipeline")))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Divider */ .cG, null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
    type: "primary",
    size: "large",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .ExportOutlined */ .PZg, null),
    onClick: handleEnhancedExport,
    loading: exportLoading
  }, "Export Application"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
    size: "large",
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .EyeOutlined */ .Om2, null),
    onClick: function onClick() {
      return setActiveTab('preview');
    }
  }, "Preview Code")), exportLoading && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
    style: {
      marginTop: 16
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Progress */ .ke, {
    percent: exportProgress,
    status: "active"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, {
    type: "secondary"
  }, "Generating your application..."))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(TabPane, {
    tab: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("span", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .EyeOutlined */ .Om2, null), "Preview"),
    key: "preview"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Space */ .$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Alert */ .Fc, {
    message: "Code Preview",
    description: "This is a preview of the generated code. Use the Configure tab to adjust export options.",
    type: "info",
    showIcon: true,
    action: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
      size: "small",
      onClick: generatePreview,
      loading: previewLoading
    }, "Refresh"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
      size: "small",
      icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .CopyOutlined */ .wq3, null),
      onClick: handleCopyCode
    }, "Copy"))
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(PreviewContainer, null, previewLoading ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
    style: {
      textAlign: 'center',
      padding: '40px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Spin */ .tK, {
    size: "large"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
    style: {
      marginTop: 16
    }
  }, "Generating preview...")) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(CodePreview, null, codePreview)))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(TabPane, {
    tab: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("span", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .HistoryOutlined */ .dUu, null), "History"),
    key: "history"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Space */ .$x, {
    direction: "vertical",
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
    style: {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Title, {
    level: 4
  }, "Export History"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
    icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .DeleteOutlined */ .SUY, null),
    onClick: function onClick() {
      setExportHistory([]);
      localStorage.removeItem('exportHistory');
      antd__WEBPACK_IMPORTED_MODULE_8__/* .message */ .iU.success('Export history cleared');
    }
  }, "Clear History")), exportHistory.length === 0 ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
    style: {
      textAlign: 'center',
      padding: '40px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(Text, {
    type: "secondary"
  }, "No export history yet")) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .List */ .B8, {
    dataSource: exportHistory,
    renderItem: function renderItem(item) {
      var _exportFormats$find;
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .List */ .B8.Item, {
        actions: [/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Button */ .$n, {
          size: "small",
          icon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(_ant_design_icons__WEBPACK_IMPORTED_MODULE_9__/* .DownloadOutlined */ .jsW, null)
        }, "Re-export")]
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .List */ .B8.Item.Meta, {
        title: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Space */ .$x, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("span", null, ((_exportFormats$find = exportFormats.find(function (f) {
          return f.value === item.format;
        })) === null || _exportFormats$find === void 0 ? void 0 : _exportFormats$find.label) || item.format), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement(antd__WEBPACK_IMPORTED_MODULE_8__/* .Tag */ .vw, {
          color: item.status === 'success' ? 'green' : 'red'
        }, item.status)),
        description: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", null, "Exported on ", new Date(item.timestamp).toLocaleString()), item.size && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", null, "Size: ", item.size, " characters"), item.error && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_6__.createElement("div", {
          style: {
            color: 'red'
          }
        }, "Error: ", item.error))
      }));
    }
  })))));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EnhancedCodeExporter);

/***/ })

}]);