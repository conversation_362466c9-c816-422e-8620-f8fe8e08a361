"use strict";
(self["webpackChunkfrontend"] = self["webpackChunkfrontend"] || []).push([[7481],{

/***/ 97481:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  "default": () => (/* binding */ enhanced_DataManagementDemo)
});

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(10467);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(5544);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/taggedTemplateLiteral.js
var taggedTemplateLiteral = __webpack_require__(57528);
// EXTERNAL MODULE: ./node_modules/@babel/runtime/regenerator/index.js
var regenerator = __webpack_require__(54756);
var regenerator_default = /*#__PURE__*/__webpack_require__.n(regenerator);
// EXTERNAL MODULE: ./node_modules/react/index.js
var react = __webpack_require__(96540);
// EXTERNAL MODULE: ./node_modules/antd/es/index.js + 112 modules
var es = __webpack_require__(33966);
// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/index.js + 1565 modules
var icons_es = __webpack_require__(36031);
// EXTERNAL MODULE: ./node_modules/styled-components/dist/styled-components.browser.esm.js + 12 modules
var styled_components_browser_esm = __webpack_require__(71606);
// EXTERNAL MODULE: ./node_modules/react-redux/dist/react-redux.mjs
var react_redux = __webpack_require__(71468);
// EXTERNAL MODULE: ./src/redux/store.js + 7 modules
var store = __webpack_require__(69792);
;// ./src/utils/dataManager.js


/**
 * Data Manager Utility
 * 
 * This utility provides functions for managing data flow between components,
 * optimizing Redux store usage, and handling data operations.
 */




/**
 * Cache for storing temporary data that doesn't need to be in Redux
 */
var dataCache = {
  temporary: new Map(),
  persistent: new Map(),
  expiring: new Map(),
  timestamps: new Map()
};

/**
 * Set data in the cache
 * @param {string} key - The key to store the data under
 * @param {any} data - The data to store
 * @param {Object} options - Options for storing the data
 * @param {string} options.type - The type of cache to use ('temporary', 'persistent', or 'expiring')
 * @param {number} options.expiresIn - Time in milliseconds after which the data expires (for 'expiring' type)
 */
var setCache = function setCache(key, data) {
  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {
    type: 'temporary',
    expiresIn: 3600000
  };
  var type = options.type,
    expiresIn = options.expiresIn;
  switch (type) {
    case 'persistent':
      dataCache.persistent.set(key, data);
      try {
        localStorage.setItem("cache_".concat(key), JSON.stringify(data));
      } catch (error) {
        console.warn('Failed to save to localStorage:', error);
      }
      break;
    case 'expiring':
      dataCache.expiring.set(key, data);
      dataCache.timestamps.set(key, Date.now() + expiresIn);
      break;
    case 'temporary':
    default:
      dataCache.temporary.set(key, data);
      break;
  }
};

/**
 * Get data from the cache
 * @param {string} key - The key to retrieve data for
 * @param {string} type - The type of cache to use ('temporary', 'persistent', or 'expiring')
 * @returns {any} The cached data or null if not found
 */
var getCache = function getCache(key) {
  var type = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'temporary';
  switch (type) {
    case 'persistent':
      {
        // Try memory cache first
        if (dataCache.persistent.has(key)) {
          return dataCache.persistent.get(key);
        }

        // Try localStorage
        try {
          var data = localStorage.getItem("cache_".concat(key));
          if (data) {
            var parsed = JSON.parse(data);
            // Update memory cache
            dataCache.persistent.set(key, parsed);
            return parsed;
          }
        } catch (error) {
          console.warn('Failed to retrieve from localStorage:', error);
        }
        return null;
      }
    case 'expiring':
      {
        if (dataCache.expiring.has(key)) {
          var timestamp = dataCache.timestamps.get(key);
          if (timestamp && timestamp > Date.now()) {
            return dataCache.expiring.get(key);
          } else {
            // Expired, clean up
            dataCache.expiring["delete"](key);
            dataCache.timestamps["delete"](key);
          }
        }
        return null;
      }
    case 'temporary':
    default:
      return dataCache.temporary.get(key) || null;
  }
};

/**
 * Clear data from the cache
 * @param {string} key - The key to clear (if not provided, clears all data of the specified type)
 * @param {string} type - The type of cache to clear ('temporary', 'persistent', 'expiring', or 'all')
 */
var clearCache = function clearCache(key) {
  var type = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'temporary';
  if (key) {
    // Clear specific key
    switch (type) {
      case 'persistent':
        dataCache.persistent["delete"](key);
        try {
          localStorage.removeItem("cache_".concat(key));
        } catch (error) {
          console.warn('Failed to remove from localStorage:', error);
        }
        break;
      case 'expiring':
        dataCache.expiring["delete"](key);
        dataCache.timestamps["delete"](key);
        break;
      case 'all':
        dataCache.temporary["delete"](key);
        dataCache.persistent["delete"](key);
        dataCache.expiring["delete"](key);
        dataCache.timestamps["delete"](key);
        try {
          localStorage.removeItem("cache_".concat(key));
        } catch (error) {
          console.warn('Failed to remove from localStorage:', error);
        }
        break;
      case 'temporary':
      default:
        dataCache.temporary["delete"](key);
        break;
    }
  } else {
    // Clear all data of specified type
    switch (type) {
      case 'persistent':
        dataCache.persistent.clear();
        try {
          Object.keys(localStorage).forEach(function (key) {
            if (key.startsWith('cache_')) {
              localStorage.removeItem(key);
            }
          });
        } catch (error) {
          console.warn('Failed to clear localStorage:', error);
        }
        break;
      case 'expiring':
        dataCache.expiring.clear();
        dataCache.timestamps.clear();
        break;
      case 'all':
        dataCache.temporary.clear();
        dataCache.persistent.clear();
        dataCache.expiring.clear();
        dataCache.timestamps.clear();
        try {
          Object.keys(localStorage).forEach(function (key) {
            if (key.startsWith('cache_')) {
              localStorage.removeItem(key);
            }
          });
        } catch (error) {
          console.warn('Failed to clear localStorage:', error);
        }
        break;
      case 'temporary':
      default:
        dataCache.temporary.clear();
        break;
    }
  }
};

/**
 * Get data from Redux store with error handling
 * @param {Function} selector - Redux selector function
 * @param {any} defaultValue - Default value to return if selector fails
 * @returns {any} The selected data or default value
 */
var getStoreData = function getStoreData(selector) {
  var defaultValue = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;
  try {
    return selector(store/* default */.A.getState());
  } catch (error) {
    console.error('Error accessing Redux store:', error);
    return defaultValue;
  }
};

/**
 * Dispatch an action to Redux store with error handling
 * @param {Object|Function} action - Redux action or thunk
 * @returns {Promise<any>} Promise that resolves with the result of the dispatch
 */
var dispatchAction = /*#__PURE__*/function () {
  var _ref = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee(action) {
    var _t;
    return regenerator_default().wrap(function (_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.prev = 0;
          _context.next = 1;
          return store/* default */.A.dispatch(action);
        case 1:
          return _context.abrupt("return", _context.sent);
        case 2:
          _context.prev = 2;
          _t = _context["catch"](0);
          console.error('Error dispatching action:', _t);
          es/* message */.iU.error('An error occurred while updating data');
          throw _t;
        case 3:
        case "end":
          return _context.stop();
      }
    }, _callee, null, [[0, 2]]);
  }));
  return function dispatchAction(_x) {
    return _ref.apply(this, arguments);
  };
}();

/**
 * Batch multiple Redux actions into a single update
 * @param {Array<Object|Function>} actions - Array of Redux actions or thunks
 * @returns {Promise<Array<any>>} Promise that resolves with results of all dispatches
 */
var batchActions = /*#__PURE__*/function () {
  var _ref2 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee2(actions) {
    var results, promises, _t2;
    return regenerator_default().wrap(function (_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.prev = 0;
          results = []; // Start all dispatches
          promises = actions.map(function (action) {
            return store/* default */.A.dispatch(action);
          }); // Wait for all to complete
          _context2.next = 1;
          return Promise.all(promises);
        case 1:
          return _context2.abrupt("return", _context2.sent);
        case 2:
          _context2.prev = 2;
          _t2 = _context2["catch"](0);
          console.error('Error in batch actions:', _t2);
          es/* message */.iU.error('An error occurred while updating multiple items');
          throw _t2;
        case 3:
        case "end":
          return _context2.stop();
      }
    }, _callee2, null, [[0, 2]]);
  }));
  return function batchActions(_x2) {
    return _ref2.apply(this, arguments);
  };
}();
/* harmony default export */ const dataManager = ({
  setCache: setCache,
  getCache: getCache,
  clearCache: clearCache,
  getStoreData: getStoreData,
  dispatchAction: dispatchAction,
  batchActions: batchActions
});
;// ./src/hooks/useDataManager.js







/**
 * Custom hook for managing data with optimized Redux usage
 * 
 * @param {Object} options - Configuration options
 * @param {Function} options.selector - Redux selector function
 * @param {Function} options.action - Action creator function
 * @param {string} options.cacheKey - Key for caching data
 * @param {string} options.cacheType - Type of cache ('temporary', 'persistent', or 'expiring')
 * @param {number} options.expiresIn - Time in ms after which cache expires (for 'expiring' type)
 * @param {boolean} options.useCache - Whether to use cache
 * @param {any} options.defaultValue - Default value if data is not found
 * @returns {Object} Data management utilities
 */
var useDataManager = function useDataManager(_ref) {
  var selector = _ref.selector,
    action = _ref.action,
    cacheKey = _ref.cacheKey,
    _ref$cacheType = _ref.cacheType,
    cacheType = _ref$cacheType === void 0 ? 'temporary' : _ref$cacheType,
    _ref$expiresIn = _ref.expiresIn,
    expiresIn = _ref$expiresIn === void 0 ? 3600000 : _ref$expiresIn,
    _ref$useCache = _ref.useCache,
    useCache = _ref$useCache === void 0 ? true : _ref$useCache,
    _ref$defaultValue = _ref.defaultValue,
    defaultValue = _ref$defaultValue === void 0 ? null : _ref$defaultValue;
  var dispatch = (0,react_redux/* useDispatch */.wA)();
  var _useState = (0,react.useState)(false),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    loading = _useState2[0],
    setLoading = _useState2[1];
  var _useState3 = (0,react.useState)(null),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    error = _useState4[0],
    setError = _useState4[1];

  // Get data from Redux store if selector is provided
  var storeData = selector ? (0,react_redux/* useSelector */.d4)(selector) : undefined;

  // Get cached data
  var getCachedData = (0,react.useCallback)(function () {
    if (!useCache || !cacheKey) return null;
    return dataManager.getCache(cacheKey, cacheType);
  }, [useCache, cacheKey, cacheType]);

  // Set data in cache
  var setCachedData = (0,react.useCallback)(function (data) {
    if (!useCache || !cacheKey) return;
    dataManager.setCache(cacheKey, data, {
      type: cacheType,
      expiresIn: expiresIn
    });
  }, [useCache, cacheKey, cacheType, expiresIn]);

  // Clear cached data
  var clearCachedData = (0,react.useCallback)(function () {
    if (!cacheKey) return;
    dataManager.clearCache(cacheKey, cacheType);
  }, [cacheKey, cacheType]);

  // Update data in Redux store
  var updateData = (0,react.useCallback)(/*#__PURE__*/function () {
    var _ref2 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee(payload) {
      var result, _t;
      return regenerator_default().wrap(function (_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            if (action) {
              _context.next = 1;
              break;
            }
            return _context.abrupt("return");
          case 1:
            setLoading(true);
            setError(null);
            _context.prev = 2;
            _context.next = 3;
            return dispatch(action(payload));
          case 3:
            result = _context.sent;
            // Update cache with new data if caching is enabled
            if (useCache && cacheKey) {
              setCachedData((result === null || result === void 0 ? void 0 : result.payload) || result);
            }
            setLoading(false);
            return _context.abrupt("return", result);
          case 4:
            _context.prev = 4;
            _t = _context["catch"](2);
            setError(_t);
            setLoading(false);
            throw _t;
          case 5:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[2, 4]]);
    }));
    return function (_x) {
      return _ref2.apply(this, arguments);
    };
  }(), [action, dispatch, useCache, cacheKey, setCachedData]);

  // Get data with cache fallback
  var getData = (0,react.useCallback)(function () {
    // First try Redux store
    if (storeData !== undefined) {
      return storeData;
    }

    // Then try cache
    var cachedData = getCachedData();
    if (cachedData !== null) {
      return cachedData;
    }

    // Fall back to default value
    return defaultValue;
  }, [storeData, getCachedData, defaultValue]);

  // Refresh data by forcing a Redux update
  var refreshData = (0,react.useCallback)(/*#__PURE__*/(0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee2() {
    var result, _t2;
    return regenerator_default().wrap(function (_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          if (action) {
            _context2.next = 1;
            break;
          }
          return _context2.abrupt("return");
        case 1:
          setLoading(true);
          setError(null);
          _context2.prev = 2;
          _context2.next = 3;
          return dispatch(action());
        case 3:
          result = _context2.sent;
          // Update cache with new data
          if (useCache && cacheKey) {
            setCachedData((result === null || result === void 0 ? void 0 : result.payload) || result);
          }
          setLoading(false);
          return _context2.abrupt("return", result);
        case 4:
          _context2.prev = 4;
          _t2 = _context2["catch"](2);
          setError(_t2);
          setLoading(false);
          throw _t2;
        case 5:
        case "end":
          return _context2.stop();
      }
    }, _callee2, null, [[2, 4]]);
  })), [action, dispatch, useCache, cacheKey, setCachedData]);

  // Initialize cache on mount if needed
  (0,react.useEffect)(function () {
    if (useCache && cacheKey && storeData !== undefined && !getCachedData()) {
      setCachedData(storeData);
    }
  }, [useCache, cacheKey, storeData, getCachedData, setCachedData]);
  return {
    data: getData(),
    loading: loading,
    error: error,
    updateData: updateData,
    refreshData: refreshData,
    clearCache: clearCachedData
  };
};
/* harmony default export */ const hooks_useDataManager = (useDataManager);
// EXTERNAL MODULE: ./src/redux/minimal-store.js
var minimal_store = __webpack_require__(34816);
;// ./src/components/enhanced/DataManagementDemo.js



var _templateObject, _templateObject2, _templateObject3, _templateObject4, _templateObject5;








var Title = es/* Typography */.o5.Title,
  Text = es/* Typography */.o5.Text,
  Paragraph = es/* Typography */.o5.Paragraph;
var Option = es/* Select */.l6.Option;
var DemoContainer = styled_components_browser_esm/* default */.Ay.div(_templateObject || (_templateObject = (0,taggedTemplateLiteral/* default */.A)(["\n  padding: 20px;\n"])));
var DemoCard = (0,styled_components_browser_esm/* default */.Ay)(es/* Card */.Zp)(_templateObject2 || (_templateObject2 = (0,taggedTemplateLiteral/* default */.A)(["\n  margin-bottom: 20px;\n"])));
var FormGroup = styled_components_browser_esm/* default */.Ay.div(_templateObject3 || (_templateObject3 = (0,taggedTemplateLiteral/* default */.A)(["\n  margin-bottom: 16px;\n  \n  .label {\n    display: block;\n    margin-bottom: 8px;\n    font-weight: 500;\n  }\n"])));
var ButtonGroup = styled_components_browser_esm/* default */.Ay.div(_templateObject4 || (_templateObject4 = (0,taggedTemplateLiteral/* default */.A)(["\n  display: flex;\n  gap: 8px;\n  margin-top: 16px;\n"])));
var InfoBox = (0,styled_components_browser_esm/* default */.Ay)(es/* Alert */.Fc)(_templateObject5 || (_templateObject5 = (0,taggedTemplateLiteral/* default */.A)(["\n  margin-bottom: 16px;\n"])));

/**
 * DataManagementDemo component
 * Demonstrates the use of data management utilities
 */
var DataManagementDemo = function DataManagementDemo() {
  // Local state
  var _useState = (0,react.useState)(''),
    _useState2 = (0,slicedToArray/* default */.A)(_useState, 2),
    inputValue = _useState2[0],
    setInputValue = _useState2[1];
  var _useState3 = (0,react.useState)('temporary'),
    _useState4 = (0,slicedToArray/* default */.A)(_useState3, 2),
    cacheType = _useState4[0],
    setCacheType = _useState4[1];
  var _useState5 = (0,react.useState)(true),
    _useState6 = (0,slicedToArray/* default */.A)(_useState5, 2),
    useCache = _useState6[0],
    setUseCache = _useState6[1];

  // Use our custom hook to manage data
  var _useDataManager = hooks_useDataManager({
      selector: function selector(state) {
        var _state$ui;
        return (_state$ui = state.ui) === null || _state$ui === void 0 ? void 0 : _state$ui.currentView;
      },
      action: minimal_store.setCurrentView,
      cacheKey: 'current_view',
      cacheType: cacheType,
      useCache: useCache,
      defaultValue: 'components'
    }),
    currentView = _useDataManager.data,
    loading = _useDataManager.loading,
    error = _useDataManager.error,
    updateData = _useDataManager.updateData,
    refreshData = _useDataManager.refreshData,
    clearCache = _useDataManager.clearCache;

  // Handle saving data
  var handleSave = /*#__PURE__*/function () {
    var _ref = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee() {
      var _t;
      return regenerator_default().wrap(function (_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            if (inputValue.trim()) {
              _context.next = 1;
              break;
            }
            return _context.abrupt("return");
          case 1:
            _context.prev = 1;
            _context.next = 2;
            return updateData(inputValue);
          case 2:
            setInputValue('');
            _context.next = 4;
            break;
          case 3:
            _context.prev = 3;
            _t = _context["catch"](1);
            console.error('Error saving data:', _t);
          case 4:
          case "end":
            return _context.stop();
        }
      }, _callee, null, [[1, 3]]);
    }));
    return function handleSave() {
      return _ref.apply(this, arguments);
    };
  }();

  // Handle refreshing data
  var handleRefresh = /*#__PURE__*/function () {
    var _ref2 = (0,asyncToGenerator/* default */.A)(/*#__PURE__*/regenerator_default().mark(function _callee2() {
      var _t2;
      return regenerator_default().wrap(function (_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            _context2.prev = 0;
            _context2.next = 1;
            return refreshData();
          case 1:
            _context2.next = 3;
            break;
          case 2:
            _context2.prev = 2;
            _t2 = _context2["catch"](0);
            console.error('Error refreshing data:', _t2);
          case 3:
          case "end":
            return _context2.stop();
        }
      }, _callee2, null, [[0, 2]]);
    }));
    return function handleRefresh() {
      return _ref2.apply(this, arguments);
    };
  }();

  // Handle clearing cache
  var handleClearCache = function handleClearCache() {
    clearCache();
  };

  // Handle direct cache operations
  var handleSetDirectCache = function handleSetDirectCache() {
    if (!inputValue.trim()) return;
    dataManager.setCache('direct_cache_demo', inputValue, {
      type: cacheType,
      expiresIn: 3600000 // 1 hour
    });
    setInputValue('');
  };
  var handleGetDirectCache = function handleGetDirectCache() {
    var cachedValue = dataManager.getCache('direct_cache_demo', cacheType);
    setInputValue(cachedValue || '');
  };
  return /*#__PURE__*/react.createElement(DemoContainer, null, /*#__PURE__*/react.createElement(Title, {
    level: 3
  }, "Data Management Demo"), /*#__PURE__*/react.createElement(Paragraph, null, "This demo shows how to use the data management utilities to optimize Redux usage and handle data operations."), /*#__PURE__*/react.createElement(DemoCard, {
    title: "Current View Data"
  }, /*#__PURE__*/react.createElement(InfoBox, {
    type: "info",
    message: "Redux + Cache Integration",
    description: "This example shows how to use the useDataManager hook to manage data with Redux and caching.",
    icon: /*#__PURE__*/react.createElement(icons_es/* InfoCircleOutlined */.rUN, null)
  }), loading ? /*#__PURE__*/react.createElement("div", {
    style: {
      textAlign: 'center',
      padding: '20px'
    }
  }, /*#__PURE__*/react.createElement(es/* Spin */.tK, {
    size: "large"
  }), /*#__PURE__*/react.createElement("div", {
    style: {
      marginTop: '10px'
    }
  }, "Loading data...")) : /*#__PURE__*/react.createElement(react.Fragment, null, error && /*#__PURE__*/react.createElement(es/* Alert */.Fc, {
    type: "error",
    message: "Error",
    description: error.message || 'An error occurred while managing data',
    style: {
      marginBottom: '16px'
    }
  }), /*#__PURE__*/react.createElement(FormGroup, null, /*#__PURE__*/react.createElement("div", {
    className: "label"
  }, "Current View:"), /*#__PURE__*/react.createElement(Text, {
    strong: true
  }, currentView)), /*#__PURE__*/react.createElement(FormGroup, null, /*#__PURE__*/react.createElement("div", {
    className: "label"
  }, "New View:"), /*#__PURE__*/react.createElement(es/* Input */.pd, {
    value: inputValue,
    onChange: function onChange(e) {
      return setInputValue(e.target.value);
    },
    placeholder: "Enter a new view name"
  })), /*#__PURE__*/react.createElement(FormGroup, null, /*#__PURE__*/react.createElement("div", {
    className: "label"
  }, "Cache Type:"), /*#__PURE__*/react.createElement(es/* Select */.l6, {
    value: cacheType,
    onChange: setCacheType,
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react.createElement(Option, {
    value: "temporary"
  }, "Temporary (Memory only)"), /*#__PURE__*/react.createElement(Option, {
    value: "persistent"
  }, "Persistent (LocalStorage)"), /*#__PURE__*/react.createElement(Option, {
    value: "expiring"
  }, "Expiring (Time-based)"))), /*#__PURE__*/react.createElement(FormGroup, null, /*#__PURE__*/react.createElement("div", {
    className: "label"
  }, "Use Cache:"), /*#__PURE__*/react.createElement(es/* Switch */.dO, {
    checked: useCache,
    onChange: setUseCache
  })), /*#__PURE__*/react.createElement(ButtonGroup, null, /*#__PURE__*/react.createElement(es/* Button */.$n, {
    type: "primary",
    icon: /*#__PURE__*/react.createElement(icons_es/* SaveOutlined */.ylI, null),
    onClick: handleSave,
    disabled: !inputValue.trim()
  }, "Save to Redux"), /*#__PURE__*/react.createElement(es/* Button */.$n, {
    icon: /*#__PURE__*/react.createElement(icons_es/* ReloadOutlined */.KF4, null),
    onClick: handleRefresh
  }, "Refresh Data"), /*#__PURE__*/react.createElement(es/* Button */.$n, {
    danger: true,
    icon: /*#__PURE__*/react.createElement(icons_es/* ClearOutlined */.ohj, null),
    onClick: handleClearCache
  }, "Clear Cache")))), /*#__PURE__*/react.createElement(DemoCard, {
    title: "Direct Cache Operations"
  }, /*#__PURE__*/react.createElement(InfoBox, {
    type: "info",
    message: "Direct Cache API",
    description: "This example shows how to use the dataManager utility directly for caching operations.",
    icon: /*#__PURE__*/react.createElement(icons_es/* InfoCircleOutlined */.rUN, null)
  }), /*#__PURE__*/react.createElement(FormGroup, null, /*#__PURE__*/react.createElement("div", {
    className: "label"
  }, "Cache Value:"), /*#__PURE__*/react.createElement(es/* Input */.pd, {
    value: inputValue,
    onChange: function onChange(e) {
      return setInputValue(e.target.value);
    },
    placeholder: "Enter a value to cache"
  })), /*#__PURE__*/react.createElement(FormGroup, null, /*#__PURE__*/react.createElement("div", {
    className: "label"
  }, "Cache Type:"), /*#__PURE__*/react.createElement(es/* Select */.l6, {
    value: cacheType,
    onChange: setCacheType,
    style: {
      width: '100%'
    }
  }, /*#__PURE__*/react.createElement(Option, {
    value: "temporary"
  }, "Temporary (Memory only)"), /*#__PURE__*/react.createElement(Option, {
    value: "persistent"
  }, "Persistent (LocalStorage)"), /*#__PURE__*/react.createElement(Option, {
    value: "expiring"
  }, "Expiring (Time-based)"))), /*#__PURE__*/react.createElement(ButtonGroup, null, /*#__PURE__*/react.createElement(es/* Button */.$n, {
    type: "primary",
    onClick: handleSetDirectCache,
    disabled: !inputValue.trim()
  }, "Set Cache"), /*#__PURE__*/react.createElement(es/* Button */.$n, {
    onClick: handleGetDirectCache
  }, "Get Cache"), /*#__PURE__*/react.createElement(es/* Button */.$n, {
    danger: true,
    onClick: function onClick() {
      return dataManager.clearCache('direct_cache_demo', cacheType);
    }
  }, "Clear Cache"))));
};
/* harmony default export */ const enhanced_DataManagementDemo = (DataManagementDemo);

/***/ })

}]);