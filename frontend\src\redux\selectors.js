/**
 * Redux Selectors
 *
 * This file contains memoized selectors for the Redux store.
 * Using memoized selectors prevents unnecessary re-renders when the state hasn't changed.
 *
 * Performance benefits:
 * 1. Prevents unnecessary re-renders by returning the same reference when inputs haven't changed
 * 2. Enables component-specific optimizations by selecting only needed data
 * 3. Reduces computation by caching derived data
 */

import { createSelector } from 'reselect';

/**
 * Basic selectors
 * These selectors directly access state properties without transformation
 * They serve as input selectors for memoized selectors
 */
const getNetworkState = state => state.network || {};
const getUIState = state => state.ui || {};
const getAppState = state => state.app || {};
const getAppDataState = state => state.appData || {};
const getWebSocketState = state => state.websocket || {};
const getAPIKeysState = state => state.apiKeys || {};
const getErrorState = state => state.error || null;

/**
 * Memoized selectors
 * These selectors use createSelector to memoize results
 * They only recompute when their input selectors return new values
 */

// Network selectors
export const selectNetworkStatus = createSelector(
  [getNetworkState],
  (network) => ({
    isOnline: network.isOnline !== undefined ? network.isOnline : navigator.onLine,
    lastOnline: network.lastOnline || null,
    connectionType: network.connectionType || null,
    effectiveType: network.effectiveType || null,
    downlink: network.downlink || null,
    rtt: network.rtt || null
  })
);

export const selectAPIStatus = createSelector(
  [getNetworkState],
  (network) => network.apiStatus || { isAvailable: true, lastChecked: null, error: null }
);

// WebSocket selectors
export const selectWebSocketStatus = createSelector(
  [getWebSocketState],
  (websocket) => ({
    connected: websocket.connected || false,
    connecting: websocket.connecting || false,
    error: websocket.error || null
  })
);

export const selectWebSocketMessages = createSelector(
  [getWebSocketState],
  (websocket) => websocket.messages || []
);

export const selectLastWebSocketMessage = createSelector(
  [selectWebSocketMessages],
  (messages) => messages.length > 0 ? messages[messages.length - 1] : null
);

// UI selectors
export const selectUIState = createSelector(
  [getUIState],
  (ui) => ({
    sidebarOpen: ui.sidebarOpen !== undefined ? ui.sidebarOpen : true,
    currentView: ui.currentView || 'components',
    previewMode: ui.previewMode || false,
    loading: ui.loading || false
  })
);

export const selectCurrentView = createSelector(
  [getUIState],
  (ui) => ui.currentView || 'components'
);

export const selectPreviewMode = createSelector(
  [getUIState],
  (ui) => ui.previewMode || false
);

export const selectSidebarOpen = createSelector(
  [getUIState],
  (ui) => ui.sidebarOpen !== undefined ? ui.sidebarOpen : true
);

// Loading state selectors
export const selectAppLoading = createSelector(
  [getAppState, getUIState],
  (app, ui) => app.loading || ui.loading || false
);

export const selectAILoading = createSelector(
  [getAppState],
  (app) => app.aiLoading || false
);

// App data selectors
export const selectAppComponents = createSelector(
  [getAppDataState],
  (appData) => appData.components || []
);

export const selectAppLayouts = createSelector(
  [getAppDataState],
  (appData) => appData.layouts || []
);

export const selectAppStyles = createSelector(
  [getAppDataState],
  (appData) => appData.styles || {}
);

export const selectAppData = createSelector(
  [getAppDataState],
  (appData) => appData.data || {}
);

export const selectComponentById = (id) => createSelector(
  [selectAppComponents],
  (components) => components.find(component => component.id === id) || null
);

export const selectLayoutById = (id) => createSelector(
  [selectAppLayouts],
  (layouts) => layouts.find(layout => layout.id === id) || null
);

export const selectStyleBySelector = (selector) => createSelector(
  [selectAppStyles],
  (styles) => styles[selector] || {}
);

// API Keys selectors
export const selectAPIKeys = createSelector(
  [getAPIKeysState],
  (apiKeys) => apiKeys.keys || {}
);

export const selectAPIKeyValidationStatus = createSelector(
  [getAPIKeysState],
  (apiKeys) => apiKeys.validationStatus || {}
);

// Error selectors
export const selectGlobalError = createSelector(
  [getErrorState],
  (error) => error
);

/**
 * Combined selectors
 * These selectors combine multiple selectors to create derived data
 */
export const selectAppFullState = createSelector(
  [selectAppComponents, selectAppLayouts, selectAppStyles, selectAppData],
  (components, layouts, styles, data) => ({
    components,
    layouts,
    styles,
    data
  })
);

export const selectApplicationState = createSelector(
  [selectAppFullState, selectUIState, selectWebSocketStatus, selectNetworkStatus],
  (appData, ui, websocket, network) => ({
    appData,
    ui,
    websocket,
    network
  })
);

// Performance-related selectors
export const selectPerformanceMetrics = createSelector(
  [getAppState],
  (app) => app.performance || {
    renderCounts: {},
    actionDurations: [],
    memoryUsage: null,
    fps: null
  }
);

/**
 * UI-specific memoized selectors
 * These prevent unnecessary re-renders by maintaining reference equality
 */
export const selectUICurrentViewAndPreview = createSelector(
  [getUIState],
  (ui) => ({
    currentView: ui.currentView || 'components',
    previewMode: ui.previewMode || false
  })
);

export const selectUISidebarAndView = createSelector(
  [getUIState],
  (ui) => ({
    sidebarOpen: ui.sidebarOpen !== undefined ? ui.sidebarOpen : true,
    currentView: ui.currentView || 'components'
  })
);

/**
 * WebSocket-specific memoized selectors
 * These prevent object recreation on every render
 */
export const selectWebSocketConnectionData = createSelector(
  [getWebSocketState],
  (websocket) => ({
    connected: websocket.connected || false,
    messages: websocket.messages || [],
    url: websocket.url || null
  })
);

export const selectWebSocketFullState = createSelector(
  [getWebSocketState],
  (websocket) => ({
    connected: websocket.connected || false,
    connecting: websocket.connecting || false,
    error: websocket.error || null,
    messages: websocket.messages || [],
    url: websocket.url || null,
    status: websocket.status || 'disconnected'
  })
);

/**
 * App data memoized selectors for components that need multiple state slices
 */
export const selectAppComponentsAndLayouts = createSelector(
  [getAppState, getAppDataState],
  (app, appData) => ({
    components: app.components || appData.components || [],
    layouts: app.layouts || appData.layouts || []
  })
);

export const selectAppBuilderState = createSelector(
  [getAppState, getAppDataState],
  (app, appData) => ({
    components: app.components || appData.components || [],
    layouts: app.layouts || appData.layouts || [],
    styles: app.styles || appData.styles || {},
    data: app.data || appData.data || {}
  })
);

/**
 * AI-specific selectors
 */
export const selectAIState = createSelector(
  [getAppState],
  (app) => ({
    aiSuggestions: app.aiSuggestions || [],
    aiLoading: app.aiLoading || false,
    aiError: app.aiError || null,
    generatedImage: app.generatedImage || null
  })
);
